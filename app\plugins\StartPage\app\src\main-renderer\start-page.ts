import TinyLogger from '@xunlei/tiny-logger';
import Vue, { VNode, CreateElement } from 'vue';
import { ISearchNavs } from '@/main-renderer/common/data-define';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';

// @ts-ignore
import load from '@xunlei/thunder-ui-vue/lib/directives/load';
Vue.use(load)

const logger: TinyLogger = TinyLogger.getLogger('start-page-entry');

import App from './start-page.vue';

// const parentVue = document.querySelector('').__vue__
// const vnode = parentVue.$createElement({ tempalte: '', methods: ''});
// const el = parentVue.refs[key];
// parentVue.__patch__(el, node);
async function getData(): Promise<{ searchNavs: ISearchNavs }> {
  const defaultValue: ISearchNavs = {
    notshow_days: 30,
    news_show: true,
  };
  let searchNavsConfig: ISearchNavs = await clientModule.callServerFunction('GetRemoteGlobalConfigValue', 'home', 'start_page', { ...defaultValue });
  let showNews: boolean = await clientModule.callServerFunction('GetRemoteGlobalConfigValue', 'main', 'url_update_switch_startpage', true);
  // 确保部分值一定存在
  if (!searchNavsConfig?.notshow_days) {
    searchNavsConfig.notshow_days = 7;
  }

  searchNavsConfig.news_show = showNews;

  if (!searchNavsConfig?.nocollect_default?.text || !searchNavsConfig?.nocollect_default?.url) {
    const searchUrl: string = 'https://www.baidu.com/s?tn=44004473_85_oem_dg&wd=MP4';
    
    if (!searchNavsConfig.nocollect_default) {
      searchNavsConfig.nocollect_default = {
        text: '搜搜看',
        url: searchUrl
      }
    } else {
      searchNavsConfig.nocollect_default.text = '搜搜看';
      searchNavsConfig.nocollect_default.url = searchUrl;
    }
  }

  return { searchNavs: searchNavsConfig };
}

async function startPageMount(): Promise<void> {
  logger.information('enter');
  do {
    const slot = document.getElementById('start-empty');
    if (!slot) {
      logger.information('get element failed');
      break;
    }
    let switchConfig: { start_page?: boolean } = await clientModule.callServerFunction('GetRemoteGlobalConfigValueAw',
      'home', 'home_switchs', {});
    // switchConfig = { start_page: true, ...(switchConfig ? switchConfig : {}) };
    if (!switchConfig?.start_page) {
      logger.information('config switch off');
      break;
    }

    // 注入样式
    const head: HTMLHeadElement = (document.getElementsByTagName('head'))?.[0];
    if (head) {
      const link = document.createElement('link');
      link.href = `${__dirname}/index.css`;
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.onload = async () => {
        // 数据获取
        const options = await getData();

        // 挂载组件
        new Vue({
          components: {
            App
          },
          data: {
            ...options
          },
          render(createElement: CreateElement): VNode {
            return createElement('app');
          }
        }).$mount('#start-empty');
      };
      head.appendChild(link);
    } else {
      logger.warning('append css failed');
    }
  } while (0);
}

startPageMount().catch();
