'use strict';

const path = require('path');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const CircularDependencyPlugin = require('circular-dependency-plugin');

const isBuildProduction = process.env.BUILD_ENV === 'production';
const isDeveloping = process.env.RUN_ENV === 'development';

const packageJSON = require('../package.json');
const binDir = path.resolve(packageJSON.build.binDir);
const binName = packageJSON.build.binName;
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(path.resolve(packageJSON.build.outDir), buildTarget, 'TaskCenter', packageJSON.version);

let mainConfig = {
  // for webpack v4
  mode: isBuildProduction ? 'production' : 'development',
  entry: {
    main: [
      path.join(__dirname, '../src/main/global.ts'),
      path.join(__dirname, '../src/common/tiny-logger.ts'),
      path.join(__dirname, '../src/main/index.ts')
    ]
  },
  module: {
    rules: [{
        enforce: 'pre',
        test: /\.js$/,
        include: path.resolve(__dirname, '../src'),
        loader: 'source-map-loader'
      },
      {
        test: /\.ts$/,
        loader: 'ts-loader',
        include: path.resolve(__dirname, '../src'),
        exclude: /node_modules/,
        options: {
          transpileOnly: true,
          experimentalWatchApi: true
        }
      }
    ]
  },
  output: {
    filename: '[name].js',
    libraryTarget: 'commonjs2',
    path: outDir
  },
  node: false,
  plugins: [
    new CircularDependencyPlugin({
      // exclude detection of files based on a RegExp
      exclude: /node_modules/,
      // add errors to webpack instead of warnings
      failOnError: true,
      // allow import cycles that include an asyncronous import,
      // e.g. via import(/* webpackMode: "weak" */ './file.js')
      allowAsyncCycles: false,
      // set the current working directory for displaying module paths
      cwd: process.cwd()
    })
  ],
  resolve: {
    extensions: ['.js', '.json', '.ts'],
    symlinks: false,
    alias: {
      '@': path.join(__dirname, '../src')
    }
  },
  target: 'electron-main',
  stats: 'errors-only',
  bail: true,
  optimization: {
    removeAvailableModules: false,
    removeEmptyChunks: false,
    splitChunks: false
  },
  devtool: 'source-map',
  optimization: {
    removeAvailableModules: false,
    removeEmptyChunks: false,
    splitChunks: false,
    ...(isBuildProduction ? {
      minimizer: [
        new UglifyJsPlugin({
          sourceMap: buildTarget === 'Release' || buildTarget === 'ProductRelease',
          cache: true,
          parallel: true,
          uglifyOptions: {
            ecma: 6,
            compress: {
              warnings: false
            }
          }
        })
      ]
    } : {
      minimize: false
    })
  }
};

mainConfig.plugins.push(
  new CopyWebpackPlugin([{
      from: path.join(__dirname, '../package.json'),
      to: path.join(outDir, '../package.json')
    },
    {
      from: path.join(__dirname, '../static'),
      to: path.join(outDir, '../static')
    }
  ])
);

if (!isBuildProduction) {
  mainConfig.plugins.push(
    new CopyWebpackPlugin([{
      from: path.join(__dirname, '../node_modules/devtron'),
      to: path.join(outDir, '../node_modules/devtron')
    }])
  );
}

module.exports = mainConfig;
