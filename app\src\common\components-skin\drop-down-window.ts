const merge: any = require('lodash.merge');

export namespace DropDownWindowSkinNS {
  export async function setStyle(dropDownWindow: any, opts: any): Promise<void> {
    if (dropDownWindow !== null) {
      // const { WindowPreferenceNS } = await import('./window-preference'); // tslint:disable-line
      // const skinOpts: any = { windowPreference: WindowPreferenceNS.getWindowPreference() };
      const skinOpts: any = { windowPreference: {} };
      dropDownWindow.setStyle(merge(skinOpts, opts));
    }
  }
}
