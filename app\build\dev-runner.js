'use strict';

const chalk = require('chalk');
const os = require('os');
const electron = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const webpack = require('webpack');
const WebpackDevServer = require('webpack-dev-server');
const webpackHotMiddleware = require('webpack-hot-middleware');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const getJobs = require('./get-devjobs');
const packageJSON = require('../package.json');
const binDir = path.resolve(packageJSON.build.binDir);
const binName = packageJSON.build.binName;
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(binDir, buildTarget, '/resources/app/out');
const config = require('./config');
const jobs = getJobs(config);

let electronProcess = null;
let manualRestart = false;
let hotMiddleware;

require('module').globalPaths.push(path.resolve(__dirname, '../node_modules'));

init(jobs);

function init(jobs) {
  Promise.all(
    jobs.map(({ name, config }) => {
      let packPromise;
      if (name === 'renderer') {
        packPromise = packRenderer(config, name);
      } else {
        packPromise = packOther(config, name);
      }
      return packPromise;
    })
  )
    .then(() => {
      console.log('success');
      startElectron();
    })
    .catch((err) => {
      throw err;
    });
}

function packRenderer(config, name) {
  let port = 9080;
  return new Promise((resolve, reject) => {
    // config.entry['main-renderer/renderer'] = [path.join(__dirname, 'dev-client')].concat(
    //   config.entry['main-renderer/renderer']
    // );

    // 为每个入口添加热更新
    Object.keys(config.entry).forEach(key  => {
      config.entry[key] = [path.join(__dirname, 'dev-client')].concat(
        config.entry[key]
      );
    })


    const compiler = webpack(config);
    hotMiddleware = webpackHotMiddleware(compiler, {
      log: false,
      heartbeat: 2500
    });

    compiler.hooks.compilation.tap('DevRunnerPlugin', (compilation) => {
      logStats(name, chalk.white.bold('compiling...'));
      HtmlWebpackPlugin.getHooks(compilation).afterEmit.tapAsync('DevRunnerPlugin', (data, cb) => {
        hotMiddleware.publish({ action: 'reload' });

        // if (electronProcess && electronProcess.kill) {
        //   manualRestart = true;
        //   process.kill(electronProcess.pid);
        //   electronProcess = null;
        //   cb(null, data);
        //   startElectron();

        //   setTimeout(() => {
        //     manualRestart = false;
        //   }, 5000);
        // } else {
          cb(null, data);
        // }
      });
    });

    compiler.hooks.failed.tap('DevRunnerPlugin', (err) => {
      throw err;
    });

    compiler.hooks.done.tap('DevRunnerPlugin', (stats) => {
      logStats(name, stats);
      console.log(`Start ${name} on port: ${port}`);
    });

    const server = new WebpackDevServer(compiler, {
      contentBase: outDir,
      quiet: true,
      hot: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
      },
      before(app, ctx) {
        app.use(hotMiddleware);
        ctx.middleware.waitUntilValid(() => {
          resolve();
        });
      }
    });

    server.listen(port);
  });
}

function packOther(config, name) {
  return new Promise((resolve, reject) => {
    if (name === 'main') {
      config.entry.main = [path.join(__dirname, '../src/main/index.dev.js'), ...config.entry.main];
    }

    const compiler = webpack(config);

    compiler.hooks.watchRun.tapAsync('DevRunnerPlugin', (compiler, done) => {
      logStats('Main', chalk.white.bold('compiling...'));
      hotMiddleware.publish({ action: 'compiling' });
      done(null, compiler);
    });

    compiler.watch({}, (err, stats) => {
      if (err) {
        throw err;
      }

      logStats(name, stats);

      if (electronProcess && electronProcess.kill) {
        manualRestart = true;
        process.kill(electronProcess.pid);
        electronProcess = null;
        resolve(null, compiler);
        startElectron();

        setTimeout(() => {
          manualRestart = false;
        }, 5000);
      } else {
        resolve(null, compiler);
      }
    });
  });
}

function startElectron() {
  let command = os.platform() === 'win32' ? path.join(binDir, buildTarget, binName) : electron;
  const isAsar = process.env.IS_ASAR;

  electronProcess = spawn(command, ['--inspect=5858', path.join(`${outDir}${isAsar ? '.asar' : ''}`, 'main.js')]);

  electronProcess.stdout.on('data', (data) => {
    electronLog(data, 'blue');
  });
  electronProcess.stderr.on('data', (data) => {
    electronLog(data, 'red');
  });

  electronProcess.on('close', () => {
    if (!manualRestart) process.exit();
  });
}

function logStats(proc, data) {
  let log = proc + '\n';

  if (typeof data === 'object') {
    data
      .toString({ chunks: false, colors: true, modules: false })
      .split(/\r?\n/)
      .forEach((line) => {
        log += `    ${line}\n`;
      });
  } else {
    log += `  ${data}\n`;
  }

  console.log(log);
}

function electronLog(data, color) {
  let log = '';
  data = data.toString().split(/\r?\n/);
  data.forEach((line) => {
    log += `  ${line}\n`;
  });
  if (/[0-9A-z]+/.test(log)) {
    console.log(
      chalk[color].bold('┏ Electron -------------------') +
        '\n\n' +
        log +
        chalk[color].bold('┗ ----------------------------') +
        '\n'
    );
  }
}
