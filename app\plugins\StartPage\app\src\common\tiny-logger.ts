import { join, dirname, basename } from 'path';
import { inspect } from 'util';
import <PERSON><PERSON>ogger, { LogLevel, IOutputLogger, outputLoggerConsole } from '@xunlei/tiny-logger';
import requireNodeFile from './require-node-file';
import { DevEnvHelperNS } from './dev-env-helper';
import { ipc<PERSON><PERSON>, ipc<PERSON><PERSON><PERSON>, BrowserWindow } from 'electron';
import { ThunderChannelList } from './channel';
import * as fs from 'fs';

const thunderHelper: any = requireNodeFile(join(__rootDir, '../bin/ThunderHelper.node'));
let logDir: string = join(dirname(process.execPath), 'log');
const loggerFilename: string = join(logDir, `${basename(process.execPath)}-${(new Date()).getTime()}-${process.pid}.log`);

// webpack运行时会根据mode设置一个全局变量process.env.NODE_ENV
process.env.TL_ENABLE = process.env.NODE_ENV === 'production' ? '0' : '1';
TinyLogger.enableLogger = process.env.TL_ENABLE === '1';

function setOutputLogger(): void {
  if (process.env.TL_OUTPUT === 'console') {
    TinyLogger.outputLogger = outputLoggerConsole;
  } else if (process.env.TL_OUTPUT === 'file') {
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir);
    }
    TinyLogger.outputLogger = getFileOutputLogger();
  } else {
    TinyLogger.outputLogger = getETWOutputLogger();
  }
}

function updateLogOutputEnv(): void {
  const enable: boolean = DevEnvHelperNS.isLogEnable();
  if ((process.env.TL_ENABLE === '1') !== enable) {
    process.env.TL_ENABLE = enable ? '1' : '0';
    TinyLogger.enableLogger = enable;
    thunderHelper.enableETWLogger(enable);
  }
  const output: string = DevEnvHelperNS.getLogOutput();
  if (output && output !== process.env.TL_OUTPUT) {
    process.env.TL_OUTPUT = output;
    setOutputLogger();
  }
}

function initLogOutputEnv(): void {
  setOutputLogger();
  updateLogOutputEnv();
}
initLogOutputEnv();

function getETWOutputLogger(): IOutputLogger {
  function stringify(...messages: any[]): string {
    return messages
      .map((message: any): string => inspect(message))
      .join(' ')
      .replace(/%/g, '%%');
  }

  function transformToFileLog(logLevel: LogLevel): (...messages: any[]) => void {
    return function(...messages: any[]): void {
      thunderHelper.printEtwLog(logLevel, stringify(...messages));
    };
  }

  return {
    [LogLevel.Critical]: transformToFileLog(LogLevel.Critical),
    [LogLevel.Error]: transformToFileLog(LogLevel.Error),
    [LogLevel.Warning]: transformToFileLog(LogLevel.Warning),
    [LogLevel.Information]: transformToFileLog(LogLevel.Information),
    [LogLevel.Verbose]: transformToFileLog(LogLevel.Verbose)
  };
}

function getFileOutputLogger(): IOutputLogger {
  function stringify(...messages: any[]): string {
    return messages
      .map((message: any): string => inspect(message))
      .join(' ')
      .replace(/%/g, '%%');
  }

  function transformToEtwLog(logLevel: LogLevel): (...messages: any[]) => void {
    return function(...messages: any[]): void {
      fs.appendFileSync(loggerFilename, `${new Date()}  ${logLevel} ${stringify(...messages)} \r\n`);
    };
  }

  return {
    [LogLevel.Critical]: transformToEtwLog(LogLevel.Critical),
    [LogLevel.Error]: transformToEtwLog(LogLevel.Error),
    [LogLevel.Warning]: transformToEtwLog(LogLevel.Warning),
    [LogLevel.Information]: transformToEtwLog(LogLevel.Information),
    [LogLevel.Verbose]: transformToEtwLog(LogLevel.Verbose)
  };
}

if (process.type === 'browser') {
  ipcMain.on(ThunderChannelList.channelRMUpdateLogEnviroment, () => {
    const browserWindows: BrowserWindow[] = BrowserWindow.getAllWindows();
    browserWindows.forEach((browserWindow: BrowserWindow) => {
      if (!browserWindow.isDestroyed()) {
        browserWindow.webContents.send(ThunderChannelList.channelMRUpdateLogEnviroment);
      }
    });
    updateLogOutputEnv();
  });
} else if (process.type === 'renderer') {
  ipcRenderer.on(ThunderChannelList.channelMRUpdateLogEnviroment, () => {
    updateLogOutputEnv();
  });
}
