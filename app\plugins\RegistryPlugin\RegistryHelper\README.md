# 描述

此插件为c++读取注册表，
插件名：RegistryHelper，
非重定向win32接口：readRegString
readRegString参数示例："HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam China\\DisplayIcon"

# 插件构建
参考c++插件编译构建教程：[https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8azbzKBjTdQvzXEBWgN7R35y?iframeQuery=utm_source%3Dportal%26utm_medium%3Dportal_recent&rnd=0.6918641898748628](https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8azbzKBjTdQvzXEBWgN7R35y?iframeQuery=utm_source%3Dportal%26utm_medium%3Dportal_recent&rnd=0.6918641898748628)

# 签名打包
将编译生成的RegistryHelper.node插件签名，并放到RegistryHelper/Release目录下：
![''](./doc/image/1.png)