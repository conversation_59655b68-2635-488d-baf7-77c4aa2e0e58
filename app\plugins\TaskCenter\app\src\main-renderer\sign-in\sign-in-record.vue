<template>
  <ul class="xly-sign-in__list">
    <li
      v-for="item in week"
      class="xly-sign-in__item"
      :key="item.index"
      :class="{ 'is-current': item.isCurrent }"
    >
      <div
        class="xly-sign-in__status"
        :class="{
          'is-current': item.isCurrent,
          'is-disabled': !item.isSigned && item.index < todayWeek && !reCheckInCount,
          'is-missed': !item.isSigned && item.index < todayWeek && reCheckInCount,
          'is-signed': item.isSigned
        }"
        @click="handleItemClick(item)"
      >
        <img
          v-if="item.isSigned || reCheckInCount || item.index >= todayWeek"
          class="xly-sign-in__card"
          :src="getIcon(item.index, item)"
        />
        <!-- 已签到 -->
        <div v-if="item.isSigned" class="xly-sign-in__tips">{{ item.isReSign ? '已补签' : '已签到' }}</div>
        <!-- 未签到 -->
        <template v-else>
          <!-- 大于等于当前日期 -->
          <template v-if="item.index >= todayWeek">
            <!-- 奖品名称 -->
            <div  class="xly-sign-in__tips">{{ item.name }}<template v-if="item.isShowDouble">*2</template></div>
            <p>签</p>
          </template>
          <!-- 小于当前日期 -->
          <template v-else>
            <!-- 有补签次数 -->
            <td-button v-if="reCheckInCount">补签</td-button>
            <!-- 没有补签次数 -->
            <p v-else>未签</p>
            <!-- 没有补签次数，提示 -->
            <div class="xly-sign-in__tips">
              {{ reCheckInCount > 0 ? `剩余可补签 ${reCheckInCount} 次` : '本周补签次数已用完' }}
            </div>
          </template>
        </template>

        <!-- 翻倍提示 -->
        <!-- 补签的日期且没有补签次数，不显示翻倍 -->
        <template v-if="item.index < todayWeek && !reCheckInCount"></template>
        <div v-else-if="item.isShowDouble" class="xly-sign-in__tag">翻倍</div>
      </div>

      <p>{{ item.isCurrent ? '今天' : item.text }}</p>
    </li>
  </ul>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import * as Dayjs from 'dayjs'

const signed = require('@/assets/img/sign-in/signed.svg')
const WEEK_TEXT = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

@Component
export default class SignInRecord extends Vue {
  @Prop() todayWeek!: number                // 当前时间的星期几
  @Prop() reSignedIndex!: number[]          // 已补签的日期列表
  @Prop() enableRemind!: boolean            // 是否开启签到提醒
  @Prop() enableRemindDate!: number         // 开启签到提醒的时间
  @Prop() alreadyCheckInList!: number[]     // 已签到的日期列表
  @Prop() reCheckInCount!: number           // 可以补签的次数
  @Prop() dailyAwardList!: any[]            // 每日奖品

  get week () {
    const now = Dayjs()   // 当前时间
    const weekStartDate = now.startOf('w').add(1, 'd')    // 本周的周一

    return this.dailyAwardList.map(award => {
      const date = weekStartDate.add(award.day_index - 1, 'd')              // 实际的时间
      const isCurrent = award.day_index === this.todayWeek                  // 是否是今天
      const isSigned = this.alreadyCheckInList.includes(award.day_index)    // 是否已签到
      const isReSign = this.reSignedIndex.includes(award.day_index)         // 是否是补签的
      const isShowDouble = !isSigned && this.getIsShowDouble(date)          // 是否显示翻倍

      return {
        ...award,
        index: award.day_index,
        name: award.name,
        icon: award.icon,
        text: WEEK_TEXT[award.day_index - 1],
        date,
        isCurrent,
        isSigned,
        isReSign,
        isShowDouble,
      }
    })
  }

  getIsShowDouble (date: Dayjs.Dayjs | Date | number) {
    // 开启提醒，并且当前时间大于开启提醒的时间，则显示【翻倍】标识
    return Dayjs(date).isAfter(Dayjs(this.enableRemindDate), 'day') && this.enableRemind
  }

  getIcon (weekDay: number, item: any) {
    return this.alreadyCheckInList.includes(weekDay) ? signed : item.icon
  }

  handleItemClick (item: any) {
    // 当前点击的条目时间小于当前时间，且有补签次数，且未签到的
    // 点击后触发补签
    if (item.index < this.todayWeek && this.reCheckInCount && !item.isSigned) {
      this.$emit('reCheckIn', item.index, item)
    }
  }
}
</script>