/**
 * Single Promise: 注意这个是指异步过程只产生一个Promise对象，
 */
export class SinglePromise {
  private _fnPromiseMap: Map<string, Promise<any>> = new Map(); // tslint:disable-line

  /**
   * Run single promise.
   * @param {string} key
   * @param {Function} fn
   * @return {Promise<T>}
   */
  async run<T>(key: string, fn: () => Promise<T>): Promise<T> {
    let result: Promise<T> = this._fnPromiseMap.get(key) as Promise<T>;
    if (!result) {
      result = new Promise<T>(async (resolve: (val: any) => void, reject: (reason: any) => void): Promise<void> => {
        try {
          // The idle promise must be run to prevent _fnPromiseMap from
          // storing the current promise function.
          await this._runIdlePromise();
          const fnResult: Promise<T> = fn();
          resolve(await fnResult);
        } catch (error) {
          reject(error);
        } finally {
          this._fnPromiseMap.delete(key);
        }
      });
      this._fnPromiseMap.set(key, result);
    }
    return result;
  }

  /**
   * Run idle promise.
   * @return {Promise<void>}
   */
  private _runIdlePromise(): Promise<void> { // tslint:disable-line
    return Promise.resolve();
  }
}
