'use strict';

const path = require('path');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const packageJSON = require('./config.json');
const CopyWebpackPlugin = require('copy-webpack-plugin');
// const webpack = require('webpack');

const binDir = path.resolve(__dirname, './bin/');
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(binDir, buildTarget, `/${packageJSON.name}`);
const outVerDir = path.join(outDir, `/${packageJSON.version}`);

const isBuildProduction = process.env.BUILD_ENV === 'production';
console.log('outVerDir', outVerDir)
let mainConfig = {
  // for webpack v4
  mode: isBuildProduction ? 'production' : 'development',
  target: 'electron-main',
  entry: {
    main: [
      path.join(__dirname, './src/index.ts')
    ]
  },
  output: {
    filename: 'index.js',
    libraryTarget: 'commonjs2',
    path: outVerDir
  },
  module: {
    rules: [{
      enforce: 'pre',
      test: /\.js$/,
      loader: 'source-map-loader'
    },
    {
      test: /\.ts$/,
      loader: 'ts-loader',
      exclude: /node_modules/
    }
    ]
  },
  node: false,
  plugins: [],
  devtool: isBuildProduction ? 'source-map' : 'cheap-module-eval-source-map',
  resolve: {
    extensions: ['.ts', '.js']
  },
  optimization: {
    removeAvailableModules: false,
    removeEmptyChunks: false,
    ...(isBuildProduction ? {
      minimizer: [
        new UglifyJsPlugin({
          sourceMap: buildTarget === 'Release',
          cache: true,
          parallel: true,
          uglifyOptions: {
            ecma: 6,
            compress: {
              warnings: false
            }
          }
        })
      ]
    } : {
      minimize: false
    })
  }
};

mainConfig.plugins.push(
  new CopyWebpackPlugin([{
    from: path.join(__dirname, './config.json'),
    to: path.join(outDir, './config.json')
  }, {
    from: path.join(__dirname, './xl_band_evaluate.dll'),
    to: path.join(outVerDir, './Bin/xl_band_evaluate.dll')
  }])
);

module.exports = mainConfig;