<template>
  <div class="xly-sign-in-panel">
    <div class="xly-sign-in-panel__header">
      <span @click="$emit('back')">
        <i class="xly-icon-arrow-left"></i>
        返回
      </span>
      <h3>奖品列表</h3>
    </div>

    <template v-if="isFetchingList"></template>

    <template v-else>
      <div v-if="!list.length" class="xly-empty">
        <div class="xly-img-message"></div>
        <p>暂无奖品~</p>
      </div>

      <div v-else class="xly-sign-in-panel__items">
        <ul>
          <li v-for="(item, index) in list" :key="index" class="xly-sign-in-panel__item">
            <div class="xly-sign-in-panel__card">
              <img :src="item.icon" :alt="item.asset_name" />
            </div>

            <div class="xly-sign-in-panel__content">
              <!-- 奖品名称 -->
              <h4>{{ item.asset_name }}</h4>
              <!-- 获得（领取）时间 -->
              <p class="xly-sign-in-panel__time">{{ item.time }}</p>
              <!-- 直接发放到账户 或 未使用的展示过期时间 -->
              <template v-if="item.isDistribute || !item.isUsed">
                <!-- 未过期 -->
                <p v-if="item.noteDay > 1" class="is-note">距离过期还剩{{ item.noteDay }}天</p>
                <p v-else-if="item.warnHour > 0" class="is-warn">距离过期还剩{{ item.warnHour }}小时</p>
                <p v-else-if="item.warnMinute > 0" class="is-warn">距离过期还剩{{ item.warnMinute }}分钟</p>
                <!-- 已过期 -->
                <p v-else class="is-note">已过期</p>
              </template>
            </div>
            <!-- 直接发放到账户 -->
            <p v-if="item.isDistribute" class="xly-sign-in-panel__text">已发放到帐户</p>
            <!-- 需要用户手动兑换 -->
            <template v-else>
              <!-- 过期了：未使用 -->
              <p v-if="item.isExpired && !item.isUsed" class="xly-sign-in-panel__text">未使用</p>
              <!-- 未过期 -->
              <td-button v-else :class="{ 'is-disabled': item.isUsed }" @click="handleUse(item)">
                {{ item.isUsed ? '已使用' : '使用' }}
              </td-button>
            </template>
          </li>
        </ul>
        <p class="xly-sign-in-panel__note">到底了~</p>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { showMessageToast } from '@/common/ipc'
import { SIGN_IN_AWARD } from '@/common/reward-map'
import * as SignInApi from '@/api/sign-in'
import * as Dayjs from 'dayjs'

@Component
export default class AwardList extends Vue {
  list: any[] = []
  isRequest: boolean = false
  isFetchingList: boolean = false

  async getAwardList (resetLoading: boolean = true) {
    // 是否重置 loading 状态
    if (resetLoading) {
      this.isFetchingList = true
    }

    const res = await SignInApi.getAwardList()

    if (res.data) {
      this.list = res.data.data.map(asset => {
        const now = Dayjs()
        // 是否已使用
        const isUsed = asset.is_used === 1
        // 本地的映射
        const localAsset = SIGN_IN_AWARD[asset.asset_id]
        // 获取时间，服务端返回秒，需要乘以 1000
        const receiveTime = Dayjs(asset.receive_unix * 1000)
        // 获取奖品的时间
        const time = receiveTime.format('YYYY-MM-DD HH:mm:ss')
        // 过期时间
        const expireTime = Dayjs(asset.expire_time * 1000)
        // 是否已过期（当前时间大于过期时间）
        const isExpired = +now > +expireTime
        // 距离过期剩 x 天
        const noteDay = expireTime.diff(now, 'day')
        // 距离过期剩 x 小时
        const warnHour = expireTime.diff(now, 'hour')
        // 距离过期剩 x 分钟
        const warnMinute = expireTime.diff(now, 'minute')

        return {
          ...asset,
          ...localAsset,
          time,
          isUsed,
          isExpired,
          noteDay,
          warnHour,
          warnMinute,
        }
      })
    } else {
      showMessageToast({ message: '操作失败，请稍后重试', type: 'error' })
    }
    this.isFetchingList = false
  }

  async handleUse (item: any) {
    if (item.isUsed) return
    if (this.isRequest) return
    this.isRequest = true

    this.$eventTrack('signin_task_reward_use_click', { type: item.trackType })
    // 加速试用先调用 IPC，加速成功后再调服务端接口，更新记录
    if (item.trackType === 'peedup_try') {
      const [ used ] = await client.callRemoteClientFunction('vip-download-webview', 'ClickSignInTry')
      // 未使用成功，相关的提示由 vip-download 提供
      this.isRequest = false
      if (!used) {
        this.$eventTrack('signin_task_reward_use_result', {
          result: 'fail',
          type: item.trackType
        })
        return
      }
    }

    const res = await SignInApi.useAward({
      id: item.id,
      game_id: item.game_id,
      cost_asset_id: item.cost_asset_id,
      get_asset_id: item.get_asset_id,
    })

    this.$eventTrack('signin_task_reward_use_result', {
      result: res.data ? 'success' : 'fail',
      type: item.trackType
    })

    if (res.data) {
      // 加速试用是展示 toast，相关的提示由 vip-download 提供
      if (item.trackType !== 'peedup_try') {
        showMessageToast({ message: '已成功兑换', type: 'success' })
      }
      // 刷新一下列表数据
      this.getAwardList(false)
    } else {
      showMessageToast({ message: '操作失败，请稍后重试', type: 'error' })
    }
    this.isRequest = false
  }

  created () {
    this.getAwardList()
    this.$eventTrack('signin_task_reward_list_show')
  }
}
</script>