import path = require('path');
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { Logger } from './logger';
import { programPath } from './path-utilities';
import { existsAW, mkdirAW } from './fs-utilities';
import { zip } from 'compressing';

const logger: Logger = Logger.getLogger('Flash');
const contextName: string = 'Flash'; // 这个名字需要和config.json 一致，由于客户端写死了，所以我们不能修改这个
const context: { name: string } = { name: contextName };

client.start(context, 'thunder');

async function run(): Promise<void> {
  do {
    logger.information('run');
    // let url: string = 'https://down.sandai.net/PepperFlash.zip';
    let dllPath: string = path.join(programPath, 'resources/bin/TBC//PepperFlash/pepflashplayer.dll');
    let manifestPath: string = path.join(programPath, 'resources/bin/TBC//PepperFlash/manifest.json');
    let cfgPath: string = path.join(programPath, 'resources/bin/TBC//PepperFlash/flash_allow_list.cfg');
    logger.information('dllPath', dllPath, 'manifestPath', manifestPath);
    let dllExist: boolean = await existsAW(dllPath).catch();
    let festExist: boolean = await existsAW(manifestPath).catch();
    let cfgExist: boolean = await existsAW(cfgPath).catch();
    if (dllExist && festExist && cfgExist) {
      break;
    }
    logger.information('dllPath', dllPath, 'manifestPath', manifestPath, 'dllExist', dllExist, 'festExist', festExist, 'cfgPath', cfgPath, 'cfgExist', cfgExist);
    let zipPath: string = `${__dirname}/../PepperFlash.zip`;
    let unzipPath: string = path.join(programPath, 'resources/bin/TBC/PepperFlash');
    if (!await existsAW(zipPath)) {
      await mkdirAW(zipPath);
    }
    logger.information('zipPath', zipPath, unzipPath);
    if (await existsAW(zipPath)) {
      await zip.uncompress(zipPath, unzipPath).catch();
    }
  } while (0);
  process.exit();
}

run().then(() => {
  // process.exit();
}).catch();
