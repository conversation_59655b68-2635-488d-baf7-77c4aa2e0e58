<template>
  <td-select
    :class="{
      'is-checked': checked
    }"
    v-bind="$attrs"
    v-on="$listeners"
    :value="value"
    custom-menu-enabled
    ref="select"
    @menu-show="handleMenuShow"
    @mousedown.native="isMouseup = false"
    @mouseup.native="isMouseup = true"
  >
    <slot slot="prefix" name="prefix"></slot>
    <slot slot="suffix" name="suffix"></slot>
    <slot slot="append" name="append"></slot>
  </td-select>
</template>

<script lang="ts">
import { ipcRenderer } from 'electron';
import { asyncRemoteCall } from '@/common/renderer-process-call';
import { Vue, Component, Prop } from 'vue-property-decorator';
import { XLStatNS } from '@/common/xlstat';
import { HistoryPathsNS } from '@/common/history-path';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';

@Component
export default class SelectWindow extends Vue {
  isMouseup: boolean = true;
  /** 绑定值 */
  @Prop() value: string;

  @Prop() panel: string;

  @Prop()
  panelSource: string;

  @Prop()
  dropOwner: string;

  @Prop()
  checked: string;

  @Prop({
    default: (): string[] => []
  })
  appendDirs: string[];

  @Prop({
    default: true
  })
  enablePrivateSpace: boolean;

  @Prop({
    default: true
  })
  enableDelete: boolean;

  async created(): Promise<void> {
    let selfWindow: any = await asyncRemoteCall.getCurrentWindow();

    if (await selfWindow.isVisible()) {
      ipcRenderer.send('dropdown-file-window-create', selfWindow.id, this.panel, this.panelSource);
    } else {
      setTimeout(() => {
        ipcRenderer.send('dropdown-file-window-create', selfWindow.id, this.panel, this.panelSource);
      }, 20);
    }

    ipcRenderer.on('dropdown-file-window-blur', () => {
      if (this.isMouseup) {
        this.hideMenu();
      }
    });
  }

  /** 处理下拉按钮点击 */
  async handleMenuShow(): Promise<void> {
    XLStatNS.trackEvent('core_event', 'create_task_panel_downloadpath_show');
    let rect: ClientRect = this.$el.getBoundingClientRect();
    let selfWindow: any = await asyncRemoteCall.getCurrentWindow();

    let options: HistoryPathsNS.IPathMenuItem[] = [];
    if (this.dropOwner === 'cloud') {
      let ret: any[] = await clientModule.callRemoteClientFunction(PanClientName, 'IpcGetRecentFolder');
      let cloudDirs: { name: string, id: string }[] = ret[0];
      if (cloudDirs) {
        for (let item of cloudDirs) {
          if (item.id !== '') {
            options.push({ dir: item.name, alias: item.name, canDelete: true, id: item.id });
          }
        }
        options = options.slice(0, 4);
      }

      options.splice(0, 0, { dir: '我的云盘', alias: '我的云盘', canDelete: false, id: '' });
    } else {
      options = await HistoryPathsNS.getLogicHistoryPaths(this.enablePrivateSpace, this.enableDelete, this.appendDirs);
    }

    // 这里直接 JSON.stringify 返回{} ,
    ipcRenderer.send(
      'dropdown-file-window-show',
      selfWindow.id,
      true,
      JSON.stringify({
        bottom: rect.bottom,
        height: rect.height,
        left: rect.left,
        right: rect.right,
        top: rect.top,
        width: rect.width
      }),
      options,
      this.dropOwner,
      this.enableDelete
    );
  }

  hideMenu(): void {
    if (this.$refs.select) {
      (this.$refs.select as any).menuVisible = false;
    }
  }

  focusEdit(): void {
    if (this.$refs.select) {
      let input: HTMLInputElement = ((this.$refs.select as Vue).$el as Element).querySelector('input');
      if (input !== null && input !== undefined) {
        input.focus();
      }
    }
  }
}
</script>
