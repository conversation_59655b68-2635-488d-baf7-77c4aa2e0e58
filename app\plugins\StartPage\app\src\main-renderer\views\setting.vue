<template>
  <!-- 设置 start -->
  <div class="xly-favorites-setting" @mouseleave="showSettingMenu=false">
    <div class="xly-favorites-setting__button" @click.stop="onClickSetting">
      <i class="xly-icon-more"></i>
    </div>
    <div class="xly-favorites-setting__drop" v-show="showSettingMenu">
      <h4 class="xly-favorites-setting__title">常用网站</h4>
      <ul class="xly-favorites-setting__list">
        <li class="xly-favorites-setting__item" @click="setShowSites"> <i v-if="sitesOn" class="xly-icon-menu-chosen"></i> 显示</li>
        <li class="xly-favorites-setting__item" @click="setSitesNodisturbed"> <i v-if="!sitesOn" class="xly-icon-menu-chosen"></i> 近期不显示</li>
      </ul>
      <!-- <h4 class="xly-favorites-setting__title">搜索发现</h4>
      <ul class="xly-favorites-setting__list">
        <li class="xly-favorites-setting__item" @click="setShowHotword"> <i v-if="hotwordShow" class="xly-icon-menu-chosen"></i> 显示</li>
        <li class="xly-favorites-setting__item" @click="setHotwordNodisturbed"> <i v-if="!hotwordShow" class="xly-icon-menu-chosen"></i> 近期不显示</li>
      </ul>
      <div class="xly-favorites-setting__line" v-if="showNews"></div>
      <h4 class="xly-favorites-setting__title" v-if="showNews">网站更新动态</h4>
      <ul class="xly-favorites-setting__list" v-if="showNews">
        <li class="xly-favorites-setting__item" @click="setShowSubscribe"> <i v-if="subscribeShow" class="xly-icon-menu-chosen"></i> 显示</li>
        <li class="xly-favorites-setting__item" @click="setSubscribeNodisturbed"> <i v-if="!subscribeShow" class="xly-icon-menu-chosen"></i> 近期不显示</li>
      </ul> -->
    </div>
  </div>
  <!-- 设置 end -->
</template>

<script lang="ts">
import TinyLogger from '@xunlei/tiny-logger';
import { Vue, Component, Prop } from 'vue-property-decorator';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';

const logger: TinyLogger = TinyLogger.getLogger('start-page-root');

const startPageSection = 'StartPage';
const showSitesKey = 'ShowSites';
const sitesNodisturbedKey = 'SitesDndTime';
const showSubscribeKey = 'ShowSubscribe';
const subscribeNodisturbedKey = 'SubscribeDndTime';
const showHotwordKey = 'ShowHotword';
const hotwordNodisturbedKey = 'HotwordDndTime';

/**
 * 获得新的一天 开始时对应的Date，新增该接口以解决两个Date自然天数差；
 */
function getDayStartDate(): Date {
  let today: Date = new Date();
  let dayStart: Date = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);

  return dayStart;
}

@Component({
  components: {
  }
})
export default class Setting extends Vue {
  @Prop({})
  showNews: boolean;

  @Prop({})
  sitesOn: boolean;

  @Prop()
  hotwordShow: boolean;

  @Prop({})
  subscribeShow: boolean;

  @Prop({})
  nodisturbDays: number;

  showSettingMenu: boolean = false;

  async getSwitchConfigs(): Promise<void> {
    type ConfigValueType = string | number | boolean | string[] | number[];
    clientModule.attachServerEvent('OnConfigValueChanged', (context: any, section: string, key: string, preValue: ConfigValueType, newValue: ConfigValueType) => {
      do {
        if (section !== startPageSection) {
          break;
        }

        if (key === showSitesKey) {
          this.$emit('setting-change', 'sites', newValue as boolean);
        }

        if (key === showSubscribeKey) {
          this.$emit('setting-change', 'subscribe', newValue as boolean)
        }

        if (key === showHotwordKey) {
          this.$emit('setting-change', 'hotword', newValue as boolean)
        }
      } while (0);
    });
    await clientModule.callServerFunction('WaitForConfigInitFinish');
    const showSites: boolean = await clientModule.callServerFunction('GetConfigValue', startPageSection, showSitesKey, true);
    const showHotword: boolean = await clientModule.callServerFunction('GetConfigValue', startPageSection, showHotwordKey, true);
    let sitesNoDisturbed: boolean = false;
    let hotwordNoDisturbed: boolean = false;

    if (!showSites) {
      sitesNoDisturbed = await this.isNodisturbed(sitesNodisturbedKey, this.nodisturbDays);
      if (!sitesNoDisturbed) {
        // 又开始展示了，则清空这个字段
        logger.information('sites no disturb out of data');
        await this.setShowSites(false);
      }
    }

    if (!showHotword) {
      hotwordNoDisturbed = await this.isNodisturbed(hotwordNodisturbedKey, this.nodisturbDays);
      if (!hotwordNoDisturbed) {
        logger.information('hotword no disturb out of data');
        await this.setShowHotword(false);
      }
    }

    if (this.showNews) {
      const showSubscribe: boolean = await clientModule.callServerFunction('GetConfigValue', startPageSection, showSubscribeKey, true);
      let subscribeNoDisturbed: boolean = false;
      if (!showSubscribe) {
        subscribeNoDisturbed = await this.isNodisturbed(subscribeNodisturbedKey, this.nodisturbDays);
        if (!subscribeNoDisturbed) {
          logger.information('subscribe no disturb out of data');
          await this.setShowSubscribe(false);
        }
      }
      
      this.$emit('init-setting', showSites || !sitesNoDisturbed, showSubscribe || !subscribeNoDisturbed, showHotword || !hotwordNoDisturbed);
    } else {
      this.$emit('init-setting', showSites || !sitesNoDisturbed, false, showHotword || !hotwordNoDisturbed);
    }
  }

  async setSitesNodisturbed(): Promise<void> {
    this.showSettingMenu = false;
    if (this.sitesOn) {
      await clientModule.callServerFunction('SetConfigValue', startPageSection, showSitesKey, false);
    }
    await clientModule.callServerFunction('SetConfigValue', startPageSection, sitesNodisturbedKey, getDayStartDate().getTime());
    await clientModule.callServerFunction('SaveConfig');
  }

  async setSubscribeNodisturbed(): Promise<void> {
    this.showSettingMenu = false;
    if (this.subscribeShow) {
      await clientModule.callServerFunction('SetConfigValue', startPageSection, showSubscribeKey, false);
    }
    await clientModule.callServerFunction('SetConfigValue', startPageSection, subscribeNodisturbedKey, getDayStartDate().getTime());
    await clientModule.callServerFunction('SaveConfig');
  }

  async setHotwordNodisturbed(): Promise<void> {
    this.showSettingMenu = false;
    if (this.hotwordShow) {
      await clientModule.callServerFunction('SetConfigValue', startPageSection, showHotwordKey, false);
    }
    await clientModule.callServerFunction('SetConfigValue', startPageSection, hotwordNodisturbedKey, getDayStartDate().getTime());
    await clientModule.callServerFunction('SaveConfig');
  }

  async setShowSites(isClick: boolean = true): Promise<void> {
    this.showSettingMenu = false;
    await clientModule.callServerFunction('SetConfigValue', startPageSection, showSitesKey, true);
    await clientModule.callServerFunction('SetConfigValue', startPageSection, sitesNodisturbedKey, 0);
    await clientModule.callServerFunction('SaveConfig');
  }

  async setShowSubscribe(isClick: boolean = true): Promise<void> {
    this.showSettingMenu = false;
    await clientModule.callServerFunction('SetConfigValue', startPageSection, showSubscribeKey, true);
    await clientModule.callServerFunction('SetConfigValue', startPageSection, subscribeNodisturbedKey, 0);
    await clientModule.callServerFunction('SaveConfig');
  }

  async setShowHotword(isClick: boolean = true): Promise<void> {
    this.showSettingMenu = false;
    await clientModule.callServerFunction('SetConfigValue', startPageSection, showHotwordKey, true);
    await clientModule.callServerFunction('SetConfigValue', startPageSection, hotwordNodisturbedKey, 0);
    await clientModule.callServerFunction('SaveConfig');
  }

  async isNodisturbed(key: typeof sitesNodisturbedKey | typeof subscribeNodisturbedKey | typeof hotwordNodisturbedKey, days: number = 7): Promise<boolean> {
    let ret: boolean = false;
    do {
      let begin: number = await clientModule.callServerFunction('GetConfigValue', startPageSection, key, 0);
      if (!begin || isNaN(begin)) {
        // 不存在配置则认为需要展示：关闭免打扰
        break;
      }

      if (days === -1) {
        // 配置的永久免打扰
        ret = true;
        break;
      }

      if (!days || isNaN(days)) {
        days = 7;
      }

      let todayBegin: number = getDayStartDate().getTime();

      if (begin > todayBegin) {
        break;
      }

      logger.information('is nodisturbed', todayBegin - begin, (days - 1) * 24 * 60 * 60 * 1000);

      if (todayBegin - begin < (days - 1) * 24 * 60 * 60 * 1000) {
        ret = true;
      }
    } while (0);

    return ret;
  }

  onClickSetting(): void {
    this.showSettingMenu = !this.showSettingMenu;
  }

  created(): void {
    this.getSwitchConfigs();
  }
}
</script>