const merge = require('webpack-merge');

module.exports = (config) => {
  const rendererTasks = config.filter((task) => task.type === 'renderer');
  const otherTasks = config.filter((task) => task.type !== 'renderer');

  const jobs = [
    {
      name: 'renderer',
      config: merge(
        require(`./webpack.renderer-common.config.js`),
        ...rendererTasks.map((task) => require(`./webpack.${task.name}.config.js`))
      )
    },
    ...otherTasks.map((task) => ({
      name: task.name,
      config: require(`./webpack.${task.name}.config.js`)
    }))
  ];
  return jobs;
};