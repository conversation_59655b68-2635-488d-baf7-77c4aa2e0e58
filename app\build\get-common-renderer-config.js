const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const packageJSON = require('../package.json');
const binDir = path.resolve(packageJSON.build.binDir);
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(binDir, buildTarget, '/resources/app/out');

const isBuildProduction = process.env.BUILD_ENV === 'production';
const isDeveloping = process.env.RUN_ENV === 'development';

module.exports = (rendererName) => {
  const rendererSrcPath = `../src/${rendererName}`;

  return {
    entry: {
      [`${rendererName}/renderer`]: [
        // 添加常量文件
        path.join(__dirname, '../src/common/constants.ts'),
        path.join(__dirname, '../src/common/tiny-logger.ts'),
        path.join(__dirname, rendererSrcPath, 'main.ts')
      ]
    },
    plugins: [
      new HtmlWebpackPlugin({
        filename: `${rendererName}/index.html`,
        template: path.resolve(
          __dirname,
          rendererSrcPath,
          rendererName === 'main-renderer' && process.env.RUN_ENV === 'development' ? 'index-dev.ejs' : 'index.ejs'
        ),
        chunks: [`${rendererName}/renderer`],
        minify: isBuildProduction
          ? {
              collapseWhitespace: true,
              removeAttributeQuotes: true,
              removeComments: true
            }
          : false,
        isDeveloping: isDeveloping,
        inject: rendererName === 'main-renderer' && process.env.RUN_ENV !== 'development' ? false : true
      })
    ]
  };
};
