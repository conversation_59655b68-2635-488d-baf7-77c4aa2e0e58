import { cryptoData, doRequest } from "."
import * as Dayjs from 'dayjs'

export interface IAsset {
  amount: number
  cost_amount: number
  cost_asset: IAsset
  ext: string
  get_amount_limit: number
  id: number
  left_amount: number
  name: string
  status: number
  total_amount: number
  type: string
  unit: string
  value: number
}

export interface SignInConfig {
  push_time: string               // 签到提醒时间
  ordinary_assets: {              // 普通签到的奖励
    asset_id: number              // 资产id
    asset_amount: number           // 资产数量
    day_index: number             // 签到日期
  }[]
  continue_sign_in_assets: {      // 连续签到的奖励
    asset_id: number
    asset_amount: number
  }[]
}

export interface GetInfoResponse {
  end_unix: number                // 活动结束时间
  game: {                         // 游戏数据
    game_id: number               // 游戏 id
    start_unix: number            // 游戏开始时间
    end_unix: number              // 游戏结束时间
    status: number                // 游戏状态    1=未参与    2=进行中    3=结束
    all_asset: IAsset[]           // 所有资产
    assets: IAsset[]              // 已获得资产
  }
  name: string                    // 活动名称
  start_unix: number              // 活动开始时间
  status: number                  // 状态  1=未开始  2=进行中  3=活动结束
  sign_in_config: SignInConfig    // 签到配置信息
}

export interface CreateGameResponse {
  game_id: number                 // 游戏 id
  start_unix: number              // 游戏开始时间
  end_unix: number                // 游戏结束时间
  status: number                  // 游戏状态  1=未参与  2=进行中  3=结束
  activity_id: number             // 活动 id
  init_game_get_assets: {
    id: number                    // 创建活动获得资产id
    amount: number                // 创建活动获得资产amount
    type: string                  // 类型
  }[]
}

export interface SignInResponse {
  get_asset_ids: number[]
}

export interface Award {
  id: string
  game_id: number                 // 游戏 id
  asset_id: number
  asset_name: string              // 资产名
  asset_unit: string
  asset_value: number
  receive_unix: number            // 获取时间，单位是秒
  cost_asset: {                   // 消耗的资产
    id: number                    // 资产 id
    name: string                  // 资产名称
  }
  ext: {                          // 扩展信息
    asset_code: string            // 资产兑换码
  }
  next_id_token: string           // 偏移id，带入下一次获取
  is_used: number                 // 兑换状态：0 - 未兑换，1 - 已兑换
  expire_time: number             // 过期时间，单位是秒
}

export interface GetAwardListResponse {
  data: Award[]
  agg: {
    amount: number                // 如果请求里的agg不为空 这里返回聚合的数量 例如agg传pan 则这里返回用户获取的总容量数
    unit: string                  // 单位  例如 GB
  }
  next_id_token: string
}

export interface GetRecordResponse {
  remaining_retroactive_sign_in_count: number       // 剩余的补签次数
  sign_in_records: {
    day_index: number                               // 第几天的签到
    has_sign_in: boolean                            // 是否有签到 目前全部返回true
    sign_in_type: string                            // 签到类型 normal:正常签到  retroactive:补签
  }[]
}

export interface UseAwardResponse {
  asset_id: number                  // 资产 id
  asset_name: string                // 资产名称
  asset_expire_unix: number         // 资产过期时间，单位：秒
  asset_type: string                // vip  svip  pan
  asset_ext: string                 // 格式为 json, quantity: 获取的数量
}

// 签到的活动 id（固定的）
const SIGN_IN_ACTIVITY_ID = 18888

/**
 * 获取活动信息
 */
export async function getInfo () {
  let uri = `/activity-v2/info?source=pc&type=sign_in&activity_id=${SIGN_IN_ACTIVITY_ID}`

  return await doRequest<GetInfoResponse>(uri, 'get')
}

/**
 * 创建活动游戏
 * 如果 getInfo 接口中返回的 gameId 为 0，则需要调用此接口创建游戏
 */
export async function createGame () {
  let uri = `/activity-v2/game_create?source=pc`

  return await doRequest<CreateGameResponse>(uri, 'post', {
    activity_id: SIGN_IN_ACTIVITY_ID,
  })
}

/**
 * 获取签到记录
 * @param game_id 用户参加活动的id
 */
export async function getRecord (game_id: number) {
  let uri = `/activity-v2/sign-in/records?source=pc&activity_id=${SIGN_IN_ACTIVITY_ID}&game_id=${game_id}`

  return await doRequest<GetRecordResponse>(uri, 'get')
}

/**
 * 签到
 * @param sign_in_type 签到类型 normal:正常签到  retroactive:补签
 * @param day_index 签到第几天，数字 1-7
 * @param game_id 用户参加活动的id
 */
export async function signIn (
  sign_in_type: 'normal' | 'retroactive',
  day_index: number,
  game_id: number,
  isDouble: boolean
) {
  let uri = '/activity-v2/sign-in?source=pc'
  // 翻倍标识，非翻倍时，用 d0ub1e 迷惑一下
  const content = cryptoData({
    extra: {
      type: isDouble ? 'double' : 'd0ub1e'
    }
  })

  return await doRequest<SignInResponse>(uri, 'post', {
    activity_id: SIGN_IN_ACTIVITY_ID,
    sign_in_type,
    day_index,
    game_id,
    content
  })
}

/**
 * 获取已获得的奖品列表
 * @param game_id 传入 game_id 则仅返回与 game_id 相关的奖品（game_id 每周会重置），所以需要下面两个字段来返回指定时间内的数据
 * @param start_time 过滤时间范围的开始时间（单位秒）
 * @param end_time 过滤时间范围的结束时间（单位秒）
 */
export async function getAwardList () {
  let uri = `/activity-v2/asset/list?source=pc&activity_id=${SIGN_IN_ACTIVITY_ID}`
  uri += '&type=ordinary_sign_in_reward,ordinary_sign_in_double_reward,continue_sign_in_reward'
  // 默认过滤时间范围，两周（本周与上周）
  const now = Dayjs()
  const end_time = now.endOf('w').add(1, 'd')
  const start_time = now.startOf('w').add(1, 'd').subtract(1, 'w')
  // 转化成秒
  const end_s = Math.floor(+end_time / 1000)
  const start_s = Math.floor(+start_time / 1000)

  uri += `&start_time=${start_s}`
  uri += `&end_time=${end_s}`

  return await doRequest<GetAwardListResponse>(uri, 'get')
}

/**
 * 使用（兑换）奖品
 * @param game_id 用户参加活动的id
 * @param get_asset_id
 * @param cost_asset_id
 */
export async function useAward ({
  id,
  game_id,
  cost_asset_id,
  get_asset_id,
} :{
  id: string
  game_id: number
  cost_asset_id: number
  get_asset_id: number
}) {
  let uri = `/activity-v2/asset?source=pc`

  return await doRequest<UseAwardResponse>(uri, 'post', {
    activity_id: SIGN_IN_ACTIVITY_ID,
    id,
    game_id,
    get_asset_id,
    cost_asset_id,
  })
}
