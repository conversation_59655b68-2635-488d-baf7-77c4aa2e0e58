# 依赖安装

1. 切换npm 镜像"npm set registry https://registry.npmmirror.com/"，执行npm install,安装官方依赖
2. 切换npm 镜像"npm set registry http://xnpm.repo.xunlei.cn/"，执行npm install, 安装迅雷依赖

# RegistryHelper c++插件编译
注册表插件包含RegistryHelper c++ 插件，c++插件编译请参考./RegistryHelper/README.MD

# RegistryPlugin 编译生成

```
# 打包发布release 版本
npm run pr
```

# RegistryPlugin 打包
将asar文件、config.json 文件、.asar.unpacked文件一起压缩成"RegistryPlugin.zip"文件
![''](./doc/image/1.png)

# 自测插件
require('D:/Code/thunder11/thunder11/app/plugins/RegistryPlugin/bin/Release/RegistryPlugin/1.0.4.asar/index.js')