<template>
  <!-- 无收藏，则不论是否登录，都显示空空如也 -->
  <div class="xly-favorites-dynamic" v-if="isLogined !== null && filterFavorities && filterFavorities.length === 0">
    <FavoritesDynamicHeader />
    <div class="xly-favorites-dynamic__empty">
      <i class="xly-img-fav"></i>
      <h4>收藏夹空空如也</h4>
      <p>检测到内容更新，将及时推送给您</p>
      <button class="td-button" @click="clickFindSites">{{ noCollectOptions.text }}</button>
    </div>
  </div>

  <!-- 有收藏数据，未登录 ，引导登录同步 -->
  <div class="xly-favorites-dynamic" v-else-if="filterFavorities && filterFavorities.length > 0 && isLogined === false">
    <FavoritesDynamicHeader />
    <div class="xly-favorites-dynamic__empty">
      <i class="xly-img-fav"></i>
      <h4>登录即可获取网站更新</h4>
      <button class="td-button" @click="clickLogin">立即登录</button>
    </div>
  </div>

  <!-- 有收藏数据，登录 ，未开启同步 -->
  <div class="xly-favorites-dynamic" v-else-if="isLogined && favoritesUpdateTurnOn === false">
    <FavoritesDynamicHeader />
    <div class="xly-favorites-dynamic__empty">
      <i class="xly-img-dynamic"></i>
      <h4>开启收藏网站更新</h4>
      <p>第一时间获取收藏动态</p>
      <button class="td-button" @click="clickFollowOver">一键开启</button>
    </div>
  </div>

  <!-- 有收藏数据，登录 ，已开启同步，无更新数据 -->
  <div class="xly-favorites-dynamic" v-else-if="isLogined && favoritesUpdateTurnOn && dynamics && dynamics.length === 0">
    <FavoritesDynamicHeader />
    <div class="xly-favorites-dynamic__empty">
      <i class="xly-img-find"></i>
      <h4>收藏网站暂无更新</h4>
      <p>检测到内容更新，将及时推送给您</p>
    </div>
  </div>

  <!-- 有收藏数据，登录 ，已开启同步， 有更新数据 -->
  <FavoritesDynamicList
    v-else-if="isLogined && favoritesUpdateTurnOn && dynamics && dynamics.length > 0"
    :dynamics="loadedDynamics"
    :area="area"
    >
    <FavoritesDynamicHeader slot="header">
      <span @click="onClickAll">查看全部 &gt;</span>
    </FavoritesDynamicHeader>

    <template slot="end" >
      <div v-show="!hasMore" class="xly-favorites-dynamic__end">
        到底啦，没有更多了
      </div>
    </template>
  </FavoritesDynamicList>
</template>

<script lang="ts">
import TinyLogger from '@xunlei/tiny-logger';
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
import FavoritesDynamicList from '@/main-renderer/views/favorites-dynamic/dynamic-list.vue';
import FavoritesDynamicHeader from '@/main-renderer/views/dynamic-header.vue'
import { IbookMarksItem, NewsItemEx, NewsItem } from '@/main-renderer/views/favorites-dynamic/data-options';
const logger: TinyLogger = TinyLogger.getLogger('favorites-dynamic');

const pageSize: number = 20;
let delayShowExtData: string = null;

@Component({
  components: {
    FavoritesDynamicList,
    FavoritesDynamicHeader
  },
  inheritAttrs: false
})
export default class FavoritesDynamic extends Vue {
  @Prop()
  visible: boolean;

  @Prop()
  settingInited: boolean;

  @Prop()
  subscribeShow: boolean;

  @Prop()
  isLogined: boolean;

  @Prop()
  noCollectOptions: { text: string; url: string; }

  dynamics: NewsItemEx[] = null;

  // 分页获取，每次请求返回记录
  nextPageToken: string = '';

  favorities: IbookMarksItem = null;

  favoritesUpdateTurnOn: boolean = null;

  // 根据url 作为key渲染
  filterFavorities: string[] = null;

  // 懒加载
  loadedIndex: number = 0;

  area: string = 'start_page';

  helpTitle: string = '动态来源于发生了信息变动的收藏网址';
  headerTitle: string = '收藏网站更新';

  get loadedDynamics(): NewsItemEx[] {
    if (this.dynamics?.length > 0) {
      let endIndex: number = this.loadedIndex + pageSize;
      if (endIndex > this.dynamics.length) {
        endIndex = this.dynamics.length;
      }

      return this.dynamics.slice(0, endIndex);
    } else {
      return [];
    }
  }

  get hasMore(): boolean {
    if (this.nextPageToken) {
      return true;
    }

    if (this.loadedDynamics.length < this.dynamics?.length) {
      return true;
    }

    return false;
  }

  @Watch('visible')
  onStartPageShow(): void {
    if (this.visible) {
      if (delayShowExtData) {
        const att1 = this.area + '_bookurl_dynamic_content_show';
        logger.information('next show time', att1, delayShowExtData);
        clientModule.callServerFunction('TrackEvent', 'core_event', att1, '', 0, 0, 0, 0, delayShowExtData).catch();
        delayShowExtData = null;
      }
    }
  }

  async getMoreNews(tickcount: number): Promise<NewsItemEx[]> {
    let more: NewsItemEx[] = [];
    do {
      const oldUserId: string = await clientModule.callServerFunction('GetUserID');
      if (!oldUserId) {
        break;
      }

      const results: { succ: boolean, next_page_token?: string, items?: NewsItem[]} = await clientModule.callServerFunction('GetFavoritesAllNews', pageSize, this.nextPageToken);
      const nowUserId: string = await clientModule.callServerFunction('GetUserID');
      if (oldUserId !== nowUserId) {
        break;
      }
      if (results?.succ) {
        this.nextPageToken = results?.next_page_token ?? '';
        more = (results?.items ?? []).map((item: NewsItem) => {
          return {
            tickcount,
            ...item
          }
        });
      }
    } while (0);
    return more;
  }

  reportShow(tickcount: number, items: NewsItem[], pos: number, showreason: 'more' | 'update'): void {
    do {
      if (!tickcount || !items?.length) {
        break;
      }

      // host-链接host， url_updatetime-当天内容最近更新的时间，拉取服务端该内容更新的时间， id-服务端返回的内容id， rn-位置（从上到下从1开始编号） 同一元素不同属性之间逗号分隔，不同元素之间分号分隔。
      // 上报格式： { host=xx1，url_updatetime=xx, id=xx, rn=1； host=xx1，url_updatetime=xx, id=xx, rn=2； host=xx1，url_updatetime=xx, id=xx, rn=3 }
      let content: string = '';
      for (let index: number = 0; index < items.length; index++) {
        const item = items[index];
        content += `host=${encodeURIComponent(item?.url ?? '')},url_updatetime=${item?.create_time ?? ''},id=${item?.id ?? ''},rn=${pos + index + 1};`;
      }
      const extData = 'sessionid=' + (tickcount ?? '')
        + ',contentlist=' + encodeURIComponent(content);

      if (this.visible) {
        const att1 = this.area + '_bookurl_dynamic_content_show';
        logger.information(att1, tickcount, content);
        clientModule.callServerFunction('TrackEvent', 'core_event', att1, '', 0, 0, 0, 0, extData).catch();
      } else {
        // 记录最近一次的数据改动
        delayShowExtData = extData;
      }
    } while (0);
  }

  isLoadingMore: boolean = false;
  async loadMore(root: HTMLElement): Promise<void> {
    if (this.dynamics?.length > 0) {
      logger.information('startpage loadMore enter', this.isLoadingMore);
      if (!this.isLoadingMore) {
        this.isLoadingMore = true;
        const now = new Date().getTime();
        if (this.loadedIndex + pageSize <= this.dynamics.length) {
          this.loadedIndex += pageSize;

          if (this.nextPageToken) {
            const more = await this.getMoreNews(now);
            if (more?.length > 0) {
              this.reportShow(now, more, this.dynamics.length, 'more');
              this.dynamics.push(...more);
            }
          }
        } else {
          if (this.nextPageToken) {
            const more = await this.getMoreNews(now);
            if (more?.length > 0) {
              this.reportShow(now, more, this.dynamics.length, 'more');
              this.dynamics.push(...more);
              if (this.loadedIndex + pageSize <= this.dynamics.length) {
                this.loadedIndex += pageSize;
              } else {
                this.loadedIndex = this.dynamics.length;
              }
            }
          } else {
            this.loadedIndex = this.dynamics.length;
          }
        }
        this.isLoadingMore = false;

        do {
        if (!root) {
          break;
        }

        if (!this.hasMore) {
          break;
        }

        if (Math.ceil(root.clientHeight + root.scrollTop) < root.scrollHeight) {
          break;
        }

        if (getComputedStyle(root).overflowY === 'scroll') {
          logger.information('starpage loadMore scrollEnd but hasMore');
          this.loadMore(root);
        }
      } while (0);
      }
    }
  }

  async mounted(): Promise<void> {
    logger.information('mounted');
    // 前置监听，如果自动登陆中会在登录完成发这个事件更新
    clientModule.attachServerEvent('OnFavoritesSynced', async () => {
      this.favorities = Object.freeze(await clientModule.callServerFunction('GetFavorities'));
      this.filterFavorities = Object.keys(this.favorities ?? {});
      this.favoritesUpdateTurnOn = await clientModule.callServerFunction('AtLeastOneFollowed') ?? false;
    });

    if (this.isLogined === null) {
      const logined = await clientModule.callServerFunction('IsLogined');
      if (!logined) {
        this.favorities = Object.freeze(await clientModule.callServerFunction('GetFavorities'));
        this.filterFavorities = Object.keys(this.favorities ?? {});
        this.favoritesUpdateTurnOn = false;
      } else {
        // 执行 OnFavoritesSynced 
      }
    } else {
      this.favorities = Object.freeze(await clientModule.callServerFunction('GetFavorities'));
      this.filterFavorities = Object.keys(this.favorities ?? {});
      if (this.isLogined) {
        this.favoritesUpdateTurnOn = await clientModule.callServerFunction('AtLeastOneFollowed') ?? false;
      }
    }

    clientModule.attachServerEvent('OnUpdateFavorites', async () => {
      this.favorities = Object.freeze(await clientModule.callServerFunction('GetFavorities'));
      this.filterFavorities = Object.keys(this.favorities ?? {});
    });

    clientModule.attachServerEvent('OnFavoritesFollowChange', async () => {
      this.favorities = Object.freeze(await clientModule.callServerFunction('GetFavorities'));
      this.filterFavorities = Object.keys(this.favorities ?? {});
    });

    this.$watch(() => {
      const { isLogined, favoritesUpdateTurnOn } = this;
      return { isLogined, favoritesUpdateTurnOn };
    }, async () => {
      if (this.isLogined && this.favoritesUpdateTurnOn) {
        const results: { succ: boolean, next_page_token?: string, items?: NewsItem[]} = await clientModule.callServerFunction('GetFavoritesAllNews', pageSize);
        if (results?.succ) {
          this.nextPageToken = results?.next_page_token ?? '';
          const now = new Date().getTime();
          this.reportShow(now, results?.items, 0, 'update');
          this.dynamics = (results?.items ?? []).map((item: NewsItem) => {
            return {
              tickcount: now,
              ...item
            }
          });
        }
      } else {
        if (!this.isLogined) {
          this.favoritesUpdateTurnOn = null;
        }
        
        this.dynamics = null;
        this.nextPageToken = '';
        this.loadedIndex = 0;
      }
    }, { immediate: true });

    clientModule.attachServerEvent('OnFavoritesFollowChange', async () => {
      do {
        if (await clientModule.callServerFunction('AtLeastOneFollowed')) {
          if (!this.favoritesUpdateTurnOn) {
            this.favoritesUpdateTurnOn = true;
            // watch里会重新刷新
            break;
          }
        } else {
          if (this.favoritesUpdateTurnOn) {
            this.favoritesUpdateTurnOn = false;
            // watch里会重新刷新
            break;
          }
        }

        const results: { succ: boolean, next_page_token?: string, items?: NewsItem[]} = await clientModule.callServerFunction('GetFavoritesAllNews', pageSize);
        if (results?.succ) {
          this.nextPageToken = results?.next_page_token ?? '';
          const now = new Date().getTime();
          this.reportShow(now, results?.items, 0, 'update');
          this.dynamics = (results?.items ?? []).map((item: NewsItem) => {
            return {
              tickcount: now,
              ...item
            }
          });
        }
      } while (0);
    });
  }

  async onClickAll(): Promise<void> {
    this.doClickStat('all');
    await clientModule.callServerFunction('OpenNewTab', 'https://sl-m-ssl.xunlei.com/entry/attention/list', JSON.stringify({ extData: JSON.stringify({ search_from: 'collect_dynamic-bookurl' })}));
  }

  clickLogin(): void {
    this.doClickStat('login');
    const from: string = 'start_page';
    clientModule.callServerFunction('ShowLoginDlg', from).catch();
  }

  async clickFindSites(): Promise<void> {
    this.doClickStat('find');
    if (this.noCollectOptions?.text && this.noCollectOptions?.url) {
      let searchUrl: string = this.noCollectOptions?.url;
      await clientModule.callServerFunction('OpenNewTab', searchUrl, JSON.stringify({ extData: JSON.stringify({ search_from: 'null_collect_url_button' })}));
    }
  }

  async clickFollowOver(): Promise<void> {
    this.doClickStat('open');
    clientModule.callServerFunction('TrackEvent', 'download_detail', 'turn_on_all_collect_url_update', '', 0, 0, 0, 0, 'from=start_page').catch;
    await clientModule.callServerFunction('FavoritesFollowOver');
  }

  doClickStat(clickId: string) {
    clientModule.callServerFunction('TrackEvent', 'download_detail', 'right_bookurl_dynamic_update_click', '', 0, 0, 0, 0, `click_id=${clickId}`).catch();
  }
}
</script>
