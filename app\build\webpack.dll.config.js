const webpack = require('webpack');
const path = require('path');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const ExtractTextPlugin = require('extract-text-webpack-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');

const packageJSON = require('../package.json');
const binDir = path.resolve(packageJSON.build.binDir);
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(binDir, buildTarget, '/resources/app/out');
const isBuildProduction = process.env.BUILD_ENV === 'production';

const vendors = [
  '@xunlei/tiny-logger',
  '@xunlei/thunder-ui',
  '@xunlei/thunder-ui-vue',
  '@xunlei/vuex-connector',
  // 'axios',
  'util.promisify',
  'vue',
  'vue-property-decorator',
  'vuex'
];

module.exports = {
  // for webpack v4
  mode: isBuildProduction ? 'production' : 'development',
  output: {
    path: outDir,
    filename: '[name].js',
    library: '[name]_[chunkhash]'
  },
  target: 'electron-renderer',
  entry: {
    vendor: vendors
  },
  devtool: 'source-map',
  node: false,
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ExtractTextPlugin.extract({
          fallback: 'style-loader',
          use: 'css-loader'
        })
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        use: {
          loader: 'url-loader',
          options: { fallback: 'file-loader', limit: 1, name: 'imgs/[name]--[folder].[ext]' }
        }
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        loader: 'url-loader',
        options: { fallback: 'file-loader', limit: 1, name: 'media/[name]--[folder].[ext]' }
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        use: {
          loader: 'url-loader',
          options: { fallback: 'file-loader', limit: 1, name: 'fonts/[name]--[folder].[ext]' }
        }
      }
    ]
  },
  plugins: [
    new webpack.DllPlugin({
      path: path.join(__dirname, 'temp/manifest.json'),
      name: '[name]_[chunkhash]',
      context: __dirname
    }),
    new ExtractTextPlugin('common-style.css')
  ],
  optimization: {
    ...(isBuildProduction
      ? {
          minimizer: [
            new UglifyJsPlugin({
              sourceMap: buildTarget === 'Release',
              cache: false,
              parallel: true,
              uglifyOptions: {
                ecma: 6,
                compress: {
                  warnings: false
                }
              }
            }),
            new OptimizeCSSAssetsPlugin()
          ]
        }
      : {
          minimize: false
        })
  },
  stats: 'errors-only',
  bail: true
};
