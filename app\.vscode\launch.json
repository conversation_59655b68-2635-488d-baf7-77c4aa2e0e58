{"version": "0.2.0", "configurations": [{"name": "Debug Main Process", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/../bin/Release/resources/app/", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd \"${workspaceFolder}/../bin/Release/resources/bin/Thunder.exe\""}, "args": ["."]}]}