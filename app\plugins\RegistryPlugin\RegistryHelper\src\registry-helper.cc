#include <node_api.h>
#include <node.h>
#include <string>
#include <windows.h>
#include "./text/transcode.h"
#include <atlbase.h>
#include <iostream>
#include <cstring>
#include <stdio.h>

namespace RegistryHelper {
  long GetRegValue(HKEY hMain, const wchar_t* pwszSubKey, const wchar_t* pwszValueName, std::wstring& wstrValue)
  {
    CRegKey regKey;

    long lRet = regKey.Open(hMain, pwszSubKey, KEY_READ | KEY_WOW64_64KEY);
    if (lRet != ERROR_SUCCESS)
    {
      lRet = regKey.Open(hMain, pwszSubKey, KEY_READ);
      if (lRet != ERROR_SUCCESS)
      {
        return lRet;
      }
    }

    DWORD dwType = REG_SZ;
    ULONG uLength = 0;
    lRet = regKey.QueryValue(pwszValueName, &dwType, NULL, &uLength);
    if (lRet == ERROR_SUCCESS)
    {
      if (uLength == 0 || dwType != REG_SZ)
      {
        return -1;
      }
      wchar_t* pBuffer = (wchar_t*)new BYTE[uLength + 2];
      memset(pBuffer, 0, uLength + 2);
      regKey.QueryStringValue(pwszValueName, pBuffer, &uLength);
      wstrValue = pBuffer;
      delete[] pBuffer;
    }
    return lRet;
  }

  void GetRegStringValue(const wchar_t* pwszRegistor, std::wstring& wstrValue) {
    if (!pwszRegistor || wcslen(pwszRegistor) == 0)
    {
      return;
    }
    std::wstring wstrReg(pwszRegistor);
    auto nPos = wstrReg.find_first_of('\\');
    if (nPos == std::wstring::npos)
    {
      return;
    }
    HKEY hMainKey;
    std::wstring wstrKey = wstrReg.substr(0, nPos);
    if (wstrKey == L"HKEY_LOCAL_MACHINE")
    {
      hMainKey = HKEY_LOCAL_MACHINE;
    }
    else if (wstrKey == L"HKEY_CURRENT_USER")
    {
      hMainKey = HKEY_CURRENT_USER;
    }
    else if (wstrKey == L"HKEY_CLASSES_ROOT")
    {
      hMainKey = HKEY_CLASSES_ROOT;
    }
    else if (wstrKey == L"HKEY_USERS")
    {
      hMainKey = HKEY_USERS;
    }
    else if (wstrKey == L"HKEY_CURRENT_CONFIG")
    {
      hMainKey = HKEY_CURRENT_CONFIG;
    }
    else
    {
      return;
    }

    auto nPosEnd = wstrReg.find_last_of('\\');
    if (nPosEnd == std::wstring::npos)
    {
      return;
    }
    std::wstring wstrValueName = wstrReg.substr(nPosEnd + 1);
    if (wstrValueName.empty())
    {
      return;
    }
    if (nPosEnd <= nPos + 1)
    {
      return;
    }
    std::wstring wstSubkey = wstrReg.substr(nPos + 1, nPosEnd - nPos - 1);
    GetRegValue(hMainKey, wstSubkey.c_str(), wstrValueName.c_str(), wstrValue);
  }

  napi_value ReadRegistry(napi_env env, napi_callback_info args) {
    napi_status status;

    size_t argc = 1;
    napi_value argv[1];
    status = napi_get_cb_info(env, args, &argc, argv, NULL, NULL);
    assert(status == napi_ok);

    std::string strRegValue;
    std::wstring wstrReg;
    napi_valuetype vt;
    status = napi_typeof(env, argv[0], &vt);
    assert(status == napi_ok);
    if (vt == napi_string)
    {
      size_t nCount = 0;
      char szUtf8[4096] = { 0 };
      napi_get_value_string_utf8(env, argv[0], szUtf8, 4096, &nCount);
      xl::text::transcode::UTF8_to_Unicode(szUtf8, strlen(szUtf8), wstrReg);

      std::wstring wstrRegValue;
      RegistryHelper::GetRegStringValue(wstrReg.c_str(), wstrRegValue);
      xl::text::transcode::Unicode_to_UTF8(wstrRegValue.c_str(), wstrRegValue.length(), strRegValue);
      napi_value greeting;
      napi_status status;

      status = napi_create_string_utf8(env, strRegValue.c_str(), strRegValue.length(), &greeting);

      if (status != napi_ok) {
        return nullptr;
      }
      return greeting;
    }

    napi_value regValue;
    status = napi_create_string_utf8(env, strRegValue.c_str(), strRegValue.length(), &regValue);

    return regValue;
  }

  std::string GetCmdResult(const std::string& strCmd)
  {
    char buf[10240] = { 0 };
    FILE* pf = NULL;
    std::string strResult = "";
    try {
      if ((pf = _popen(strCmd.c_str(), "r")) == NULL)
      {
        return "";
      }

      
      while (fgets(buf, sizeof buf, pf))
      {
        strResult += buf;
      }

      _pclose(pf);

      unsigned int iSize = strResult.size();
      if (iSize > 0 && strResult[iSize - 1] == '\n')  // linux
      {
        strResult = strResult.substr(0, iSize - 1);
      }
    } catch (int nExpect) {
      strResult = "";
    }
    
    return strResult;
  }

  napi_value RunCmd(napi_env env, napi_callback_info args) {
    napi_status status;

    size_t argc = 1;
    napi_value argv[1];
    status = napi_get_cb_info(env, args, &argc, argv, NULL, NULL);
    assert(status == napi_ok);

    std::string strResult = "";
    napi_valuetype vt;
    status = napi_typeof(env, argv[0], &vt);
    assert(status == napi_ok);
    if (vt == napi_string) {
      size_t nCount = 0;
      char szUtf8[4096] = { 0 };
      napi_get_value_string_utf8(env, argv[0], szUtf8, 4096, &nCount);
      strResult = GetCmdResult(std::string(szUtf8));
    }

    napi_value resultValue;
    status = napi_create_string_utf8(env, strResult.c_str(), strResult.length(), &resultValue);
    return resultValue;
  }

  napi_value init(napi_env env, napi_value exports) {
    napi_status status;
    napi_value fn;

    status = napi_create_function(env, nullptr, 0, ReadRegistry, nullptr, &fn);
    if (status != napi_ok) return nullptr;

    status = napi_set_named_property(env, exports, "readRegString", fn);
    if (status != napi_ok) return nullptr;

    status = napi_create_function(env, nullptr, 0, RunCmd, nullptr, &fn);
    if (status != napi_ok) return nullptr;

    status = napi_set_named_property(env, exports, "runCmd", fn);
    if (status != napi_ok) return nullptr;
    return exports;
  }

  NAPI_MODULE(NODE_GYP_MODULE_NAME, init)
}
