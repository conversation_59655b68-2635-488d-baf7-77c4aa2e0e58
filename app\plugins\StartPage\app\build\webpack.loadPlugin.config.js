'use strict';
const path = require('path');
const packageJSON = require('../package.json');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const buildTarget = process.env.BIN_TARGET || 'Debug';
/** @type {import('webpack').Configuration} **/
module.exports = {

  context: path.resolve(__dirname, '../'),
  target: 'node',
  entry: {
    app: path.resolve(__dirname, '../src/loadPlugin.js')
  },
  output: {
    path: path.join(path.resolve(packageJSON.build.outDir), buildTarget, 'StartPage', packageJSON.version),
    filename: 'loadPlugin.js'
  },
  node: {
    __dirname: false
  },
  plugins: [
    // 使用 CopyPlugin 将 config.json 同步到构建产物中，并替换版本号信息。
    new CopyWebpackPlugin(
      [{
        from: path.resolve(__dirname, 'config.json'),
        to: path.join(path.resolve(packageJSON.build.outDir), buildTarget, 'StartPage'),
        transform(content, path) {
          console.log(content);
          const conf = JSON.parse(content.toString());
          conf.version = packageJSON.version;
          conf.main = `${packageJSON.version}.asar/loadPlugin.js`;
          return Buffer.from(JSON.stringify(conf), 'utf8');
        }
      }]
    )
  ]
};

