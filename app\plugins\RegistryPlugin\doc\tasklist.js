const childProcess = require('child_process');

function getTaskList() {
  return new Promise((resolve,reject) => {
    childProcess.exec('tasklist /fo csv', (err, stdout, stderr) => {
      if (err) {
        resolve([]);
      }

      if (stderr) {
        resolve([]);
      }

      const r = stdout.split('\n').map((line) => {
        const item = line.trim().split(',');
        const name = item[0]; // 0 进程名称， 1 进程id
        return name;
      });
      resolve(r) 
    })
  });
}

getTaskList().then((list) => {
  console.log('#########', list)
})