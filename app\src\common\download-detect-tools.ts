import path = require('path');
import TinyLogger from '@xunlei/tiny-logger';
import requireNodeFile from '@/common/require-node-file';
import { Socket, isIP } from 'net';
import * as dns from 'dns';
const ping: any = require('ping');
import { DownloadKernel } from '@/common/download-kernel';
import util = require('util');
const promisify: any = util.promisify;
import { AxiosStatic, AxiosResponse } from 'axios';
import EventContainer from '@/common/event-container';
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';
import { XLStatNS } from '@/common/xlstat';
import { FileSystemAWNS } from './base/fs-utilities';

const thunderHelper: any = requireNodeFile(path.join(__rootDir, '../bin/ThunderHelper.node'));
const logger: TinyLogger = TinyLogger.getLogger('download-detect-tools');

export namespace DownloadDetectToolsNS {
  export enum AfType {
    AF_INET = 2,
    AF_INET6 = 23
  }

  export enum IfType {
    IF_TYPE_SOFTWARE_LOOPBACK = 24
  }

  export enum IfOperStatus {
    IfOperStatusUp = 1,
    IfOperStatusDown,
    IfOperStatusTesting,
    IfOperStatusUnknown,
    IfOperStatusDormant,
    IfOperStatusNotPresent,
    IfOperStatusLowerLayerDown
  }

  export interface INetworkAdapterIPInfo {
    type: number;
    family: AfType;
    address: string;
  }

  export interface INetworkAdapterInfo {
    type: IfType;
    operStatus: IfOperStatus;
    adapterName: string;
    description: string;
    macAddress: string;
    dnsAddresses: string[];
    ipAddresses: INetworkAdapterIPInfo[];
  }

  export enum DetectState {
    None = 0,
    Started,
    Success,
    Failed
  }

  export enum DetectItemType {
    IndexServer = 0,
    BTIndexServer,
    EMuleIndexServer,
    MirrorSource,
    TorrentServer,
    P2PServer,
    PingServer,
    NATServer,
    AccountLogin,
    VipDownload,
    UPNPState,
    DHTState,
    IPv6Support,
    HostsTamper,
    Baotuan
  }

  export interface IDetectItemData {
    type: DetectItemType;
    name: string;
    state: DetectState;
    detectDesc: string;
    detail: string;
    expand?: boolean;
    showExpand?: boolean;
    statResult?: string;
    detect: (item: IDetectItemData) => Promise<boolean>;
  }

  export interface ILocalIPInfo {
    teleCommunications?: string;
    localIP?: string;
  }

  export enum DetectEvent {
    Start = 'Start',
    StartItem = 'StartItem',
    StatusChange = 'StatusChange',
    FinishItem = 'FinishItem',
    Finish = 'Finish'
  }

  /*
    Index：hub5idx.v6.shub.sandai.net
    Server：hub5sr.v6.shub.sandai.net
    BT：hub5btmain.v6.shub.sandai.net
    eMule：hub5emu.v6.shub.sandai.net
    Torrent：pool.v6.bt.n0808.com
    P2P：hub5pr.sandai.net
    Ping：hub5u.sandai.net
    NAT ：hub5pn.sandai.net
        或 g1.hub5pn.sandai.net
        或 g2.hub5pn.sandai.net
        或 g3.hub5pn.sandai.net
  */

  class Detector extends EventContainer {
    private detectingItems: IDetectItemData[] = null;
    private localIPInfo: ILocalIPInfo = null;
    private currentDetectingIndex: number = undefined;
    private adapterInfos: INetworkAdapterInfo[] = null;
    private statCookie: number = -1;

    constructor() {
      super();

      this.detectingItems = [
        {
          type: DetectItemType.IndexServer,
          name: '普通任务信息',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载“HTTP、FTP”协议的下载链接时，需要查询下载链接对应的文件信息。
          如果此项失败，您将无法加速下载“HTTP、FTP”协议的下载链接。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectIndexServer.bind(this)
        },
        {
          type: DetectItemType.BTIndexServer,
          name: 'BT任务信息',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载BT任务时，需要查询BT任务对应的文件信息。
          如果此项失败，您将无法加速下载BT任务。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectBTIndexServer.bind(this)
        },
        {
          type: DetectItemType.TorrentServer,
          name: '磁力任务信息',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载磁力链接时，需要查询磁力链接对应的BT种子信息。
          如果此项失败，您将无法下载磁力链接。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectTorrentServer.bind(this)
        },
        {
          type: DetectItemType.EMuleIndexServer,
          name: 'eMule任务信息',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载“ed2k”协议的下载链接时，需要查询下载链接对应的文件信息。
          如果此项失败，您将无法加速下载eMule任务。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectEMuleIndexServer.bind(this)
        },
        {
          type: DetectItemType.MirrorSource,
          name: '主机镜像资源服务',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载任意任务时，需要通过该服务查询主机镜像资源。
          如果此项失败，您下载任意任务时，都无法获得主机镜像资源加速。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectMirrorSource.bind(this)
        },
        {
          type: DetectItemType.P2PServer,
          name: '节点镜像资源服务',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载任意任务时，需要通过该服务查询节点镜像资源。
          如果此项失败，您下载任意任务时，都无法获得节点镜像资源加速。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectP2PServer.bind(this)
        },
        {
          type: DetectItemType.PingServer,
          name: '连通检测服务',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载任意任务时，需要通过该服务检测各个镜像资源的连通性，从而提升镜像资源连通率，理论上可以更快获得加速效果。
          如果此项失败，您下载任意任务时，下载加速效果提升较为缓慢。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectPingServer.bind(this)
        },
        {
          type: DetectItemType.NATServer,
          name: '网络感知服务',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载任意任务时，需要通过该服务感知您与节点镜像资源之间的网络连接结构，从而提升节点镜像资源连通率，理论上可以获得更快的下载速度。
          如果此项失败，您下载任意任务时，下载加速效果较差。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectNATServer.bind(this)
        },
        {
          type: DetectItemType.AccountLogin,
          name: '账号登录服务',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷登录账号时，需要通过该服务获取您的账号信息。
          如果此项失败，您将无法登录迅雷账号。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectAccountLogin.bind(this)
        },
        {
          type: DetectItemType.VipDownload,
          name: '会员加速服务',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷会员加速下载任意任务时，需要通过该服务查询会员加速资源，从而获得会员加速效果。
          如果此项失败，您下载任意任务时，都无法获得会员加速效果。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.detectVipDownload.bind(this)
        },
        {
          type: DetectItemType.Baotuan,
          name: '组队加速服务',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用组队加速时，需要查询任务的组队情况，以便加入组队。
          如果此项失败，将无法使用组队加速服务。
          通常该问题与您的网络环境有关，您可以联系客服协助您解决问题。`,
          expand: false,
          showExpand: false,
          detect: this.decectBaotuan.bind(this)
        },
        {
          type: DetectItemType.UPNPState,
          name: 'UPnP状态',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `迅雷能够利用路由器的UPnP功能进行端口映射，从而提升镜像资源的连通率，理论上可以获得更快的下载速度。
          如果您没有使用路由器，这项失败可以忽略。
          如果您使用路由器，并且此项失败，请您登录路由器的管理页面，开启“UPnP”功能。`,
          expand: false,
          showExpand: false,
          detect: this.detectUPNPState.bind(this)
        },
        {
          type: DetectItemType.DHTState,
          name: 'DHT状态',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `使用迅雷下载BT任务时，需要通过DHT网络获取更多镜像资源，理论上可以获得更快的下载速度。
          如果此项失败，您下载BT任务时，下载加速效果可能略受影响。
          但是如果“BT任务信息、节点镜像资源服务”正常，则该失败可以忽略。`,
          expand: false,
          showExpand: false,
          detect: this.detectDHTState.bind(this)
        },
        {
          type: DetectItemType.IPv6Support,
          name: 'IPv6支持',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: `迅雷现已支持IPv6互联网协议，通过该协议迅雷可以更快速可靠的连接镜像资源，从而提升镜像资源连通率，理论上可以获得更快的下载速度。
          如果此项失败，可能是由于您的路由器或网络运营商暂未支持IPv6互联网协议，不过该项失败并不会影响您的正常下载。`,
          expand: false,
          showExpand: false,
          detect: this.detectIPv6Support.bind(this)
        },
        {
          type: DetectItemType.HostsTamper,
          name: 'Hosts文件',
          state: DetectState.None,
          detectDesc: '正在扫描...',
          detail: '被篡改的Hosts文件可能会将迅雷相关域名指向错误的IP地址，从而造成“下载失败、下载缓慢、无法加速、登录失败”等严重问题。如果此项失败，您可以联系客服协助您解决问题',
          expand: false,
          showExpand: false,
          detect: this.detectHostsTamper.bind(this)
        }
      ];
    }

    private async dnsLookupAW(hostName: string): Promise<dns.LookupAddress> {
      let address: dns.LookupAddress = null;
      const dnsLookup: Promisify = promisify(dns.lookup);
      // try {
      //   address = await dnsLookup(hostName, 6);
      // } catch (e) {
      // logger.warning('dnsLookup IPv6 failed! hostName', hostName, 'e', e);
      try {
        address = await dnsLookup(hostName, 4);
      } catch (e) {
        logger.warning('dnsLookup IPv4 failed! hostName', hostName, 'e', e);
      }
      // }
      return address;
    }

    private async tcpConnectAW(host: string, port: number, family: number = 4): Promise<boolean> {
      return new Promise<boolean>(
        async (resolve: (result: boolean) => void): Promise<void> => {
          const socket: Socket = new Socket();
          try {
            socket.on('connect', () => {
              resolve(true);
              socket.end();
            });
            socket.on('error', (err: Error) => {
              logger.warning('tcpConnectAW error! host', host, 'port', port, err);
              resolve(false);
              socket.end();
            });
            socket.on('timeout', () => {
              logger.warning('tcpConnectAW timeout! host', host, 'port', port);
              resolve(false);
              socket.end();
            });
            socket.connect({
              port,
              host,
              family
            });
          } catch (e) {
            logger.warning('tcpConnect failed! host', host, 'port', port, e);
            resolve(false);
          }
        }
      );
    }

    private async detectTcpConnect(detectingItem: IDetectItemData, host: string, port: number = 80): Promise<boolean> {
      let ret: boolean = false;
      do {
        this.setDetectDesc((detectingItem.name.indexOf('服务') !== -1 ? '开始连接' : '开始查询') + detectingItem.name);
        const address: dns.LookupAddress = await this.dnsLookupAW(host);
        if (!address) {
          this.setDetectDesc('域名解析失败', '域名解析失败');
          break;
        }
        this.setDetectDesc('域名解析成功');
        const startTime: number = new Date().getTime();
        const result: boolean = await this.tcpConnectAW(address.address, port);
        if (result) {
          const cost: number = new Date().getTime() - startTime;
          this.setDetectDesc(
            detectingItem.name +
              (detectingItem.name.indexOf('服务') !== -1 ? '连接' : '查询') +
              '成功（耗时：' +
              cost +
              '毫秒）'
          );
          ret = true;
        } else {
          this.setDetectDesc(
            detectingItem.name + (detectingItem.name.indexOf('服务') !== -1 ? '连接' : '查询') + '失败',
            'tcp连接失败（' + address.address + ':' + port + '）'
          );
        }
      } while (false);
      return ret;
    }

    private setDetectDesc(detail: string, statResult?: string): void {
      if (this.currentDetectingIndex !== undefined) {
        const detectItem: IDetectItemData = this.detectingItems[this.currentDetectingIndex];
        detectItem.detectDesc = detail;
        detectItem.statResult = statResult;
        this.fireEvent(DetectEvent.StatusChange, detectItem, detail);
      }
    }

    private async detectIndexServer(detectingItem: IDetectItemData): Promise<boolean> {
      return this.detectTcpConnect(detectingItem, 'hub5idx.v6.shub.sandai.net');
    }

    private async detectBTIndexServer(detectingItem: IDetectItemData): Promise<boolean> {
      return this.detectTcpConnect(detectingItem, 'hub5btmain.v6.shub.sandai.net');
    }

    private async detectEMuleIndexServer(detectingItem: IDetectItemData): Promise<boolean> {
      return this.detectTcpConnect(detectingItem, 'hub5emu.v6.shub.sandai.net');
    }

    private async detectTorrentServer(detectingItem: IDetectItemData): Promise<boolean> {
      return this.detectTcpConnect(detectingItem, 'pool.v6.bt.n0808.com');
    }

    private async detectP2PServer(detectingItem: IDetectItemData): Promise<boolean> {
      return this.detectTcpConnect(detectingItem, 'hub5pr.sandai.net');
    }

    private async detectPingServer(detectingItem: IDetectItemData): Promise<boolean> {
      let ret: boolean = false;
      const host: string = 'hub5u.sandai.net';
      this.setDetectDesc('开始连接' + detectingItem.name);
      const startTime: number = new Date().getTime();
      const result: any = await ping.promise.probe(host);
      if (result && result.alive) {
        const cost: number = new Date().getTime() - startTime;
        this.setDetectDesc(detectingItem.name + '连接成功（耗时：' + cost + '毫秒）');
        ret = true;
      } else {
        logger.information('ping failed! host:', host, ', result:', result);
        this.setDetectDesc(detectingItem.name + '连接失败', 'ping失败（' + host + '）');
      }
      return ret;
    }

    private async detectDCDNServer(detectingItem: IDetectItemData): Promise<boolean> {
      let ret: boolean = false;
      do {
        this.setDetectDesc('开始连接会员加速服务');
        const address: dns.LookupAddress = await this.dnsLookupAW('dcdnhub.xfs.xcloud.sandai.net');
        if (!address) {
          this.setDetectDesc('域名解析失败', 'dcdnhub.xfs.xcloud.sandai.net域名解析失败');
          break;
        }
        this.setDetectDesc('域名解析成功');
        const startTime: number = new Date().getTime();
        const result: boolean = await this.tcpConnectAW(address.address, 80);
        if (result) {
          const cost: number = new Date().getTime() - startTime;
          this.setDetectDesc('会员加速服务连接成功（耗时：' + cost + '毫秒）');
          ret = true;
        } else {
          this.setDetectDesc('会员加速服务连接失败', 'tcp连接失败（' + address.address + ':80）');
        }
      } while (false);
      return ret;
    }

    private async detectVipAuthServer(detectingItem: IDetectItemData): Promise<boolean> {
      let ret: boolean = false;
      do {
        this.setDetectDesc('开始连接会员加速验证服务');
        const address: dns.LookupAddress = await this.dnsLookupAW('ali.pc-x.speed.auth.vip.xunlei.com');
        if (!address) {
          this.setDetectDesc('域名解析失败', 'ali.pc-x.speed.auth.vip.xunlei.com域名解析失败');
          break;
        }
        this.setDetectDesc('域名解析成功');
        const startTime: number = new Date().getTime();
        const result: boolean = await this.tcpConnectAW(address.address, 80);
        if (result) {
          const cost: number = new Date().getTime() - startTime;
          this.setDetectDesc('会员加速验证服务连接成功（耗时：' + cost + '毫秒）');
          ret = true;
        } else {
          this.setDetectDesc('会员加速验证服务连接失败', 'tcp连接失败（' + address.address + ':80）');
        }
      } while (false);
      return ret;
    }

    private async detectMirrorSource(detectingItem: IDetectItemData): Promise<boolean> {
      return this.detectTcpConnect(detectingItem, 'hub5sr.v6.shub.sandai.net');
    }

    private async detectNATServer(detectingItem: IDetectItemData): Promise<boolean> {
      return new Promise<boolean>(
        async (resolve: (result: boolean) => void): Promise<void> => {
          let ret: boolean = false;
          do {
            const hostNATList: string[] = [
              'hub5pn.sandai.net',
              'g1.hub5pn.sandai.net',
              'g2.hub5pn.sandai.net',
              'g3.hub5pn.sandai.net'
            ];
            let statResult: string = '';
            for (let i: number = 0; i < hostNATList.length; ++i) {
              const host: string = hostNATList[i];
              this.setDetectDesc('开始连接' + detectingItem.name + '【' + (i + 1) + '】');
              const startTime: number = new Date().getTime();
              const result: any = await ping.promise.probe(host);
              if (result && result.alive) {
                const cost: number = new Date().getTime() - startTime;
                this.setDetectDesc(detectingItem.name + '连接成功（耗时：' + cost + '毫秒）');
                ret = true;
                break;
              } else {
                logger.warning('detectNATServer ping failed! host:', host, ', result', result);
                if (statResult.length > 0) {
                  statResult += ',';
                }
                statResult += 'ping失败（' + host + '）';
                this.setDetectDesc(detectingItem.name + '连接失败', statResult);
              }
            }
          } while (false);
          resolve(ret);
        }
      );
    }

    private async detectAccountLogin(detectingItem: IDetectItemData): Promise<boolean> {
      return new Promise<boolean>(
        async (resolve: (result: boolean) => void): Promise<void> => {
          this.setDetectDesc('开始账号登录服务');
          const startTime: number = new Date().getTime();
          const axios: AxiosStatic = await require('axios');
          axios.defaults.adapter = require('axios/lib/adapters/http');
          let statResult: string = detectingItem.statResult || '';
          try {
            const res: AxiosResponse = await axios.request({
              method: 'options',
              url: 'https://xluser-ssl.xunlei.com/xluser.core.login',
              params: {}
            });

            if (res.status === 204) {
              const cost: number = new Date().getTime() - startTime;
              this.setDetectDesc('连接账号登录服务成功（耗时：' + cost + '毫秒）');
              resolve(true);
            } else {
              if (statResult.length > 0) {
                statResult += ',';
              }
              statResult += 'options请求失败（' + res.status + '）';
              this.setDetectDesc('连接账号登录服务失败（' + res.status + '）', statResult);
              resolve(false);
            }
          } catch (e) {
            logger.information('detectAccountLogin exception! e', e);
            if (statResult.length > 0) {
              statResult += ',';
            }
            statResult += 'options请求失败，' + (e || '');
            this.setDetectDesc('连接账号登录服务失败', statResult);
            resolve(false);
          }
        }
      );
    }

    private async detectVipDownload(detectingItem: IDetectItemData): Promise<boolean> {
      return new Promise<boolean>(
        async (resolve: (result: boolean) => void): Promise<void> => {
          let ret: boolean = false;
          do {
            // vip auth
            if (!(await this.detectVipAuthServer(detectingItem))) {
              break;
            }
            // DCDN
            if (!(await this.detectDCDNServer(detectingItem))) {
              break;
            }
            ret = true;
          } while (false);
          resolve(ret);
        }
      );
    }

    private async decectBaotuan(detectingItem: IDetectItemData): Promise<boolean> {
      return this.detectTcpConnect(detectingItem, 'team.speed.cdn.vip.xunlei.com');
    }

    private async detectUPNPState(detectingItem: IDetectItemData): Promise<boolean> {
      return new Promise<boolean>(
        async (resolve: (result: boolean) => void): Promise<void> => {
          this.setDetectDesc('开始检测UPnP状态');
          const stat: DownloadKernel.IGlobalStat = await client.callServerFunction('QueryGlobalInfo');
          const ret: boolean = stat && stat.isUPnPReady > 0;
          this.setDetectDesc('UPnP状态' + (ret ? '可用' : '不可用'));
          resolve(ret);
        }
      );
    }
    private async detectDHTState(detectingItem: IDetectItemData): Promise<boolean> {
      return new Promise<boolean>(
        async (resolve: (result: boolean) => void): Promise<void> => {
          this.setDetectDesc('开始检测DHT状态');
          const stat: DownloadKernel.IGlobalStat = await client.callServerFunction('QueryGlobalInfo');
          const ret: boolean = stat && stat.dhtNodeCount > 0;
          this.setDetectDesc('DHT状态' + (ret ? '可用' : '不可用'));
          resolve(ret);
        }
      );
    }
    private async detectIPv6Support(detectingItem: IDetectItemData): Promise<boolean> {
      return new Promise<boolean>(
        (resolve: (result: boolean) => void): void => {
          this.setDetectDesc('开始检测IPv6支持');
          thunderHelper.getNetAdapterInfo(async (adapterInfos: INetworkAdapterInfo[]) => {
            logger.information('adapterInfos', adapterInfos);
            this.adapterInfos = adapterInfos;
            let supportIPv6: boolean = false;
            for (let i: number = 0; i < adapterInfos.length; ++i) {
              const adapterInfo: INetworkAdapterInfo = adapterInfos[i];
              if (
                adapterInfo.type === IfType.IF_TYPE_SOFTWARE_LOOPBACK ||
                adapterInfo.operStatus !== IfOperStatus.IfOperStatusUp
              ) {
                continue;
              }
              for (let j: number = 0; j < adapterInfo.ipAddresses.length; ++j) {
                if (adapterInfo.ipAddresses[j].family === AfType.AF_INET6) {
                  supportIPv6 = true;
                  break;
                }
              }
              if (supportIPv6) {
                break;
              }
            }
            if (supportIPv6) {
              // 机器支持ipv6，再ipv6连接一下hub
              let address: dns.LookupAddress = null;
              const dnsLookup: Promisify = promisify(dns.lookup);
              const hostName: string = 'hub5idx.v6.shub.sandai.net';
              try {
                address = await dnsLookup(hostName, 6);
              } catch (e) {
                logger.warning('dnsLookup IPv6 failed! hostName', hostName, 'e', e);
              }
              if (address) {
                supportIPv6 = await this.tcpConnectAW(address.address, 80, 6);
              } else {
                supportIPv6 = false;
              }
            }
            this.setDetectDesc(supportIPv6 ? '机器和网络环境已支持IPv6' : '机器或网络环境不支持IPv6');
            resolve(supportIPv6);
          });
        }
      );
    }
    private async detectHostsTamper(detectingItem: IDetectItemData): Promise<boolean> {
      let ret: boolean = true;
      do {
        this.setDetectDesc('开始检测Hosts文件');
        const sysPath: string = thunderHelper.getSystemDirectory();
        const hostsPath: string = path.join(sysPath, 'drivers\\etc\\hosts');
        const lines: string[] = await FileSystemAWNS.readLineAw(hostsPath);
        if (!lines || lines.length === 0) {
          break;
        }
        for (let line of lines) {
          line = line.trim();
          if (line.indexOf('#') === 0) {
            continue;
          }
          const items: string[] = line.split(' ');
          if (items.length === 1) {
            continue;
          }

          let ipResult: number = 0;
          try {
            ipResult = isIP(items[0]);
          } catch (error) {
            ipResult = 0;
          }

          if (ipResult === 0) {
            continue;
          }
          for (let index: number = 1; index < items.length; index++) {
            const item: string = items[index];
            if (!item) {
              continue;
            }
            if (item.endsWith('.xunlei.com') || item.endsWith('.sandai.net')) {
              ret = false;
              break;
            }
          }

          if (!ret) {
            break;
          }
        }
      } while (0);
      this.setDetectDesc(ret ? '查询Hosts文件正常' : '检测到Hosts文件被篡改');
      return ret;
    }
    private async sendFinishStat(statResult: string, wrongCnt: number): Promise<void> {
      // windows_version：Windows版本号（例如：10.0.18362.175）
      // download_engine_version：下载引擎版本号
      // dns：DNS信息 {}
      let extData: string = 'result=' + encodeURIComponent(statResult);
      extData += ',wrong_cnt=' + wrongCnt;
      extData += ',ip=' + (this.localIPInfo && this.localIPInfo.localIP ? this.localIPInfo.localIP : '');
      const os: any = await import('os');
      const osVer: string = os.release();
      extData += ',windows_version=' + osVer;
      const downloadSDKPath: string = path.join(__rootDir, '../bin/SDK/DownloadSDK.dll');
      const downloadSDKVersion: string = thunderHelper.getFileVersion(downloadSDKPath);
      extData += ',download_engine_version=' + downloadSDKVersion;
      const dnsList: string[] = this.getDNSList();
      extData += ',dns=' + dnsList.join('|');

      XLStatNS.trackEvent(
        'xlx_download_diagnosis',
        'download_diagnosis_result',
        '',
        0,
        0,
        0,
        0,
        extData,
        this.statCookie
      );
    }
    public async getLocalIPInfoAW(): Promise<ILocalIPInfo> {
      if (this.localIPInfo) {
        return this.localIPInfo;
      }
      const axios: AxiosStatic = await require('axios');
      axios.defaults.adapter = require('axios/lib/adapters/http');
      try {
        const res: AxiosResponse = await axios.request({
          method: 'post',
          url: 'https://tool.lu/ip/ajax.html',
          params: {},
          headers: {}
        });
        if (res.status === 200 && res.data && res.data.text) {
          this.localIPInfo = {
            teleCommunications: res.data.text.ip2region_location,
            localIP: res.data.text.ip
          };
        }
      } catch (e) {
        logger.warning('request https://tool.lu/ip/ajax.html failed! e', e);
      }
      return this.localIPInfo;
    }
    public async start(from?: string): Promise<void> {
      let statResult: string = '';
      let wrongCnt: number = 0;
      this.statCookie = await client.callServerFunction('GetDetectStatCookie');
      client.broadcastEvent('DownloadDetectStart', from);

      this.fireEvent(DetectEvent.Start);
      let detectResult: DetectState = DetectState.Success;
      for (let i: number = 0; i < this.detectingItems.length; ++i) {
        const detectItem: IDetectItemData = this.detectingItems[i];
        this.currentDetectingIndex = i;
        detectItem.state = DetectState.Started;
        this.fireEvent(DetectEvent.StartItem, detectItem, i);
        const result: boolean = await detectItem.detect(detectItem);
        if (result) {
          detectItem.state = DetectState.Success;
        } else {
          detectItem.state = DetectState.Failed;
          detectResult = DetectState.Failed;
          statResult += detectItem.name + ',' + (detectItem.statResult || '') + '|';
          wrongCnt++;
        }
        this.fireEvent(DetectEvent.FinishItem, detectItem, i, detectItem.state);
      }
      // 获取完ip地址后，再发送成功事件
      await this.getLocalIPInfoAW();
      this.fireEvent(DetectEvent.Finish, detectResult);

      this.sendFinishStat(statResult, wrongCnt).catch();
    }
    public getAllDectectItems(): IDetectItemData[] {
      return this.detectingItems;
    }
    public getDNSList(): string[] {
      let list: string[] = [];
      if (this.adapterInfos) {
        const dnsInfoMap: { [dns: string]: boolean } = {};
        for (let i: number = 0; i < this.adapterInfos.length; ++i) {
          const adapterInfo: INetworkAdapterInfo = this.adapterInfos[i];
          if (
            adapterInfo.type === IfType.IF_TYPE_SOFTWARE_LOOPBACK ||
            adapterInfo.operStatus !== IfOperStatus.IfOperStatusUp
          ) {
            continue;
          }
          adapterInfo.dnsAddresses.forEach((dnsAddress: string) => {
            dnsInfoMap[dnsAddress] = true;
          });
        }
        list = Object.keys(dnsInfoMap);
      }
      return list;
    }
    public async restart(from?: string): Promise<void> {
      for (let i: number = 0; i < this.detectingItems.length; ++i) {
        const detectItem: IDetectItemData = this.detectingItems[i];
        detectItem.state = DetectState.None;
        detectItem.detectDesc = '正在扫描…';
      }
      this.currentDetectingIndex = 0;
      return this.start(from);
    }
  }

  const detector: Detector = new Detector();

  export async function getLocalIPInfo(): Promise<ILocalIPInfo> {
    return detector.getLocalIPInfoAW();
  }
  export function start(from?: string): void {
    detector.start(from).catch();
  }
  export function restart(from?: string): void {
    detector.restart(from).catch();
  }
  export function getAllDectectItems(): IDetectItemData[] {
    return detector.getAllDectectItems();
  }
  export function getDNSList(): string[] {
    return detector.getDNSList();
  }
  export function attachListener(event: DetectEvent, listener: (...args: any[]) => void): void {
    detector.attachListener(event, listener);
  }
  export function detachListener(event: DetectEvent, listener: (...args: any[]) => void): void {
    detector.detachListener(event, listener);
  }
}
