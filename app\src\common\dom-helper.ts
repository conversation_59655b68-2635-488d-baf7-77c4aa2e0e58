
export namespace DomHelperNS {

  export function getElementLeft(e: any): number {
    if (!e) {
      return 0;
    }
    let offset: number = e.offsetLeft;
    if (e.offsetParent !== null) {
      offset += getElementLeft(e.offsetParent);
    }
    return offset;
  }

  export function getElementTop(e: any): number {
    if (!e) {
      return 0;
    }
    let offset: number = e.offsetTop;
    if (e.offsetParent !== null) {
      offset += getElementTop(e.offsetParent);
    }
    return offset;
  }

  export function getElementWidth(e: any): number {
    if (!e) {
      return 0;
    }
    return e.clientWidth;
  }

  export function getElementHeight(e: any): number {
    if (!e) {
      return 0;
    }
    return e.clientHeight;
  }

  export function getElementBounds(e: any): any {
    return {
      x: Math.floor((getElementLeft(e))),
      y: Math.floor((getElementTop(e))),
      width: Math.floor(getElementWidth(e)),
      height: Math.floor(getElementHeight(e)),
      right: Math.floor((getElementLeft(e))) + Math.floor(getElementWidth(e)),
      bottom: Math.floor((getElementTop(e))) + Math.floor(getElementHeight(e))
    };
  }

  export function isPointInElement(pt: { x: number, y: number }, e: any): boolean {
    let bound: any = e?.getBoundingClientRect();
    return isPointInBound(pt, bound);
  }

  export function isPointInBound(pt: { x: number, y: number }, bound: any): boolean {
    if (!bound) {
      return false;
    }
    if (pt.x < bound.x || pt.x > bound.right || pt.y > bound.bottom || pt.y < bound.y) {
      return false;
    }
    return true;
  }

  export function isPointInRect(pt: { x: number, y: number }, rect: any): boolean {
    if (!rect) {
      return false;
    }
    if (pt.x < rect.x || pt.x > (rect.x + rect.width) || pt.y > (rect.y + rect.height) || pt.y < rect.y) {
      return false;
    }
    return true;
  }

  export async function downloadImage(imageSrc: string, callBack?: (downloaded: boolean, imageSrc: string) => void): Promise<boolean> {
    let ret: Promise<boolean> = new Promise<boolean>(
      (resolve: (value?: boolean | PromiseLike<boolean>) => void): void => {
        let image: HTMLImageElement = document.createElement('img');
        image.onload = (event: Event): void => {
          if (callBack) {
            callBack(true, imageSrc);
          }
          image = null;
          resolve(true);
        };
        image.onerror = (): void => {
          if (callBack) {
            callBack(false, imageSrc);
          }
          image = null;
          resolve(false);
        };
        image.src = imageSrc;
      }
    );
    return ret;
  }
}
