<template>
  <div class="xly-favorites-recommend">
    <div class="xly-favorites-common__header">
      <h2>搜索发现</h2>
    </div>
    <ul class="xly-favorites-recommend__list">
      <li
        v-for="(item, index) in recData"
        :key="index"
        class="xly-favorites-recommend__item"
        @click="onItemClick(item, 'title')"
      >
        <span class="xly-favorites-recommend__number">{{ index + 1 }}</span>
        <span class="xly-favorites-recommend__title" :title="item.text">{{
          item.text
        }}</span>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import TinyLogger from '@xunlei/tiny-logger';
import { Vue, Component, Prop } from 'vue-property-decorator';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
import { IAssets, ISlotItem, IAssetsType } from '@/main-renderer/common/data-define';

let searchUrl: string = '';
const assetsLength: number = 10;
const logger: TinyLogger = TinyLogger.getLogger('start-page-hotword-text');

@Component({
  components: {
  }
})
export default class HotwordImage extends Vue {
  @Prop({})
  options: ISlotItem[];

  @Prop()
  visible: boolean;

  @Prop()
  hotwordShow: boolean;

  get recData(): (IAssets & { index: number; slotIndex: number })[] {
    const assets: (IAssets & { index: number; slotIndex: number })[] = [];
    for (let index: number = 0; index < this.options.length; index++) {
      const slot: ISlotItem = this.options[index];
      if (!slot.getSucceed()) {
        continue;
      }

      const mainAssets: IAssets = slot.getAssets();
      if (!mainAssets?.title) {
        continue;
      }
      if (slot.getSucceed() && slot.getSubSlots()?.length > 0) {
        const subSlots: ISlotItem[] = slot.getSubSlots();
        for (let i: number = 0; i < subSlots.length; i++) {
          const subSlot: ISlotItem = subSlots[i];
          if (subSlot.getSucceed()) {
            const subAssets: IAssets = subSlot.getAssets();
            if (subAssets.text) {
              assets.push({ ...subAssets, slotIndex: index, index: i });

              if (assets.length >= assetsLength) {
                // 最多取N组有效数据
                break;
              }
            }
          }
        }

        if (assets.length > 0) {
          break;
        }
      }
    }
    return assets;
  }

  reportShow(): void {
    if (this.recData?.length > 0) {
      const optionIndex: number = this.recData[0].slotIndex;
      const slot: ISlotItem = this.options[optionIndex];
      slot.show();
      logger.information('report text hotword showed', slot.id());
    }
  }
  mounted(): void {
    this.$watch('recData', () => {
      if (this.visible && this.hotwordShow && this.recData.length > 0) {
        this.reportShow();
      }
    }, { immediate: true });
  }

  async getSearchUrl(word: string): Promise<string> {
    if (!searchUrl) {
      searchUrl = await clientModule.callServerFunction(
      'GetRemoteGlobalConfigValue',
      'search',
      'search_engine_url',
      'https://www.baidu.com/s?tn=46054985_oem_dg&wd=$word$'
      );
    }
    return searchUrl.replace('$word$', encodeURIComponent(word));
  }

  async onItemClick(item: IAssets & { index: number; slotIndex: number }, type: IAssetsType): Promise<void> {
    logger.information('text hotword click', item.slotIndex, item.index);
    this.options?.[item.slotIndex].getSubSlots()?.[item.index]?.click(type);

    if (!item.linkUrl) {
      const url = await this.getSearchUrl(item.text);
      // todo 
      const searchFrom = 'start_page_right-' + 'search_discovery_module';
      clientModule.callServerFunction('OpenNewTab', url, JSON.stringify({ extData: JSON.stringify({ search_from: searchFrom }) })).catch();
      clientModule.callServerFunction('AddSearchKeyword', { keyword: item.text, url: url }).catch();
    }
  }
}
</script>
