{"name": "hotfix-none", "version": "1.0.2", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "main": "1.0.2.asar/index.js", "scripts": {"format": "prettier --write \"{src,__tests__}/**/*.ts\" --single-quote", "lint": "tslint --force --format verbose --project ./", "r": "cross-env BIN_TARGET=Release webpack", "pr": "cross-env BUILD_ENV=production BIN_TARGET=ProductRelease webpack", "postr": "cross-env BIN_TARGET=Release node ./build/build-asar.js", "postpr": "cross-env BUILD_ENV=production BIN_TARGET=ProductRelease node ./build/build-asar.js", "start-release": "node ./bin/Release/app/index.js", "start-product-release": "node ./bin/ProductRelease/app/index.js"}, "license": "MIT", "dependencies": {}, "devDependencies": {"@types/node": "^10.12.12", "copy-webpack-plugin": "^4.5.2", "cross-env": "^5.2.0", "node-loader": "^0.6.0", "source-map-loader": "^0.2.4", "ts-loader": "^4.4.2", "typescript": "^4.2.4", "uglifyjs-webpack-plugin": "^1.3.0", "webpack": "^4.16.5", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.5"}}