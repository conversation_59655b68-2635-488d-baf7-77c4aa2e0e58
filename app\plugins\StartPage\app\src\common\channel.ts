// =================================================================
// @description: 渲染进程与主进程发送消息的channel定义集合
//               渲染进程与主进程通信命名：channle{$RM_MODULENAME_ChANNELID} = '{$RM_MODULENAME_ChANNELID}'
//               主进程与渲染进程通信命名：channel{$MR_MODULENAME_ChANNELID} = '{$MR_MODULENAME_ChANNELID}'
//               特别注意：后续如果业务改动导致不再需要某个消息id，请注意第一时间从本文件中剔除
// @author:      maolanghua
// @date:        2018.03.23
// =================================================================
export namespace ThunderChannelList {
  // 渲染进程与主进程的通信id
  export const channelBase: string = 'ChannelBase';
  export const channelRMNewTaskSetTaskData: string = channelBase + '1';
  export const channelRMPreNewTaskSetTaskData: string = channelBase + '2';
  export const channelRMNewTaskCreateNewTask: string = channelBase + '3';
  // 主渲染进程发给BT进程的bt信息
  export const channelRMNewTaskSetBTInfo: string = channelBase + '4';
  export const channelRMNewTaskDownloadTorrent: string = channelBase + '5';
  // bt进程点击立即下载后发消息到主渲染进程创建任务
  export const channelRMNewTaskCreateBtTask: string = channelBase + '6';
  // bt解析进程点击关闭，发消息到主渲染进程暂停后台磁力链任务
  export const channelRMNewTaskCancleMagnet: string = channelBase + '7';
  // 新建任务进程导入torrent种子
  export const channelRMImportTorrent: string = channelBase + '8';
  // 渲染进程通知主进程配置获取结果
  export const channelRMGetConfigValueResolve: string = channelBase + '9';
  // 渲染进程通知主进程配置获取错误
  export const channelRMGetConfigValueReject: string = channelBase + '10';

  // 主进程与渲染进程通信id
  export const channelMRTrayMenuClick: string = channelBase + '11'; // 托盘菜单点击事件
  export const channelMRNewTaskMagnetTaskCreated: string = channelBase + '12';
  export const channelMRNewTaskDownloadTorrentResult: string = channelBase + '13';
  // 主渲染进程发给创建任务进程，通知其关闭的id
  export const channelMRNewTaskCreateNewTaskResult: string = channelBase + '14';
  // 主渲染进程发给创建bt任务进程，通知其关闭的id
  export const channelMRNewTaskCreateBtTaskResult: string = channelBase + '15';
  // 主进程发送消息给主渲染进程获取配置
  export const channelMRGetConfigValue: string = channelBase + '16';
  // 主进程发送消息给主渲染进程设置配置
  export const channelMRSetConfigValue: string = channelBase + '17';

  // 计划任务渲染进程发消息给主渲染进程，保存当前计划任务
  export const channelRMCommitPlanTask: string = channelBase + '18';
  export const channelRMPerformePlanTask: string = channelBase + '19';

  // 渲染进程发送消息给主进程，监控渲染进程异常退出时主进程也退出
  export const channelRMProcessSend: string = channelBase + '20';

  // 渲染进程发送消息给主进程，获取私人空间信息
  export const channelRMGetPrivateSpaceInfo: string = channelBase + '21';
  // 主进程发送消息给渲染进程，返回私人空间信息
  export const channelMRGetPrivateSpaceInfoResult: string = channelBase + '22';

  // 渲染进程发送文件操作消息给主进程
  export const channelRMFileCopy: string = channelBase + '23';
  export const channelRMFileMove: string = channelBase + '24';
  // 主进程向渲染进程发送文件操作结果的消息
  export const channelMRFileCopyResult: string = channelBase + '25';
  export const channelMRFileMoveResult: string = channelBase + '26';

  // 渲染进程发消息给主进程，通过cid获取字幕信息
  export const channelRMGetSutitleByCid: string = channelBase + '27';
  // 主进程返回字幕信息
  export const channelMRGetSutitleByCidResult: string = channelBase + '28';
  // 渲染进程发消息给主进程，通过name获取字幕信息
  export const channelRMGetSutitleByName: string = channelBase + '29';
  // 主进程返回字幕信息
  export const channelMRGetSutitleByNameResult: string = channelBase + '30';
  // 渲染进程发消息给主进程，下载字幕信息
  export const channelRMDownloadSutitle: string = channelBase + '31';
  // 主进程发下载字幕成功
  export const channelMRDownloadSutitleSuc: string = channelBase + '32';
  // 主进程发下载字幕失败
  export const channelMRDownloadSutitleFail: string = channelBase + '33';
  // 渲染进程发消息给主进程，获取可播放任务的任务名
  export const channelRMGetDisplayName: string = channelBase + '34';
  // 主进程返回显示的名字
  export const channelMRGetDisplayNameResult: string = channelBase + '35';
  // 渲染进程发消息给主进程，把窗口拉到最前，bringWindowToTop只能在主进程调用
  export const channelMRBringWindowToTop: string = channelBase + '36';
  // 渲染进程发消息给主进程，点击下载完整版本迅雷播放组件
  export const channelRMDownloadXmp: string = channelBase + '37';
  // 主进程发消息给渲染进程，修复xmp成功
  export const channelMRFixXmpSuc: string = channelBase + '38';
  // 主进程发消息给渲染进程，修复xmp失败
  export const channelMRFixXMPFail: string = channelBase + '39';
  // 渲染进程发送给主后台进程创建播放列表窗口
  // export const channelRMCreatePlaylistWindow = channelBase + '40';
  export const channelMRAPlayerCreated: string = channelBase + '41';
  // 主渲染进程nativecall初始化完成
  export const channelMRMainRendererNativeCallInited: string = channelBase + '42';
  // 隐藏显示窗口
  export const channelMRShowWindow: string = channelBase + '43';
  // 创建TopView
  export const channelMRCreateTopView: string = channelBase + '44';
  // 将top hwnd发送到thunder进行管理
  export const channelMRSendTopView: string = channelBase + '44.1';
  // 销毁TopView
  export const channelMRDestroyTopView: string = channelBase + '44.5';
  // 创建TopViewFinish(reaty-to-show才返回)
  export const channelMRCreateTopViewReadyFinish: string = channelBase + '45';
  // 创建TopViewFinish(create即返回)
  export const channelMRCreateTopViewFinish: string = channelBase + '45.5';
  // 创建CtrlView
  export const channelMRCreateCtrlView: string = channelBase + '46';
  // 将ctrl hwnd发送到thunder进行管理
  export const channelMRSendCtrlView: string = channelBase + '46.1';
  // 销毁CtrlView
  export const channelMRDestroyCtrlView: string = channelBase + '46.5';
  // 创建CtrlViewFinish(reaty-to-show才返回)
  export const channelMRCreateCtrlViewReadyFinish: string = channelBase + '47';
  // 创建CtrlViewFinish(create即返回)
  export const channelMRCreateCtrlViewFinish: string = channelBase + '47.5';
  // 创建SiderBar
  export const channelMRCreateSideBar: string = channelBase + '48';
  // 创建创建SiderBarFinish
  export const channelMRCreateSideBarFinish: string = channelBase + '49';
  // 展开播放列表,动画
  export const channelMRExpandListWindow: string = channelBase + '52';
  // 展开VUE列表视图
  export const channelMRExpandListVue: string = channelBase + '53';
  // 设置是否变更APlayerViewSize
  export const channelMRSetChangeViewSize: string = channelBase + '54';
  // XmpShowModeNotify
  export const channelMRSyncShowMode: string = channelBase + '55';
  // APWindowUpdate
  export const channelMRAPWindowUpdate: string = channelBase + '56';
  // 更新APlayer位置
  export const channelMRUpdateAPlayerPos: string = channelBase + '57';
  // XmpVideoTipCreated
  export const channelMRXmpVideoTipCreated: string = channelBase + '58';
  // XmpVideoTipDestroy
  export const channelMRXmpVideoTipDestroy: string = channelBase + '59';
  // ShowSettingCenterWindow
  export const channelMRShowSettingCenterWindow: string = channelBase + '60';
  // 调整位置
  export const channelRMSetPosition: string = channelBase + '61';
  // 渲染进程发送消息给主进程，弹出右下角的设置窗口
  export const channelRMShowPlaySettingWindow: string = channelBase + '62';
  // 隐藏显示鼠标
  export const channelRMShowCursor: string = channelBase + '63';
  // 通知APlayer播放状态
  export const channelRMAPlayerState: string = channelBase + '64';
  // 通知主进程APlayer播放进度
  export const channelRMAPlayerProgress: string = channelBase + '65';
  // [主后台进程] 调用播放,暂停,上一个下一个
  export const channelRMPlayAction: string = channelBase + '66';
  // 渲染进程发消息给主进程，将焦点设置到主进程窗口上
  export const channelRMSetFoucs: string = channelBase + '67';
  // 主进程发消息给渲染进程，调整Z序
  export const channelMRSetZorder: string = channelBase + '68';
  // 渲染进程发消息给主进程，获取主进程启动方式
  export const channelRMGetBrowserStartType: string = channelBase + '69';
  // 主进程发消息给渲染进程，返回主进程启动方式结果
  export const channelMRGetBrowserStartTypeResult: string = channelBase + '70';
  // 弹出窗口，统一消息，便于全局知晓有弹出窗口显示了
  export const channelMRWindowPopUp: string = channelBase + '71';
  // 渲染进程发信息主进程，更新tips窗口的Z序
  export const channelMRUpdateTipWindowZorder: string = channelBase + '72';
  // 渲染进程发信息主进程，设置阴影窗口是否能resize
  export const channelMRSetShadowWindowResize: string = channelBase + '73';
  // 当前浏览器窗口改变事件
  export const channelMRBrowserWindowChange: string = channelBase + '74';
  // 设置全屏
  export const channelMRChangeFullScreen: string = channelBase + '75';
  // tab显示个数改变事件
  export const channelMRTabNumberChange: string = channelBase + '76';
  // taskBar 接口操作
  export const channelMRThumbTaskBarAction: string = channelBase + '77';
  // taskBar Button Status
  export const channelMRThumbTaskBarButtonStatus: string = channelBase + '78';
  // 打开文件管理器事件
  export const channelRMOpenFolder: string = channelBase + '79';
  // 重启播控栏
  export const channelRReCreateCtrlWindow: string = channelBase + '80';
  // 重启顶部栏
  export const channelRReCreateTopWindow: string = channelBase + '81';
  // 设置环境变量
  export const channelRMSetEnvironmentVariable: string = channelBase + '82';
  // 通知主进程server初始化完毕
  export const channelRMServerStarted: string = channelBase + '83';
  // 通知主进程打开相关窗口的开发者工具
  export const channelRMOpenDevTools: string = channelBase + '84';
  // 进入视频编辑模式
  export const channelEnterEditMode: string = channelBase + '85';
  // 更新编辑模式区域
  export const channelUpdateEditRect: string = channelBase + '86';
  // MessageBox关闭事件
  export const channelMessageBoxClose: string = channelBase + '87';
  // 阻止系统休眠事件
  export const channelPreventSleep: string = channelBase + '88';
  // 取消阻止系统休眠事件
  export const channelCancelPreventSleep: string = channelBase + '89';
  // 关闭小设置面板
  export const channelCloseEffectWindow: string = channelBase + '90';
  // 在后台进程弹出菜单
  export const channelPopUpMenu: string = channelBase + '91';
  // 老板键销毁托盘图标
  export const channelHideTray: string = channelBase + '92';
  // 显示异常阴影窗口
  // export const channelShowShadowWindow: string = channelBase + '93';
  // 处理内嵌模式子窗口的move和resize
  export const channelEmbedMoveResize: string = channelBase + '94';
  // 处理timetip 播放位置初始化
  export const channelTimeTipPos: string = channelBase + '95';
  // 处理timetip 播放位置初始化
  export const channelUpdateVideoTip: string = channelBase + '96';
  // 查询BrowserView是否存在
  export const channelGetBrowserView: string = channelBase + '97';
  // 查询BrowserView回应
  export const channelGetBrowserViewResult: string = channelBase + '98';
  // BrowserView LoadURL
  export const channelBrowserViewLoad: string = channelBase + '99';
  // BrowserView OpenDev
  export const channelBrowserViewOpenDev: string = channelBase + '100';
  // 在当前渲染进程应用BrowserView对象
  export const channelApplyBrowserView: string = channelBase + '101';
  // CreateBrowserView
  export const channelCreateBrowserView: string = channelBase + '102';
  // CreateBrowserView回应
  export const channelCreateBrowserViewResult: string = channelBase + '103';
  // BrowserView Function Call
  export const channelBrowserViewCall: string = channelBase + '104';
  export const channelBrowserViewCallRet: string = channelBase + '105';

  // 处理内嵌模式子窗口的move和resize
  export const channelEmbedWindowRgn: string = channelBase + '106';

  // 主渲染进程发送给主进程更新日志环境
  export const channelRMUpdateLogEnviroment: string = 'RM_UPDATE_LOG_ENVIRONMENT';
  // 主进程发送给主渲染进程更新日志环境
  export const channelMRUpdateLogEnviroment: string = 'MR_UPDATE_LOG_ENVIRONMENT';
}
