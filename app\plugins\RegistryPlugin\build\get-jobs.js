const merge = require('webpack-merge');
const fs = require('fs');

module.exports = (config) => {
  const rendererTasks = config.filter((task) => task.type === 'renderer');
  const otherTasks = config.filter((task) => task.type !== 'renderer');
  let mainRendererTask = rendererTasks.filter((task) => task.name == "main-renderer");
  mainRendererTask.map((task) => {
    if (task.name == "main-renderer") {
      let str = `'use strict'; \r
      const merge = require('webpack-merge'); \r
      module.exports = merge.smart( \r
        require(\`./webpack.renderer-common.config.js\`), \r
        require(\`./webpack.${task.name}.config.js\`) \r
        )`;
          fs.writeFileSync(`./build/webpack.${task.name}.temp.config.js`, str)
    }
  })
  let otherRendererTask = rendererTasks.filter((task) => task.name != "main-renderer");

  // 修复编译指定模块时的报错
  const rendererTasksJob = otherRendererTask.length > 0 ? [{
    name: 'renderer',
    config: merge(
      require(`./webpack.renderer-common.config.js`),
      ...otherRendererTask.map((task) => require(`./webpack.${task.name}.config.js`))
    )
  }] : []

  const jobs = [
    ...rendererTasksJob,
    ...otherTasks.map((task) => ({
      name: task.name,
      config: require.resolve(`./webpack.${task.name}.config.js`)
    })),
    ...mainRendererTask.map((task) => ({
       name: `renderer-${task.name}`,
       config: require.resolve(`./webpack.${task.name}.temp.config.js`)
    }))
  ];
  return jobs;
};
