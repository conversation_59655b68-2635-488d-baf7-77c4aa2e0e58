import { VNode, VNodeDirective } from 'vue';

const visiblityDirective: any = {
  bind(el: any, { value }: VNodeDirective, vnode: VNode): void {
    el.__vOriginalVisibility = el.style.visiblity;
    el.style.visibility = value ? 'visible' : 'hidden';
  },

  update(el: any, { value, oldValue }: VNodeDirective, vnode: VNode): void {
    /* istanbul ignore if */
    if (value === oldValue) return;
    el.style.visibility = value ? 'visible' : 'hidden';
  },

  unbind(el: any, binding: VNodeDirective, vnode: VNode, oldVnode: VNode, isDestroy: boolean): void {
    if (!isDestroy) {
      el.style.visiblity = el.__vOriginalVisibility;
    }
  }
};

export default visiblityDirective;
