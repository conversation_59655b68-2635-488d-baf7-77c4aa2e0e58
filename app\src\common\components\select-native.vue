<template>
  <!-- 根据$slots 里面是否存在对象, 决定是否使用原生select -->
  <td-select v-if="!useNative"
    v-bind="$attrs"
    v-on="$listeners"
    :value="value"
    ref="select"
    :options="filterOptions"
    :position="position"
  >
    <slot slot="suffix" name="suffix"></slot>
    <slot slot="append" name="append"></slot>
  </td-select>
  <select @change="onSelect" class="xly-select" v-else ref="selectNative">
    <option v-for="opt of filterOptions" :value="opt" :key="opt">{{ opt }}</option>
  </select>
</template>

<script lang="ts">
// import asyncRemote = require('@xunlei/async-remote');
// import { asyncRemoteCall } from '@/common/renderer-process-call';
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';

@Component
export default class SelectNative extends Vue {
  /** 绑定值 */
  @Prop()
  value: string;

  /** 下拉选项 */
  @Prop({
    default: (): string[] => []
  })
  options: string[];

  @Prop({ default: false })
  useNative: boolean;

  @Prop({ default: 'bottom' })
  position: string;

  /** 下拉菜单控件 */
  // dropdown: any;

  @Watch('options')
  async onOptionsChanged(): Promise<void> {
    //
  }

  get filterOptions(): any {
    return this.options.filter((opt: any) => {
      if (opt === 'separator') {
        return false;
      }
      return true;
    });
  }

  onSelect(e: Event): void {
    this.$emit('input', (e.srcElement as HTMLSelectElement).value, true);
  }

  mounted(): void {
    const ele: any = this.$refs.selectNative;
    if (ele) {
      for (const index in this.filterOptions) {
        if (String(this.filterOptions[index]) === this.value) {
          ele.selectedIndex = index;
          break;
        }
      }
    }
  }

  focusEdit(): void {
    if (this.$refs.select) {
      const input: HTMLInputElement = ((this.$refs.select as Vue).$el as Element).querySelector('input');
      if (input !== null && input !== undefined) {
        input.focus();
      }
    }
  }
}
</script>

<style>
.xly-select-setting .td-dropdown-menu { opacity: 0; }
</style>