
const cardAdd = require('@/assets/img/sign-in/card-add.png')
const cardFlow = require('@/assets/img/sign-in/card-retrieve.png')
const cardSpeed = require('@/assets/img/sign-in/card-speed.png')
const cardVip = require('@/assets/img/sign-in/card-svip-s.png')

interface ILocalAssetMap {
  icon: string                      // 图标
  trackType: string                 // 埋点的 type
  isDistribute: boolean             // 是否是直接发放到账户的（云添加、高速流量）
  expired: number                   // 过期时间，单位小时（目前服务端会返回 expired_time，这里暂时不使用）
  usageLimitText: string            // 过期文案
  nameTextSymbol: string            // 单位符号
  cost_asset_id?: number
  get_asset_id?: number
}

interface ISignInAward {
  [key: number]: ILocalAssetMap
}

/**
 * 签到任务奖励映射，服务端对应的资产 id
 * cost_asset_id 与 get_asset_id 为服务端固定映射，目前仅 超会天卡、加速试用 才有
 */
export const SIGN_IN_AWARD: ISignInAward = {
  // 云添加次数+1
  37: {
    icon: cardAdd,
    trackType: 'cloud_add1',
    isDistribute: true,
    expired: 24,
    usageLimitText: '今日内有效',
    nameTextSymbol: '+',
  },
  // 云盘下载高速流量+100M
  38: {
    icon: cardFlow,
    trackType: 'withdraw_flow',
    isDistribute: true,
    expired: 24,
    usageLimitText: '今日内有效',
    nameTextSymbol: '',
  },
  // 加速试用权益
  39: {
    icon: cardSpeed,
    trackType: 'peedup_try',
    isDistribute: false,
    expired: 24,
    usageLimitText: '今日内有效',
    nameTextSymbol: '*',
    cost_asset_id: 42,
    get_asset_id: 39,
  },
  // 天超级会员
  40: {
    icon: cardVip,
    trackType: 'vip_1day',
    isDistribute: false,
    expired: 24 * 7,
    usageLimitText: '7天内有效',
    nameTextSymbol: '*',
    cost_asset_id: 41,
    get_asset_id: 40,
  },
  // 天超级会员-卡
  41: {
    icon: cardVip,
    trackType: 'vip_1day',
    isDistribute: false,
    expired: 24 * 7,
    usageLimitText: '7天内有效',
    nameTextSymbol: '*',
    cost_asset_id: 41,
    get_asset_id: 40,
  },
  // 加速试用-卡
  42: {
    icon: cardSpeed,
    trackType: 'peedup_try',
    isDistribute: false,
    expired: 24,
    usageLimitText: '今日内有效',
    nameTextSymbol: '*',
    cost_asset_id: 42,
    get_asset_id: 39,
  },
  // 云添加次数+3
  43: {
    icon: cardAdd,
    trackType: 'cloud_add3',
    isDistribute: true,
    expired: 24,
    usageLimitText: '今日内有效',
    nameTextSymbol: '+',
  },
}
