/**
 * @description: 任务总管理器
 *               主要是维护整个任务列表，包括:正在下载、已完成、垃圾箱、
 *               已经开始的任务
 *               负责任务的创建、管理、任务的生命周期
 *               轮询任务信息、设置任务信息等等
 * @author:      <PERSON><PERSON><PERSON><PERSON>
 * @version:     1.0
 */

import { ipcRenderer } from 'electron';
import path = require('path');
import fs = require('fs');
import os = require('os');
import TinyLogger from '@xunlei/tiny-logger';
import requireNodeFile from '@/common/require-node-file';
import EventContainer from '@/common/event-container';
import { FileSystemAWNS } from '@/common/base/fs-utilities';
import { NodeEventMesssageNS } from '@/common/node-event-message';
import { Common } from '@/common/common';
import { DownloadKernel } from '@/common/download-kernel';
import { ThunderKernel } from '@/main-renderer/common/download-kernel/thunder-kernel/thunder-kernel';
import { Task } from '@/main-renderer/common/download-kernel/impl/task';
import { GroupTask } from '@/main-renderer/common/download-kernel/impl/task-group';
import { CategoryView } from '@/main-renderer/common/download-kernel/impl/category-view';
import { DownloadKernelEventManager, EventManager } from '@/main-renderer/common/download-kernel/impl/event-manager';
import { DownloadKernelCategoryViewManager } from '@/main-renderer/common/download-kernel/impl/category-view-manager';
import { BtTask } from '@/main-renderer/common/download-kernel/impl/task-bt';
import { BtFile } from '@/main-renderer/common/download-kernel/impl/file-bt';
import { DownloadKernelCategoryManager } from '@/main-renderer/common/download-kernel/impl/category-manager';
import { ThunderChannelList } from '@/common/channel';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
import { SingleBtTaskNS } from '@/main-renderer/common/download-kernel/impl/single-bt-task';
import { MessageBox } from '@/common/message-box';
import { MessageBoxNS } from '@/common/message-box/message-box-helper';
import { TaskUserDataGUID } from '@/common/task-userdata-guid';
import { ThunderPanTaskNS } from '../../thunder-pan-task';
import { SinglePromise } from '@/common/single-promise';
const eventManager: EventManager = DownloadKernelEventManager.getEventManager() as EventManager;
const categoryViewManager: DownloadKernel.ICategoryViewManager = DownloadKernelCategoryViewManager.getCategoryViewManager();
const categoryManager: DownloadKernel.ICategoryManager = DownloadKernelCategoryManager.getCategoryManager();
const logger: TinyLogger = TinyLogger.getLogger('DownloadKernel.TaskManager');
const thunderHelper: any = requireNodeFile(path.join(__rootDir, '../bin/ThunderHelper.node'));

interface ITaskNameResult {
  newName: string;
  index: number;
}

export class TaskManager implements DownloadKernel.ITaskManager {
  // 总任务列表 key: taskId
  private tasks: Map<number, DownloadKernel.ITask>;
  // 已开始的任务列表taskId
  private startTasks: Set<number>;

  private singletonInstance: SinglePromise;

  private isLoadStorageFinish: boolean = false;
  private isInitDkFinish: boolean = false;
  private isInitDkSuccess: boolean = false;  // sdk 是否初始化成功
  private isEnableP2pChannel: boolean = true;
  private isEnableP2sChannel: boolean = true;
  private lastGlobalStat: DownloadKernel.IGlobalStat = {
    size: 0,
    recvdBytes: 0,
    sentBytes: 0,
    dhtNodeCount: 0,
    isUPnPReady: 0
  };
  private lastGlobalTime: number = 0;
  private totalDownloadSpeed: number = 0;
  private totalUploadSpeed: number = 0;
  private eventContainer: EventContainer = new EventContainer();
  private sdkCrash: boolean = false;
  private backgroundTasks: Set<number>;

  constructor() {
    this.tasks = new Map();
    this.startTasks = new Set();
    this.backgroundTasks = new Set();
    this.singletonInstance = new SinglePromise();

    clientModule.attachServerEvent('OnPanPluginTrashFiles', (context: unknown, files: string[], retrieval_fn_error: 'file_in_recycle_bin' | 'file_not_found') => {
      const filesMap: { [fileId: string]: boolean } = {};
      for (let file of files) {
        filesMap[file] = true;
      }
      this.startTasks.forEach(async (taskId: number) => {
        const item = this.tasks.get(taskId);
        if (item) {
          const extra: DownloadKernel.ITaskExtra = item.getExtraObject();
          const userData: string = extra.getUserData(TaskUserDataGUID.guidThunderYunDownloadUserData, 'extras');
          if (userData) {
            let userDataObj: { fileId: string; isInSafe: number; linkExpire: string; passCodeToken: string; shareId: string; token: string; _retrieval_fn_error?: string } = null;
            try {
              userDataObj = JSON.parse(userData);
            } catch (error) {
              //
            }

            logger.information('force failure get error', taskId, userDataObj?.fileId);
            if (userDataObj?.fileId) {
              if (filesMap[userDataObj?.fileId]) {
                if (retrieval_fn_error) {
                  userDataObj._retrieval_fn_error = retrieval_fn_error;
                  await clientModule.callServerFunction('SetUserData', taskId, TaskUserDataGUID.guidThunderYunDownloadUserData, 'extras', JSON.stringify(userDataObj))
                }
                this.forcedFailureTask(taskId, 421);
                // clientModule.callServerFunction('ForcedFailureTask', taskId, 421);
              }
            }
          }
        }
      });
    });
  }

  get adTaskId(): number {
    return 1;
  }

  public getStartTaskIDs(): number[] {
    return Array.from(this.startTasks);
  }

  // 创建单个任务
  private async internalCreateTask(
    taskType: DownloadKernel.TaskType,
    newTaskInfo: DownloadKernel.INewTaskInfo
  ): Promise<DownloadKernel.ITask> {
    // 创建任务并且加入到任务列表
    let data: { [propName: string]: string | number; } = undefined;
    if (taskType === DownloadKernel.TaskType.Bt) {
      data = window.__performanceMap['ClickDownload_' + newTaskInfo.btTaskInfo.infoId];
    } else if (taskType === DownloadKernel.TaskType.Group) {
      data = window.__performanceMap['ClickDownload_' + newTaskInfo.taskBaseInfo.taskName];
    } else {
      data = window.__performanceMap['ClickDownload_' +
        newTaskInfo.taskBaseInfo.gcid || newTaskInfo.emuleTaskInfo?.url.slice(0, 150) || newTaskInfo.p2spTaskInfo?.url.slice(0, 150)];
    }
    if (data && data.extdata) {
      data.extdata += ',createTaskStart-tick=' + thunderHelper.getTickCount() + ',allTaskNum=' + this.getTaskCount();
    }
    await this.downloadSdkInited();
    let ret: boolean = true;
    let isSubTask: boolean = false;
    let newTask: Task = new Task(this);

    let categoryId: number = newTaskInfo.categoryId;
    if (newTaskInfo.privateSpace) {
      const category: DownloadKernel.ICategory = categoryManager.getCurrentPrivateCategory();
      if (category) {
        categoryId = category.getId();
      }
    }
    switch (taskType) {
      case DownloadKernel.TaskType.P2sp:
        {
          newTaskInfo.taskBaseInfo.checkIsHtml = newTaskInfo.taskBaseInfo.checkIsHtml ?? true;
          const newName: string = (await this.getTaskName(
            taskType,
            newTaskInfo.taskBaseInfo.savePath,
            newTaskInfo.taskBaseInfo.taskName,
            0,
            newTaskInfo.taskBaseInfo.origin
          )).newName;
          newTaskInfo.taskBaseInfo.taskName = newName;
          const taskId: number = newTask.createP2spTask(
            newTaskInfo.isBackground,
            newTaskInfo.taskBaseInfo,
            newTaskInfo.p2spTaskInfo,
            categoryId
          );
          if (newTaskInfo.taskBaseInfo.groupTaskId && newTaskInfo.taskBaseInfo.groupTaskId > 0) {
            isSubTask = true;
          }
          if (newTaskInfo.isBackground) {
            this.backgroundTasks.add(taskId);
          }

          if (newTaskInfo.isOneClickDownload) {
            newTask.setIsOneClickDownload();
          }
        }
        break;

      case DownloadKernel.TaskType.Bt:
        // BT任务特殊处理
        {
          // if (!newTaskInfo.taskBaseInfo.import) {
          //   let newName: string = (await this.getTaskName(
          //     taskType,
          //     newTaskInfo.taskBaseInfo.savePath,
          //     newTaskInfo.taskBaseInfo.taskName,
          //     0,
          //     newTaskInfo.taskBaseInfo.origin
          //   )).newName;
          //   newTaskInfo.taskBaseInfo.taskName = newName;
          // }

          await newTask.createBtTask(
            newTaskInfo.isBackground,
            newTaskInfo.taskBaseInfo,
            newTaskInfo.btTaskInfo,
            categoryId
          );
        }
        break;

      case DownloadKernel.TaskType.Emule:
        {
          const newName: string = (await this.getTaskName(
            taskType,
            newTaskInfo.taskBaseInfo.savePath,
            newTaskInfo.taskBaseInfo.taskName,
            0,
            newTaskInfo.taskBaseInfo.origin
          )).newName;
          newTaskInfo.taskBaseInfo.taskName = newName;
          newTask.createEmuleTask(
            newTaskInfo.isBackground,
            newTaskInfo.taskBaseInfo,
            newTaskInfo.emuleTaskInfo,
            categoryId
          );
          if (newTaskInfo.taskBaseInfo.groupTaskId && newTaskInfo.taskBaseInfo.groupTaskId > 0) {
            isSubTask = true;
          }
          if (newTaskInfo.isOneClickDownload) {
            newTask.setIsOneClickDownload();
          }
        }
        break;

      case DownloadKernel.TaskType.Group:
        {
          const dir: string = path.dirname(newTaskInfo.taskBaseInfo.savePath);
          const newName: string = (await this.getTaskName(
            taskType,
            dir,
            newTaskInfo.taskBaseInfo.taskName,
            0,
            newTaskInfo.taskBaseInfo.origin
          )).newName;
          const oldRootPath: string = newTaskInfo.taskBaseInfo.savePath;
          if (newName !== newTaskInfo.taskBaseInfo.taskName) {
            newTaskInfo.taskBaseInfo.taskName = newName;
            newTaskInfo.taskBaseInfo.savePath = path.join(dir, newName);
            const savePath: string = newTaskInfo.taskBaseInfo.savePath;
            for (let i: number = 0; i < newTaskInfo.groupTaskInfo.subTaskList.length; ++i) {
              if (newTaskInfo.groupTaskInfo.subTaskList[i].p2spTaskInfo || newTaskInfo.groupTaskInfo.subTaskList[i].emuleTaskInfo) {
                // 任务组存在树状结构时，不可直接替换目录
                const subSavePath: string = newTaskInfo.groupTaskInfo.subTaskList[i].taskBaseInfo.savePath;
                newTaskInfo.groupTaskInfo.subTaskList[i].taskBaseInfo.savePath = savePath + subSavePath.substr(oldRootPath.length);
              }
            }
          }

          await this.createGroupTask(newTask, newTaskInfo.taskBaseInfo, newTaskInfo.groupTaskInfo, categoryId);
        }
        break;

      case DownloadKernel.TaskType.Magnet:
        newTask.createMagnetTask(
          newTaskInfo.isBackground,
          newTaskInfo.taskBaseInfo,
          newTaskInfo.magnetTaskInfo,
          categoryId
        );
        break;

      default:
        ret = false;
        break;
    }

    if (!ret) {
      newTask = null;
    } else {
      if (data && data.extdata) {
        data.extdata += ',createTaskEnd-tick=' + thunderHelper.getTickCount();
      }
      // 把任务添加到任务列表
      this.addTask(newTask, newTaskInfo);
      // 非后台任务才需要插入视图并且不是任务组子任务
      if (!newTaskInfo.isBackground && !isSubTask) {
        this.insertToCategoryView(newTask, DownloadKernel.TaskInsertReason.Create);
      }
      eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskCreated, [newTask]);
    }

    return newTask;
  }

  public async createTask(
    taskType: DownloadKernel.TaskType,
    newTaskInfo: DownloadKernel.INewTaskInfo
  ): Promise<DownloadKernel.ITask> {
    // 根据任务类型，设置防重入的key
    function getSingletonKey(): string {
      let key = '';
      switch (newTaskInfo.taskType) {
        case DownloadKernel.TaskType.P2sp:
          key = newTaskInfo.p2spTaskInfo.url;
          break;
        case DownloadKernel.TaskType.Emule:
          key = newTaskInfo.emuleTaskInfo.url;
          break;
        case DownloadKernel.TaskType.Bt:
          key = newTaskInfo.btTaskInfo.infoId;
          break;
        case DownloadKernel.TaskType.Magnet:
          key = newTaskInfo.magnetTaskInfo.url;
          break;
        default:
          break;
      }
      return key;
    }

    const key = getSingletonKey();
    if (key) {
      return this.singletonInstance.run(key, () => {
        return this.internalCreateTask(taskType, newTaskInfo);
      });
    } else {
      return this.internalCreateTask(taskType, newTaskInfo);
    }
  }

  private getCategoryView(categoryId: number, categoryViewID: DownloadKernel.CategroyViewID): CategoryView {
    let categoryView: CategoryView = null;
    if (categoryId !== undefined && categoryId > -1 && categoryId !== categoryManager.getZhanCategoryId()) {
      categoryView = categoryManager.getCategoryView(
        categoryId,
        categoryViewID
      ) as CategoryView;
    } else {
      categoryView = categoryViewManager.getCategoryViewFromId(
        categoryViewID
      ) as CategoryView;
    }
    return categoryView;
  }

  // 不会有bt任务
  private async internalCreateBatchTask(
    newTaskInfos: DownloadKernel.INewTaskInfo[],
    groupTask?: DownloadKernel.ITask
  ): Promise<DownloadKernel.ITask[]> {
    await this.downloadSdkInited();
    const tick: number = thunderHelper.getTickCount();
    const count: number = this.getTaskCount();
    let data: { [propName: string]: string | number; } = undefined;
    if (groupTask) {
      data = window.__performanceMap['ClickDownload_' + groupTask.getTaskName()];
      if (data && data.extdata) {
        data.extdata += ',createBatchTaskStart-tick=' + tick + ',allTaskNum=' + count;
      }
    } else {
      for (const newTaskInfo of newTaskInfos) {
        data = window.__performanceMap['ClickDownload_' +
            newTaskInfo.taskBaseInfo.gcid || newTaskInfo.emuleTaskInfo?.url.slice(0, 150) || newTaskInfo.p2spTaskInfo?.url.slice(0, 150)];
        if (data && data.extdata) {
          data.extdata += ',createBatchTaskStart-tick=' + tick + ',allTaskNum=' + count;
        }
      }
    }
    const transactionId: number = ThunderKernel.beginTransaction();

    let groupTaskId: number = undefined;
    let groupTaskPathLength: number = undefined;
    if (groupTask) {
      groupTaskId = groupTask.getId();
      const groupTaskSavePath: string = groupTask.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
      if (groupTaskSavePath) {
        groupTaskPathLength = groupTaskSavePath.length + 1;
      }
    }

    const groupSource: string = groupTask ? groupTask.getTaskAttribute(DownloadKernel.TaskAttribute.Origin) : '';
    const taskNameRenameIndex: Map<string, number> = new Map();
    let categoryId: number = undefined;
    const taskLists: DownloadKernel.ITask[] = [];
    for (let i: number = 0; i < newTaskInfos.length; ++i) {
      let ret: boolean = true;
      let newTask: Task = new Task(this);
      const newTaskInfo: DownloadKernel.INewTaskInfo = newTaskInfos[i];
      newTaskInfo.taskBaseInfo.groupTaskId = groupTaskId;
      const taskType: DownloadKernel.TaskType = newTaskInfo.taskType;
      const filePath: string = path.join(newTaskInfo.taskBaseInfo.savePath, newTaskInfo.taskBaseInfo.taskName);
      this.setGroupTaskRelativePath(newTask, newTaskInfo.taskBaseInfo.savePath, groupTaskPathLength);
      if (categoryId === undefined) {
        // 获取创建的目录id（私人空间），不支持批量创建到不同的目录。只取第一次
        categoryId = newTaskInfo.categoryId;
        if (newTaskInfo.privateSpace) {
          const category: DownloadKernel.ICategory = categoryManager.getCurrentPrivateCategory();
          if (category) {
            categoryId = category.getId();
          }
        }
      }
      // 联盟任务组只要保证文件夹不重复即可，不再对子任务进行重命名处理
      if (groupTask && groupSource?.toLowerCase() === 'ThunderUnion'.toLowerCase()) {
        // do nothing
      } else {
        let index: number = taskNameRenameIndex.get(filePath);
        if (typeof index !== 'number') {
          index = 0;
        }
        const result: ITaskNameResult = await this.getTaskName(
          taskType,
          newTaskInfo.taskBaseInfo.savePath,
          newTaskInfo.taskBaseInfo.taskName,
          index,
          newTaskInfo.taskBaseInfo.origin
        );
        taskNameRenameIndex.set(filePath, result.index + 1);
        newTaskInfo.taskBaseInfo.taskName = result.newName;
      }
      if (taskType === DownloadKernel.TaskType.P2sp) {
        newTask.createP2spTask(
          newTaskInfo.isBackground,
          newTaskInfo.taskBaseInfo,
          newTaskInfo.p2spTaskInfo,
          categoryId
        );
      } else if (taskType === DownloadKernel.TaskType.Emule) {
        newTask.createEmuleTask(
          newTaskInfo.isBackground,
          newTaskInfo.taskBaseInfo,
          newTaskInfo.emuleTaskInfo,
          categoryId
        );
      } else {
        ret = false;
      }

      if (!ret) {
        newTask = null;
      } else {
        if (groupTask) {
          (groupTask.getExtraObject() as GroupTask).addSubTask(newTask);
        }
        this.addTask(newTask, newTaskInfo);
        if (!newTaskInfo.isBackground) {
          taskLists.push(newTask);
        }
      }
    }

    ThunderKernel.commitTransaction(transactionId);
    if (groupTask) {
      data = window.__performanceMap['ClickDownload_' + groupTask.getTaskName()];
      if (data && data.extdata) {
        data.extdata += ',createBatchTaskEnd-tick=' + tick + ',allTaskNum=' + count;
      }
    } else {
      for (const newTaskInfo of newTaskInfos) {
        data = window.__performanceMap['ClickDownload_' +
            newTaskInfo.taskBaseInfo.gcid || newTaskInfo.emuleTaskInfo?.url.slice(0, 150) || newTaskInfo.p2spTaskInfo?.url.slice(0, 150)];
        if (data && data.extdata) {
          data.extdata += ',createBatchTaskEnd-tick=' + tick + ',allTaskNum=' + count;
        }
      }
    }

    if (!groupTask) {
      // 插入任务视图
      const categoryView: CategoryView = this.getCategoryView(categoryId, DownloadKernel.CategroyViewID.Downloading);
      if (categoryView) {
        categoryView.insertTask(taskLists, DownloadKernel.TaskInsertReason.Create);
      }
      eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskCreated, taskLists);
    }
    return taskLists;
  }

    // 创建任务防重入一直没处理好，很容易出现错误码212: 当前下载目录无法写入数据，请尝试下载到其它目录
  // 跟踪原因是由于getTaskName这里重入，返回了一致的文件名，此前的查重是无法查到的。
  // 但是诉求是不允许重复下载，批量任务会内部避过文件名的问题，所以只解决创建单个任务的接口
  // 所以修改原来的createBatchTask，如果只有一个任务，则调用createTask接口。
  // 构造必现场景：使得“迅雷快速申请磁盘空间助手”必然弹出，然后点击同一个链接，出来多个UAC弹框，下一步就会必现无法写入数据
  public async createBatchTask(
    newTaskInfos: DownloadKernel.INewTaskInfo[],
    groupTask?: DownloadKernel.ITask
  ): Promise<DownloadKernel.ITask[]> {
    if (!groupTask && newTaskInfos.length === 1) {
      const newTaskInfo = newTaskInfos[0];
      const task = await this.createTask(newTaskInfo.taskType, newTaskInfo);
      return task ? [task] : [];
    } else {
      return this.internalCreateBatchTask(newTaskInfos, groupTask);
    }
  }

  // 开始任务, 所有任务都是经过此方法
  // 排队等待不进入此方法
  public async startTask(taskId: number, force?: boolean): Promise<boolean> {
    // 如果已开始列表中不存在该任务,那么执行开始并加入已开始列表
    const task: Task = this.tasks.get(taskId) as Task;
    let ret: boolean = false;
    const data: { [prop: string]: string | number } = (window).__performanceMap['StartTask_' + taskId];
    if (task) {
      const groupTaskId = task.getTaskAttribute(DownloadKernel.TaskAttribute.GroupTaskId);
      const ownerTask: DownloadKernel.ITask = groupTaskId > 0 ? this.tasks.get(groupTaskId) : null;
      if (this.sdkCrash) {
        if (data && data.extdata) {
          data.extdata += ',setTaskErrorSdkCrash=' + thunderHelper.getTickCount();
        }
        task.setStatusAndErrorCode(DownloadKernel.TaskStatus.Failed, DownloadKernel.TaskError.DownloadSDKCrash);
        eventManager.fireTaskEvent(DownloadKernel.TaskEventType.StatusChanged, [task]);
        return ret;
      }

      // 先加到已开始列表
      if (this.startTasks.has(taskId)) {
        return true;
      }
      this.startTasks.add(taskId); // 可能重复调用接口，所以startTasks改成Set
      if (data && data.extdata) {
        data.extdata += ',isInitDkFinish=' + this.isInitDkSuccess;
      }
      await this.downloadSdkInited();
      if (this.isInitDkSuccess) {
        ret = await task.start(ownerTask, force);
        // 防止任务一开始就失败，未进入开始列表，导致无法queryTaskInfo
        this.taskStatusChanage([task]);
        if (ret) {
          if (data && data.extdata) {
            data.extdata += ',task.startEnd=' + thunderHelper.getTickCount();
          }
        } else {
          logger.warning('task start fail! taskId', taskId);
          // 如果开始不了，那么就从开始列表里面移除
          this.removeStartTask(task);
          if (data && data.extdata) {
            data.extdata += ',task.startFail=' + thunderHelper.getTickCount();
          }
        }
      } else {
        // 先设置为正在开始
        ret = task.updateTaskStatus(DownloadKernel.TaskStatus.StartPending);
        if (data && data.extdata) {
          data.extdata += ',updateTaskStatusToStartPending=' + thunderHelper.getTickCount();
        }
        if (ret) {
          this.taskStatusChanage([task]);
          this.downloadSdkInited()
            .then(async () => {
              // 初始化下载库完成后再次真正开始
              ret = await task.start(ownerTask, true);
              // 防止任务一开始就失败，未进入开始列表，导致无法queryTaskInfo
              this.taskStatusChanage([task]);
              if (ret) {
                if (data && data.extdata) {
                  data.extdata += ',task.startEnd=' + thunderHelper.getTickCount();
                }
              } else {
                logger.warning('task start fail! taskId', taskId);
                // 如果开始不了，那么就从开始列表里面移除
                this.removeStartTask(task);
                if (data && data.extdata) {
                  data.extdata += ',task.startEnd=' + thunderHelper.getTickCount();
                }
              }
            })
            .catch();
        }
      }
    } else {
      logger.warning('task not exist! taskId', taskId);
    }
    return ret;
  }

  // 开始任务处于排队状态
  public startWaitingTask(taskId: number): boolean {
    let ret: boolean = false;
    const task: Task = this.tasks.get(taskId) as Task;
    if (task) {
      if (task.startWaiting()) {
        this.taskStatusChanage([task]);
        ret = true;
      }
    }
    return ret;
  }

  public startWaitingTasks(taskIds: number[]): boolean {
    let ret: boolean = false;
    const transactionId: number = ThunderKernel.beginTransaction();
    const taskList: DownloadKernel.ITask[] = [];
    const tickCount: number = thunderHelper.getTickCount();
    for (const taskId of taskIds) {
      const task: Task = this.tasks.get(taskId) as Task;
      if (task) {
        if (task.startWaiting()) {
          const data: { [prop: string]: string | number } = (window).__performanceMap['StartTask_' + taskId];
          if (data && data.extdata) {
            data.extdata += ',startWaitingEnd=' + tickCount;
          }
          taskList.push(task);
          ret = true;
        }
      }
    }
    ThunderKernel.commitTransaction(transactionId);
    if (taskList.length > 0) {
      eventManager.fireTaskEvent(DownloadKernel.TaskEventType.StatusChanged, taskList);
    }
    return ret;
  }

  // 暂停任务
  public async stopTask(taskId: number, reason: DownloadKernel.TaskStopReason): Promise<void> {
    const task: Task = this.tasks.get(taskId) as Task;
    if (task) {
      if (await task.stop(reason)) {
        this.taskStatusChanage([task]);
        if (task.getTaskStatus() === DownloadKernel.TaskStatus.Stopped) {
          task.setStopReason(DownloadKernel.TaskStopReason.Unknown);
        }
      } else {
        logger.warning('task stop fail! taskId', taskId);
      }
    } else {
      logger.warning('task not exist! taskId', taskId);
    }
  }

  public async batchStopTask(taskIds: number[], reason: DownloadKernel.TaskStopReason): Promise<void> {
    const waitingTasks: Task[] = [];
    const noWaitingTasks: Task[] = [];
    for (let i: number = 0; i < taskIds.length; ++i) {
      const task: Task = this.tasks.get(taskIds[i]) as Task;
      if (task) {
        const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
        if (taskStatus === DownloadKernel.TaskStatus.StartWaiting) {
          waitingTasks.push(task);
        } else {
          noWaitingTasks.push(task);
        }
      }
    }
    const statusChangedTask: Task[] = [];
    // 先停止排队中的任务
    for (let i: number = 0; i < waitingTasks.length; ++i) {
      const task: Task = waitingTasks[i];
      if (await task.stop(reason)) {
        statusChangedTask.push(task);
      }
    }
    // 再停止非排队中的任务
    for (let i: number = 0; i < noWaitingTasks.length; ++i) {
      const task: Task = noWaitingTasks[i];
      if (await task.stop(reason)) {
        statusChangedTask.push(task);
      }
    }

    // 触发任务状态改变事件
    if (statusChangedTask.length > 0) {
      this.taskStatusChanage(statusChangedTask);
    }

    // 清掉停止原因
    for (let i: number = 0; i < statusChangedTask.length; ++i) {
      const task: Task = statusChangedTask[i];
      if (task.getTaskStatus() === DownloadKernel.TaskStatus.Stopped) {
        task.setStopReason(DownloadKernel.TaskStopReason.Unknown);
      }
    }
  }

  // 暂停任务AW版本
  public async stopTaskAW(taskId: number, reason: DownloadKernel.TaskStopReason): Promise<boolean> {
    const task: Task = this.tasks.get(taskId) as Task;
    if (task) {
      if (await task.stop(reason)) {
        this.taskStatusChanage([task]);
        if (task.getTaskStatus() === DownloadKernel.TaskStatus.Stopped) {
          task.setStopReason(DownloadKernel.TaskStopReason.Unknown);
        }
      } else {
        logger.warning('task stop fail! taskId', taskId);
      }
    } else {
      logger.warning('task not exist! taskId', taskId);
    }
    return this.attachTaskStopOnce(taskId);
  }

  // 彻底删除任务
  // deleteFile: true 删除下载文件，false 保留下载文件
  public async deleteTask(taskId: number, deleteFile: boolean, reason: DownloadKernel.TaskDeleteFileReason): Promise<boolean> {
    const task: Task = this.tasks.get(taskId) as Task;
    if (!task) {
      return false;
    }

    const categoryViewId: DownloadKernel.CategroyViewID = task.getTaskAttribute(
      DownloadKernel.TaskAttribute.CategoryViewId
    );
    if (
      categoryViewId === DownloadKernel.CategroyViewID.Downloading ||
      categoryViewId === DownloadKernel.CategroyViewID.Unkown
    ) {
      // 如果正在下载中删除任务前先停止
      await this.stopTaskAW(taskId, DownloadKernel.TaskStopReason.DeleteTask);
    }
    eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskDestroying, [task], deleteFile);
    if (task.updateTaskStatus(DownloadKernel.TaskStatus.DestroyPending)) {
      this.taskStatusChanage([task]);
    }
    // 上面stopTaskAW异步的，有可能回来的是下载完成，此时categoryView是在已完成，所以要放到后面来取categoryView
    const categoryView: CategoryView = this.getTaskCategoryView(task);
    // 从视图中移除
    const taskList: DownloadKernel.ITask[] = [task];
    if (categoryView !== null) {
      categoryView.removeTask(taskList);
    }

    // 移除任务
    this.removeTask(taskId);

    // 有可能是任务组，需要 transaction
    const transactionId: number = ThunderKernel.beginTransaction();
    // 删除数据库
    task.deleteStorageTask();
    ThunderKernel.commitTransaction(transactionId);

    let result: boolean = true;
    if (deleteFile) {
      // 删除文件
      result = (await task.deleteFile(reason)) && result;
    }

    eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskDestroyed, [task], result);
    return result;
  }

  // 批量彻底删除任务（只支持从一个视图批量删除）
  // deleteFile: true 删除下载文件，false 保留下载文件
  // 返回值，有一个失败就失败
  public async deleteBatchTask(taskIds: number[], deleteFile: boolean, reason: DownloadKernel.TaskDeleteFileReason): Promise<boolean> {
    const deleteTasks: Task[] = [];
    const stopDeleteTasks: Task[] = [];
    for (let i: number = 0; i < taskIds.length; ++i) {
      const task: Task = this.tasks.get(taskIds[i]) as Task;
      if (!task) {
        // 防止重入
        logger.warning('deleteBatchTask task', task);
        continue;
      }
      const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
      if (taskStatus === DownloadKernel.TaskStatus.StartPending || taskStatus === DownloadKernel.TaskStatus.Started) {
        // 正在下载的，需要先停止再删除
        stopDeleteTasks.push(task);
      } else {
        // 非正在下载的，直接删除
        deleteTasks.push(task);
      }
      ThunderPanTaskNS.deleteTask(null, null, task.getId());
    }

    let result: boolean = true;
    // 可能包括不同的categoryid。
    const deleteTaskCategoryMap: { [categoryId: number]: Task[] } = {};
    if (deleteTasks.length > 0) {
      eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskDestroying, deleteTasks, deleteFile);
      const statusChangedTasks: Task[] = [];
      for (let i: number = 0; i < deleteTasks.length; ++i) {
        const task: Task = deleteTasks[i];
        const categoryId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId);
        if (!deleteTaskCategoryMap[categoryId]) {
          deleteTaskCategoryMap[categoryId] = [];
        }
        deleteTaskCategoryMap[categoryId].push(task);

        if (task.updateTaskStatus(DownloadKernel.TaskStatus.DestroyPending)) {
          // this.taskStatusChanage(task);
          statusChangedTasks.push(task);
        }
      }
      if (statusChangedTasks.length > 0) {
        eventManager.fireTaskEvent(DownloadKernel.TaskEventType.StatusChanged, statusChangedTasks);
      }

      for (const categoryId in deleteTaskCategoryMap) {
        if (!deleteTaskCategoryMap[categoryId]) {
          continue;
        }
        const categoryView: CategoryView = this.getTaskCategoryView(deleteTaskCategoryMap[categoryId][0]);
        // 从视图中移除
        if (categoryView !== null) {
          categoryView.removeTask(deleteTasks);
        }
      }

      const transactionId: number = ThunderKernel.beginTransaction();
      for (let i: number = 0; i < deleteTasks.length; ++i) {
        const task: Task = deleteTasks[i];
        // 移除任务
        this.removeTask(task.getId());
        // 删除数据库记录
        task.deleteStorageTask();
      }
      ThunderKernel.commitTransaction(transactionId);

      // 删除文件
      if (deleteFile) {
        for (let i: number = 0; i < deleteTasks.length; ++i) {
          const task: Task = deleteTasks[i];
          // 如果文件有关联的快捷方式，一并删除
          const taskExtra: DownloadKernel.IGroupTask = task.getExtraObject() as DownloadKernel.IGroupTask;
          const createShortcutStr: string = taskExtra.getUserData(
            TaskUserDataGUID.guidCreateShortcut,
            'createShortcut'
          ) as string;
          let createShortcut: { name: string } | null = null;
          if (createShortcutStr) {
            try {
              createShortcut = JSON.parse(createShortcutStr);
            } catch (error) {
              logger.error(error);
            }
          }
          if (createShortcut) {
            const homedir: string = os.homedir();
            fs.unlink(path.resolve(homedir, 'Desktop', `${createShortcut.name}.lnk`), (err: Error) => {
              if (err) {
                logger.error(err);
              }
            });
          }
          result = (await task.deleteFile(reason)) && result;
        }
      }

      eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskDestroyed, deleteTasks, result);
    }
    if (stopDeleteTasks.length > 0) {
      await new Promise<void>(
        (resolve: () => void): void => {
          let stopCount: number = 0;
          for (let i: number = 0; i < stopDeleteTasks.length; ++i) {
            const task: Task = stopDeleteTasks[i];
            const taskId: number = task.getId();
            // 如果正在下载中删除任务前先停止
            this.stopTaskAW(taskId, DownloadKernel.TaskStopReason.DeleteTask)
              .then(async () => {
                ++stopCount;
                eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskDestroying, [task], deleteFile);
                if (task.updateTaskStatus(DownloadKernel.TaskStatus.DestroyPending)) {
                  this.taskStatusChanage([task]);
                }
                // 上面stopTaskAW异步的，有可能回来的是下载完成，此时categoryView是在已完成，所以要放到后面来取categoryView
                const categoryView: CategoryView = this.getTaskCategoryView(task);
                // 从视图中移除
                if (categoryView !== null) {
                  categoryView.removeTask([task]);
                }

                // 移除任务
                this.removeTask(taskId);

                // 有可能是任务组，需要 transaction
                const transactionId: number = ThunderKernel.beginTransaction();
                // 删除数据库
                task.deleteStorageTask();
                ThunderKernel.commitTransaction(transactionId);

                if (deleteFile) {
                  // 删除文件
                  result = (await task.deleteFile(reason)) && result;
                }

                eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskDestroyed, [task], result);
                if (stopCount === stopDeleteTasks.length) {
                  resolve();
                }
              })
              .catch();
          }
        }
      );
    }
    return result;
  }

  // 删除到垃圾箱
  public async removeToRecycle(taskId: number): Promise<void> {
    const task: Task = this.tasks.get(taskId) as Task;
    if (task) {
      if (task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId) !== -1) {
        this.deleteTask(taskId, true, DownloadKernel.TaskDeleteFileReason.DeleteTask).catch();
        return;
      }
      let categoryViewId: DownloadKernel.CategroyViewID = task.getTaskAttribute(
        DownloadKernel.TaskAttribute.CategoryViewId
      );
      if (categoryViewId === DownloadKernel.CategroyViewID.Completed) {
        // 已完成的任务直接删除
        const completeView: CategoryView = categoryViewManager.getCategoryViewFromId(
          DownloadKernel.CategroyViewID.Completed
        ) as CategoryView;
        const taskList: DownloadKernel.ITask[] = [task];
        // 从已完成视图移除
        completeView.removeTask(taskList);
      } else {
        // 删除任务前先停止
        await this.stopTaskAW(taskId, DownloadKernel.TaskStopReason.DeleteTask);
        // 异步停止有可能变成下载完成，此时要重取下视图id
        categoryViewId = task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryViewId);
        if (categoryViewId === DownloadKernel.CategroyViewID.Completed) {
          // 上面stopTaskAW异步回来是下载完成了
          const completeView: CategoryView = categoryViewManager.getCategoryViewFromId(
            DownloadKernel.CategroyViewID.Completed
          ) as CategoryView;
          const taskList: DownloadKernel.ITask[] = [task];
          // 从已完成视图移除
          completeView.removeTask(taskList);
        } else {
          const downloadingView: CategoryView = categoryViewManager.getCategoryViewFromId(
            DownloadKernel.CategroyViewID.Downloading
          ) as CategoryView;
          const taskList: DownloadKernel.ITask[] = [task];
          // 从正在下载视图移除
          downloadingView.removeTask(taskList);
          this.removeStartTask(task);
        }
      }

      task.moveToRecycle();

      // 放到垃圾箱
      const recycleView: CategoryView = categoryViewManager.getCategoryViewFromId(
        DownloadKernel.CategroyViewID.Recycle
      ) as CategoryView;
      const taskList: DownloadKernel.ITask[] = [];
      taskList.push(task);
      recycleView.insertTask(taskList, DownloadKernel.TaskInsertReason.Recycle);
    }
  }

  // 批量删除到垃圾箱，只支持从一个视图批量删除, 不应该允许categoryid为-1之外的任务删除到回收站
  public async batchRemoveToRecycle(taskIds: number[]): Promise<void> {
    const removeTasks: Task[] = [];
    const stopRemoveTasks: Task[] = [];
    const destroyTasks: number[] = [];
    for (let i: number = 0; i < taskIds.length; ++i) {
      const task: Task = this.tasks.get(taskIds[i]) as Task;
      const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
      if (task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId) !== -1) {
        destroyTasks.push(taskIds[i]);
        continue;
      }
      if (taskStatus === DownloadKernel.TaskStatus.StartPending || taskStatus === DownloadKernel.TaskStatus.Started) {
        // 正在下载的，需要先停止再删除
        stopRemoveTasks.push(task);
      } else {
        // 非正在下载的，直接删除
        removeTasks.push(task);
      }
    }
    if (destroyTasks.length) {
      this.deleteBatchTask(destroyTasks, true, DownloadKernel.TaskDeleteFileReason.DeleteTask).catch();
    }
    if (removeTasks.length > 0) {
      for (let task of removeTasks) {
        if (task.getTaskStatus() === DownloadKernel.TaskStatus.StartWaiting) {
          await task.stop(DownloadKernel.TaskStopReason.DeleteTask);
        }
      }
      // 先从视图删除
      const catetoryView: CategoryView = this.getTaskCategoryView(removeTasks[0]);
      catetoryView.removeTask(removeTasks);
      let transactionId: number = undefined;
      if (removeTasks.length > 1) {
        transactionId = ThunderKernel.beginTransaction();
      }
      // 改数据库
      removeTasks.forEach(
        (task: Task): void => {
          task.moveToRecycle();
        }
      );
      if (transactionId !== undefined) {
        ThunderKernel.commitTransaction(transactionId);
      }
      // 添加进垃圾箱视图
      const recycleView: CategoryView = categoryViewManager.getCategoryViewFromId(
        DownloadKernel.CategroyViewID.Recycle
      ) as CategoryView;
      recycleView.insertTask(removeTasks, DownloadKernel.TaskInsertReason.Recycle);
    }
    if (stopRemoveTasks.length > 0) {
      stopRemoveTasks.forEach(
        async (task: Task): Promise<void> => {
          const taskId: number = task.getId();
          // 删除任务前先停止
          await this.stopTaskAW(taskId, DownloadKernel.TaskStopReason.DeleteTask);
          const categoryViewId: DownloadKernel.CategroyViewID = task.getTaskAttribute(
            DownloadKernel.TaskAttribute.CategoryViewId
          );
          if (categoryViewId === DownloadKernel.CategroyViewID.Completed) {
            // 上面stopTaskAW异步回来是下载完成了
            const completeView: CategoryView = categoryViewManager.getCategoryViewFromId(
              DownloadKernel.CategroyViewID.Completed
            ) as CategoryView;
            const taskList: DownloadKernel.ITask[] = [task];
            // 从已完成视图移除
            completeView.removeTask(taskList);
          } else {
            const downloadingView: CategoryView = categoryViewManager.getCategoryViewFromId(
              DownloadKernel.CategroyViewID.Downloading
            ) as CategoryView;
            // 从正在下载视图移除
            downloadingView.removeTask([task]);
            this.removeStartTask(task);
          }
          task.moveToRecycle();
          // 放到垃圾箱
          const recycleView: CategoryView = categoryViewManager.getCategoryViewFromId(
            DownloadKernel.CategroyViewID.Recycle
          ) as CategoryView;
          recycleView.insertTask([task], DownloadKernel.TaskInsertReason.Recycle);
        }
      );
    }
  }

  public recoverFromRecycle(taskId: number): boolean {
    let recycleTime: number = 0;
    const task: Task = this.tasks.get(taskId) as Task;
    if (task) {
      recycleTime = task.getTaskAttribute(DownloadKernel.TaskAttribute.RecycleTime);
      if (recycleTime > 0) {
        if (task.recover()) {
          const recycleView: CategoryView = categoryViewManager.getCategoryViewFromId(
            DownloadKernel.CategroyViewID.Recycle
          ) as CategoryView;
          const taskList: DownloadKernel.ITask[] = [];
          taskList.push(task);
          recycleView.removeTask(taskList);
        }

        const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
        if (taskStatus === DownloadKernel.TaskStatus.Succeeded || taskStatus === DownloadKernel.TaskStatus.Seeding) {
          const completeView: CategoryView = categoryViewManager.getCategoryViewFromId(
            DownloadKernel.CategroyViewID.Completed
          ) as CategoryView;
          const taskList: DownloadKernel.ITask[] = [];
          taskList.push(task);
          completeView.insertTask(taskList, DownloadKernel.TaskInsertReason.Recover);
        } else {
          const downloadingView: CategoryView = categoryViewManager.getCategoryViewFromId(
            DownloadKernel.CategroyViewID.Downloading
          ) as CategoryView;
          const taskList: DownloadKernel.ITask[] = [];
          taskList.push(task);
          downloadingView.insertTask(taskList, DownloadKernel.TaskInsertReason.Recover);
        }
      }
    }

    return recycleTime > 0;
  }

  public batchRecoverFromRecycle(taskIds: number[]): void {
    const recycleTaskList: DownloadKernel.ITask[] = [];
    const completeTaskList: DownloadKernel.ITask[] = [];
    const downloadingTaskList: DownloadKernel.ITask[] = [];

    const transactionId: number = ThunderKernel.beginTransaction();
    for (const taskId of taskIds) {
      const task: Task = this.tasks.get(taskId) as Task;
      if (task) {
        const recycleTime: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.RecycleTime);
        if (recycleTime > 0) {
          if (task.recover()) {
            recycleTaskList.push(task);
          }

          const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
          if (taskStatus === DownloadKernel.TaskStatus.Succeeded || taskStatus === DownloadKernel.TaskStatus.Seeding) {
            completeTaskList.push(task);
          } else {
            downloadingTaskList.push(task);
          }
        }
      }
    }
    ThunderKernel.commitTransaction(transactionId);

    if (recycleTaskList.length > 0) {
      const recycleView: CategoryView = categoryViewManager.getCategoryViewFromId(
        DownloadKernel.CategroyViewID.Recycle
      ) as CategoryView;
      recycleView.removeTask(recycleTaskList);
    }

    if (completeTaskList.length > 0) {
      const completeView: CategoryView = categoryViewManager.getCategoryViewFromId(
        DownloadKernel.CategroyViewID.Completed
      ) as CategoryView;
      completeView.insertTask(completeTaskList, DownloadKernel.TaskInsertReason.Recover);
    }

    if (downloadingTaskList.length > 0) {
      const downloadingView: CategoryView = categoryViewManager.getCategoryViewFromId(
        DownloadKernel.CategroyViewID.Downloading
      ) as CategoryView;
      downloadingView.insertTask(downloadingTaskList, DownloadKernel.TaskInsertReason.Recover);
    }
  }

  public async forcedFailureTask(taskId: number, errorCode: number = 5): Promise<boolean> {
    const task: Task = this.tasks.get(taskId) as Task;
    if (!task) {
      return false;
    }

    const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
    if (
      taskStatus === DownloadKernel.TaskStatus.Succeeded ||
      taskStatus === DownloadKernel.TaskStatus.Seeding ||
      taskStatus === DownloadKernel.TaskStatus.Failed
    ) {
      return false;
    }

    const categoryViewId: DownloadKernel.CategroyViewID = task.getTaskAttribute(
      DownloadKernel.TaskAttribute.CategoryViewId
    );
    if (
      categoryViewId === DownloadKernel.CategroyViewID.Downloading ||
      categoryViewId === DownloadKernel.CategroyViewID.Unkown
    ) {
      // 如果正在下载中删除任务前先停止
      await this.stopTaskAW(taskId, DownloadKernel.TaskStopReason.ForceFailure);
    }
    task.setStatusAndErrorCode(DownloadKernel.TaskStatus.Failed, errorCode, true);
    eventManager.fireTaskEvent(DownloadKernel.TaskEventType.StatusChanged, [task]);
    eventManager.fireTaskEvent(DownloadKernel.TaskEventType.DetailChanged, [task], DownloadKernel.TaskDetailChangedFlags.ErrorCode);
    return true;
  }

  public async reDownloadTask(
    taskId: number,
    newTaskInfo?: DownloadKernel.INewTaskInfo
  ): Promise<DownloadKernel.ITask> {
    let newTask: DownloadKernel.ITask = null;

    do {
      const task: DownloadKernel.ITask = this.tasks.get(taskId);
      if (!task) {
        break;
      }

      // 重新下载先把原来的删除
      await this.deleteTask(taskId, true, DownloadKernel.TaskDeleteFileReason.ReDownload);

      const oldTaskExtra: DownloadKernel.ITaskExtra = task.getExtraObject();
      const userData: string = oldTaskExtra.getAllUserData();
      const fileSize: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.FileSize);
      if (fileSize > 0) {
        newTaskInfo.taskBaseInfo.fileSize = fileSize;
      }

      const taskType: DownloadKernel.TaskType = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
      switch (taskType) {
        case DownloadKernel.TaskType.P2sp:
          newTask = await this.redownloadP2spTask(task, newTaskInfo);
          break;

        case DownloadKernel.TaskType.Bt:
          newTask = await this.redownloadBtTask(task, newTaskInfo);
          break;

        case DownloadKernel.TaskType.Emule:
          newTask = await this.redownloadEmuleTask(task, newTaskInfo);
          break;

        case DownloadKernel.TaskType.Group:
          newTask = await this.redownloadGroupTask(task, newTaskInfo);
          break;

        default:
          break;
      }
      if (newTask) {
        const taskExtra: DownloadKernel.ITaskExtra = newTask.getExtraObject();
        taskExtra.setAllUserData(userData);
      }
    } while (0);

    return newTask;
  }

  public async moveTaskToPath(taskId: number, newPath: string, move2Private?: boolean,
    banToast?: boolean, //是否禁止“移动成功”的toast，纯需求驱动改进，从12版本开始，移入到私人空间的toast条有个“查看”按钮，点击后要定位到具体的category，
    ): Promise<boolean> {
    let ret: boolean = false;
    const task: DownloadKernel.ITask = this.getTaskFromTaskId(taskId);
    do {
      if (!task) {
        break;
      }

      const savePath: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
      if (!newPath || newPath === '' || newPath === savePath) {
        break;
      }

      let taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
      if (
        taskStatus === DownloadKernel.TaskStatus.Started ||
        taskStatus === DownloadKernel.TaskStatus.StartPending ||
        taskStatus === DownloadKernel.TaskStatus.StartWaiting
      ) {
        // 等待中的任务移动后也开始任务（是否排队，取决当前任务调度资源） 20190429
        ret = await this.stopTaskAW(taskId, DownloadKernel.TaskStopReason.MoveTask);
        taskStatus = task.getTaskStatus();
        // 停止后可能变成下载完成
        if (taskStatus === DownloadKernel.TaskStatus.Succeeded || taskStatus === DownloadKernel.TaskStatus.Seeding) {
          ret = await this.moveTask(taskId, newPath, false, move2Private, banToast);
        } else {
          ret = await this.moveTask(taskId, newPath, true, move2Private, banToast);
        }
      } else {
        ret = await this.moveTask(taskId, newPath, false, move2Private, banToast);
      }
    } while (0);

    const taskList: DownloadKernel.ITask[] = [];
    if (ret) {
      taskList[0] = task;
    } else {
      taskList[0] = null;
    }

    // 通知界面
    eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskMovedEnd, taskList);
    return ret;
  }

  public moveCategoryTask(taskId: number, categoryId: number): boolean {
    let ret: boolean = false;
    const task: DownloadKernel.ITask = this.getTaskFromTaskId(taskId);
    do {
      if (!task) {
        break;
      }
      const oldCategoryId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId);
      if (categoryId === undefined || oldCategoryId === categoryId) {
        break;
      }
      const categoryViewId: DownloadKernel.CategroyViewID = task.getTaskAttribute(
        DownloadKernel.TaskAttribute.CategoryViewId
      );
      const oldCategoryView: CategoryView = this.getCategoryView(oldCategoryId, categoryViewId);
      if (!oldCategoryView) {
        break;
      }
      const newCategoryView: CategoryView = this.getCategoryView(categoryId, categoryViewId);
      if (!newCategoryView) {
        break;
      }
      const taskList: DownloadKernel.ITask[] = [];
      taskList.push(task);
      oldCategoryView.removeTask(taskList);
      task.setCategoryId(categoryId);
      if (task.getType() === DownloadKernel.TaskType.Group) {
        // 任务组要设置子任务的 categoryId
        const groupTask: GroupTask = task.getExtraObject() as GroupTask;
        const taskCount: number = groupTask.getSubTaskCount();
        for (let i: number = 0; i < taskCount; ++i) {
          const subTask: DownloadKernel.ITask = groupTask.getSubTaskByIndex(i);
          if (subTask) {
            subTask.setCategoryId(categoryId);
          }
        }
      }
      newCategoryView.insertTask(taskList, DownloadKernel.TaskInsertReason.MoveThoughCategory);
      ret = true;
    } while (0);

    const taskList: DownloadKernel.ITask[] = [];
    if (ret) {
      taskList[0] = task;
    } else {
      taskList[0] = null;
    }

    // 通知界面
    eventManager.fireTaskEvent(DownloadKernel.TaskEventType.TaskCategoryMovedEnd, taskList);
    return ret;
  }

  public getTaskCount(): number {
    return this.tasks.size;
  }

  public getTaskFromTaskId(taskId: number): DownloadKernel.ITask {
    return this.tasks.get(taskId);
  }

  // 拼接好sql后用此方法获取tasks, sql长度必须要在5w字符以下
  private async batchFindTasksBySql(sql: string): Promise<DownloadKernel.ITask[]> {
    if (sql.length > 50000) {
      return [];
    }
    return new Promise<DownloadKernel.ITask[]>(
      (resolve: (value?: DownloadKernel.ITask[] | PromiseLike<DownloadKernel.ITask[]>) => void): void => {
        ThunderKernel.querySqliteEx(sql, (result: number, infos: DownloadKernel.IKeyValue[][], msg: string) => {
          if (result !== 0 || msg !== '') {
            resolve([]);
          }
          const tasks: DownloadKernel.ITask[] = [];
          for (const info of infos) {
            if (info && info[0]) {
              const task: DownloadKernel.ITask = this.getTaskFromTaskId(Number(info[0].value));
              if (task && !this.isOtherPrivateSpaceTask(task)) {
                tasks.push(task);
              }
            }
          }
          resolve(tasks);
        });
      });
  }

  // 查重
  public async batchFindP2spTask(urls: string[]): Promise<DownloadKernel.ITask[]> {
    let sql: string = 'select taskid from P2spTask where ';
    let tasks: DownloadKernel.ITask[] = [];
    for (const url of urls) {
      // 以前的代码中会在存url时多加一个空格, sql语句不允许带前后空格的字符串查找, 因此只能用like
      const condision: string = `DisplayUrl like "${url.trim()}" or `;
      if (sql.length + condision.length >= 50000) {
        const tempTasks: DownloadKernel.ITask[] = await this.batchFindTasksBySql(sql.slice(0, sql.length - 3));
        tasks = tasks.concat(tempTasks);
        sql = 'select taskid from P2spTask where ';
      }
      sql += condision;
    }
    const tempTasks: DownloadKernel.ITask[] = await this.batchFindTasksBySql(sql.slice(0, sql.length - 3));
    tasks = tasks.concat(tempTasks);
    return tasks;
  }

  // emule任务查重, 可以使用select * from emuleTask WHERE FileHash = X'2EADB63CDA2C63CD1BAF654B8B9935F8'; 这种方式查找blob
  public async batchFindEmuleTask(fileHashs: string[]): Promise<DownloadKernel.ITask[]> {
    let sql: string = 'select taskid from EmuleTask WHERE ';
    let tasks: DownloadKernel.ITask[] = [];
    for (const fileHash of fileHashs) {
      const condision: string = `fileHash = X'${fileHash}' or `;
      if (sql.length + condision.length >= 50000) {
        const tempTasks: DownloadKernel.ITask[] = await this.batchFindTasksBySql(sql.slice(0, sql.length - 3));
        tasks = tasks.concat(tempTasks);
        sql = 'select taskid from EmuleTask WHERE ';
      }
      sql += condision;
    }
    const tempTasks: DownloadKernel.ITask[] = await this.batchFindTasksBySql(sql.slice(0, sql.length - 3));
    tasks = tasks.concat(tempTasks);
    return tasks;
  }

  public async batchFindBtTask(infoIds: string[]): Promise<DownloadKernel.ITask[]> {
    let sql: string = 'select taskid from BtTask where ';
    let tasks: DownloadKernel.ITask[] = [];
    for (const infoId of infoIds) {
      // infoId 是blob类型, 这里必须是单引号
      const condision: string = `InfoId = X'${infoId}' or `;
      if (sql.length + condision.length >= 50000) {
        const tempTasks: DownloadKernel.ITask[] = await this.batchFindTasksBySql(sql.slice(0, sql.length - 3));
        tasks = tasks.concat(tempTasks);
        sql = 'select taskid from BtTask where ';
      }
      sql += condision;
    }
    const tempTasks: DownloadKernel.ITask[] = await this.batchFindTasksBySql(sql.slice(0, sql.length - 3));
    tasks = tasks.concat(tempTasks);
    return tasks;
  }

  public async batchFindBtTaskByPath(savePath: string, taskNames: string[]): Promise<DownloadKernel.ITask[]> {
    return new Promise<DownloadKernel.ITask[]>(
      (resolve: (value?: DownloadKernel.ITask[] | PromiseLike<DownloadKernel.ITask[]>) => void): void => {
        savePath = savePath.trim();
        const condisions: string[] = [];
        let savePathBlash = savePath;
        if (savePath.endsWith('\\')) {
          savePathBlash = savePath.slice(0, savePath.length - 1);
        } else {
          savePathBlash = savePath + '\\'; // 兼容存储路径尾巴斜杠的问题
        }
        for (const taskName of taskNames) {
          // name必须用like
          condisions.push(`(Name like "${taskName.trim()}" and SavePath like "${savePath}") or (Name like "${taskName.trim()}" and SavePath like "${savePathBlash}")`);
        }
        const sql: string = 'select taskid from TaskBase where type = 2 and (' + condisions.join(' or ') + ')';
        ThunderKernel.querySqliteEx(sql, (result: number, infos: DownloadKernel.IKeyValue[][], msg: string) => {
          if (result !== 0 || msg !== '') {
            resolve(null);
          }
          const taskIds: number[] = [];
          for (const info of infos) {
            if (info && info[0]) {
              taskIds.push(Number(info[0].value));
            }
          }
          const tasks: DownloadKernel.ITask[] = [];
          for (const taskid of taskIds) {
            const task: DownloadKernel.ITask = this.getTaskFromTaskId(taskid);
            if (task && !this.isOtherPrivateSpaceTask(task)) {
              tasks.push(task);
            }
          }
          resolve(tasks);
        });
      }
    );
  }

  public batchFindMagnetTask(urls: string[]): DownloadKernel.ITask[] {
    const tasks: DownloadKernel.ITask[] = [];
    do {
      const magnetKeys: string[] = [];
      for (let i: number = 0; i < urls.length; ++i) {
        let url: string = urls[i];
        // 解码
        if (Common.isThunderPrivateUrl(url)) {
          url = Common.parseThunderPrivateUrl(url);
        }
        try {
          url = decodeURI(url);
        } catch (error) {
          logger.warning(error);
        }
        if (!url) {
          continue;
        }
        // 去掉专用链后面的==或者=
        magnetKeys.push(this.splitLastChar(url).toLowerCase());
      }
      if (magnetKeys.length <= 0) {
        break;
      }
      for (let i: number = 0; i < magnetKeys.length; ++i) {
        const magnetUrl: string = magnetKeys[i];
        for (const [, task] of this.tasks) {
          // 非私人空间任务或者是当前私人空间的任务才返回结果
          if (!this.isOtherPrivateSpaceTask(task)) {
            const taskType: DownloadKernel.TaskType = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
            if (taskType === DownloadKernel.TaskType.Magnet) {
              let url: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskUrl) as string;
              // 解码
              if (Common.isThunderPrivateUrl(url)) {
                url = Common.parseThunderPrivateUrl(url);
              }
              try {
                url = decodeURI(url);
              } catch (error) {
                logger.warning(error);
              }
              if (url && magnetUrl === this.splitLastChar(url).toLowerCase()) {
                // 找到了
                tasks.push(task);
                break;
              }
            }
          }
        }
      }
    } while (0);
    return tasks;
  }

  public async batchFindGroupTask(taskNames: string[]): Promise<DownloadKernel.ITask[]> {
    return new Promise<DownloadKernel.ITask[]>(
      (resolve: (value?: DownloadKernel.ITask[] | PromiseLike<DownloadKernel.ITask[]>) => void): void => {
        const condisions: string[] = [];
        for (const taskName of taskNames) {
          // name必须用like
          condisions.push(`Name like "${taskName.trim()}"`);
        }
        const sql: string = 'select taskid from TaskBase where type = 4 and (' + condisions.join(' or ') + ')';
        ThunderKernel.querySqliteEx(sql, (result: number, infos: DownloadKernel.IKeyValue[][], msg: string) => {
          if (result !== 0 || msg !== '') {
            resolve(null);
          }
          const taskIds: number[] = [];
          for (const info of infos) {
            if (info && info[0]) {
              taskIds.push(Number(info[0].value));
            }
          }
          const tasks: DownloadKernel.ITask[] = [];
          for (const taskid of taskIds) {
            const task: DownloadKernel.ITask = this.getTaskFromTaskId(taskid);
            if (task && !this.isOtherPrivateSpaceTask(task)) {
              tasks.push(task);
            }
          }
          resolve(tasks);
        });
      }
    );
  }

  /**
   * 批量查找任务 batchFindTaskAsync
   *
   * @param {DownloadKernel.IKeyInfo[]} keyInfos
   *        要查找的IKeyInfo列表
   * @param {(tasks: DownloadKernel.ITask[]) => void} callback
   *        返回查到到的task列表
   */
  public async batchFindTaskAsync(keyInfos: DownloadKernel.IKeyInfo[]): Promise<DownloadKernel.ITask[]> {
    const p2spKeys: string[] = [];
    const p2spBackgroundKeys: { [key: string]: boolean } = {};
    const emuleKeys: string[] = [];
    const btKeys: string[] = [];
    const magnetKeys: string[] = [];
    const groupKeys: string[] = [];
    for (let i: number = 0; i < keyInfos.length; ++i) {
      const keyInfo: DownloadKernel.IKeyInfo = keyInfos[i];
      const keyValue: string = keyInfo.keyValue;
      if (keyValue === undefined) {
        continue;
      }
      if (keyInfo.keyType === DownloadKernel.KeyType.P2spUrl) {
        p2spKeys.push(keyValue);
        if (keyInfo.keyFilter === DownloadKernel.KeyFilter.All || keyInfo.keyFilter === DownloadKernel.KeyFilter.CommonBackground) {
          p2spBackgroundKeys[keyValue.toLowerCase()] = true;
        }
      } else if (keyInfo.keyType === DownloadKernel.KeyType.EmuleFileHash) {
        emuleKeys.push(keyValue);
      } else if (keyInfo.keyType === DownloadKernel.KeyType.BtInfoId) {
        btKeys.push(keyValue);
      } else if (keyInfo.keyType === DownloadKernel.KeyType.MagnetUrl) {
        magnetKeys.push(keyValue);
      } else if (keyInfo.keyType === DownloadKernel.KeyType.GroupTaskName) {
        groupKeys.push(keyValue);
      }
    }

    let ret: DownloadKernel.ITask[] = [];
    if (
      p2spKeys.length <= 0 &&
      emuleKeys.length <= 0 &&
      btKeys.length <= 0 &&
      magnetKeys.length <= 0 &&
      groupKeys.length <= 0
    ) {
      return ret;
    }
    if (p2spKeys.length > 0) {
      const p2spResult: DownloadKernel.ITask[] = await this.batchFindP2spTask(p2spKeys);
      if (p2spResult && p2spResult.length > 0) {
        ret = ret.concat(p2spResult);
      }
    }
    if (Object.keys(p2spBackgroundKeys).length) {
      this.backgroundTasks.forEach((id: number) => {
        const task = this.tasks.get(id);
        const url = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskUrl)?.toLowerCase();
        if (p2spBackgroundKeys[url]) {
          ret.push(task);
        }
      });
    }
    if (emuleKeys.length > 0) {
      const emuleResult: DownloadKernel.ITask[] = await this.batchFindEmuleTask(emuleKeys);
      if (emuleResult && emuleResult.length > 0) {
        ret = ret.concat(emuleResult);
      }
    }
    if (btKeys.length > 0) {
      const btResult: DownloadKernel.ITask[] = await this.batchFindBtTask(btKeys);
      if (btResult && btResult.length > 0) {
        ret = ret.concat(btResult);
      }
    }
    if (magnetKeys.length > 0) {
      const magnetResult: DownloadKernel.ITask[] = this.batchFindMagnetTask(magnetKeys);
      if (magnetResult && magnetResult.length > 0) {
        ret = ret.concat(magnetResult);
      }
    }
    if (groupKeys.length > 0) {
      const groupResult: DownloadKernel.ITask[] = await this.batchFindGroupTask(groupKeys);
      if (groupResult && groupResult.length > 0) {
        ret = ret.concat(groupResult);
      }
    }
    logger.information('batchFindTaskAsync keyInfos:', keyInfos, ', ret:', ret);
    return ret;
  }

  public isLoadStorageTaskFinish(): boolean {
    return this.isLoadStorageFinish;
  }

  public getTotalDownloadSpeed(): number {
    return this.totalDownloadSpeed;
  }

  public getTotalUploadSpeed(): number {
    return this.totalUploadSpeed;
  }

  public getTotalDownloadSize(): number {
    let ret: number = 0;
    if (this.lastGlobalStat && this.lastGlobalStat.recvdBytes) {
      ret = this.lastGlobalStat.recvdBytes;
    }
    if (ret < 0) {
      ret = 0;
    }
    return ret;
  }

  public getTotalUploadSize(): number {
    let ret: number = 0;
    if (this.lastGlobalStat && this.lastGlobalStat.sentBytes) {
      ret = this.lastGlobalStat.sentBytes;
    }
    if (ret < 0) {
      ret = 0;
    }
    return ret;
  }

  public attachListener(event: string | symbol, listener: (...args: any[]) => void): void {
    do {
      if (event === NodeEventMesssageNS.eventTaskDataBaseLoadEnd) {
        if (this.isLoadStorageFinish) {
          listener(this.isLoadStorageFinish);
          break;
        }
        this.attachOnceListener(event, listener);
        break;
      } else if (event === NodeEventMesssageNS.eventDownloadKernelInitEnd) {
        if (this.isInitDkFinish) {
          listener(this.isInitDkSuccess);
          break;
        }
        this.attachOnceListener(event, listener);
        break;
      }

      this.eventContainer.attachListener(event, listener);
    } while (0);
  }

  public attachOnceListener(event: string | symbol, listener: (...args: any[]) => void): void {
    this.eventContainer.attachOnceListener(event, listener);
  }

  public detachListener(event: string | symbol, listener: (...args: any[]) => void): void {
    this.eventContainer.detachListener(event, listener);
  }

  // queryTaskInfo 1s 执行一次，查询回来的数据写sql时，也是按照1s一次的事务提交
  public queryTaskInfo(): void {
    let queryedCount: number = 0;
    const totalCount: number = this.startTasks.size;
    let transactionId: number = undefined;
    if (totalCount > 0) {
      transactionId = ThunderKernel.beginTransaction();
    }
    this.startTasks.forEach((taskId: number) => {
      const task: Task = this.tasks.get(taskId) as Task;
      let queryResult: boolean = false;
      if (task) {
        queryResult = task.queryTaskInfo(
          (targetTask: DownloadKernel.ITask) => {
            this.taskStatusChanage([targetTask]);
          },
          (targetTask: DownloadKernel.ITask, taskDetailChangedFlags: DownloadKernel.TaskDetailChangedFlags) => {
            this.taskDetailChanage([targetTask], taskDetailChangedFlags);
          },
          () => {
            // 查询完成
            queryedCount++;
            if (queryedCount === totalCount) {
              ThunderKernel.commitTransaction(transactionId);
            }
          }
        );
      }
      if (!queryResult) {
        queryedCount++;
        if (queryedCount === totalCount) {
          ThunderKernel.commitTransaction(transactionId);
        }
      }
    });

    // 查询全局流量信息(下载、上传总速度)
    ThunderKernel.queryGlobalInfo(0, (stat: DownloadKernel.IGlobalStat) => {
      if (this.lastGlobalStat && stat) {
        // 每1秒查询一次，所以速度近似等于流量差
        let now = (new Date).getTime();
        if (this.lastGlobalStat.recvdBytes !== undefined && stat.recvdBytes !== undefined && this.lastGlobalTime > 0) {
          let totalDownloadSpeed: number = stat.recvdBytes - this.lastGlobalStat.recvdBytes;
          if (totalDownloadSpeed < 0) {
            totalDownloadSpeed = 0;
          }
          if (this.totalDownloadSpeed !== totalDownloadSpeed) {
            this.eventContainer.fireEvent(NodeEventMesssageNS.eventGlobalDownloadSpeedChanged, totalDownloadSpeed);
            this.totalDownloadSpeed = totalDownloadSpeed;
          }
        } else {
          this.totalDownloadSpeed = 0;
        }

        if (this.lastGlobalStat.sentBytes !== undefined && stat.sentBytes !== undefined) {
          let totalUploadSpeed: number = stat.sentBytes - this.lastGlobalStat.sentBytes;
          if (totalUploadSpeed < 0) {
            totalUploadSpeed = 0;
          }
          if (this.totalUploadSpeed !== totalUploadSpeed) {
            this.eventContainer.fireEvent(NodeEventMesssageNS.eventGlobalUploadSpeedChanged, totalUploadSpeed);
            this.totalUploadSpeed = totalUploadSpeed;
          }
        } else {
          this.totalUploadSpeed = 0;
        }

        if (stat) {
          this.lastGlobalStat = stat;
          this.lastGlobalTime = now;
        }
      }
    });
  }

  public loadTaskBase(): void {
    const data: { [propName: string]: string | number; } = window.__performanceMap['StartupTime'];
    data.extdata += ',loadTaskBaseStart=' + new Date().getTime();
    ThunderKernel.loadTaskBase((taskBaseList: DownloadKernel.ITaskBase[]) => {
      this.loadTaskBaseCallback(taskBaseList).catch();
    });
  }

  public async setGlobalExtInfo(installChannel: string): Promise<void> {
    const { LoginHelperNS } = await import('@/main-renderer/common/thunder-login/thunder-login'); // tslint:disable-line
    const isLogin: boolean = await LoginHelperNS.loginHelper.isLoginedAw();
    if (!isLogin) {
      ThunderKernel.setUserInfo('', '');
      const buf: string = `isvip=0,viptype=,viplevel=0,userchannel=${installChannel}`;
      logger.information('stat dk extinfo', buf);
      ThunderKernel.setGlobalExtInfo(buf, false);
    } else {
      LoginHelperNS.loginHelper.getAllUserInfo((userInfo: any) => {
        let isVip: string = '0';
        let vipType: string = '';
        let vipLevel: number = 0;
        let userId: string = '';
        do {
          if (!userInfo) {
            break;
          }
          userId = userInfo.userID;
          logger.information(`stat dk userId ${userInfo.userID}`, userInfo);

          if (!userInfo.vipList) {
            break;
          }

          if (!userInfo.vipList[0]) {
            break;
          }

          logger.information(`stat dk isVip ${userInfo.vipList[0].isVip}`);
          isVip = String(userInfo.vipList[0].isVip) === '1' ? '1' : '0';
          if (isVip === '0') {
            break;
          }

          logger.information(
            `stat dk vasType ${userInfo.vipList[0].vasType}, vipLevel ${userInfo.vipList[0].vipLevel}`
          );

          const vipTypes: { [vasType: string]: string } = { 2: 'normal', 3: 'platinum', 5: 'super' };
          if (userInfo.vipList[0].vasType && vipTypes.hasOwnProperty(String(userInfo.vipList[0].vasType))) {
            vipType = vipTypes[String(userInfo.vipList[0].vasType)];
          }
          if (userInfo.vipList[0].vipLevel && !isNaN(Number(userInfo.vipList[0].vipLevel))) {
            vipLevel = userInfo.vipList[0].vipLevel;
          }
        } while (0);
        ThunderKernel.setUserInfo(userId, '');
        const buf: string = `isvip=${isVip},viptype=${vipType},viplevel=${vipLevel},userchannel=${installChannel}`;
        logger.information('stat dk extinfo', buf);
        ThunderKernel.setGlobalExtInfo(buf, false);
      });
    }
  }

  public isDkLoaded(): boolean {
    return this.isInitDkSuccess;
  }

  public initDownloadKernelStart(): void {
    this.isInitDkFinish = false;
  }

  public initDownloadKernelEnd(result: number): void {
    this.isInitDkFinish = true;
    this.isInitDkSuccess = result === 0;
    this.eventContainer.fireEvent(NodeEventMesssageNS.eventDownloadKernelInitEnd, this.isInitDkSuccess);
  }

  public downloadSDKCrashRecover(result: number): void {
    this.eventContainer.fireEvent(NodeEventMesssageNS.eventDownloadSDKRecover, result);
    this.startTasks.forEach((taskId: number) => {
      const task: Task = this.tasks.get(taskId) as Task;
      if (task) {
        const groupTaskId = task.getTaskAttribute(DownloadKernel.TaskAttribute.GroupTaskId);
        const ownerTask: DownloadKernel.ITask = groupTaskId > 0 ? this.tasks.get(groupTaskId) : null;
        task.onCrash();
        task.createDownloadSdkTask(ownerTask).catch();
      }
    });
  }

  public isTaskForceDownload(taskId: number): boolean {
    let ret: boolean = false;
    do {
      const task: Task = this.tasks.get(taskId) as Task;
      if (!task) {
        break;
      }

      let targetTask: DownloadKernel.ITask = task;
      const groupTaskId = task.getTaskAttribute(DownloadKernel.TaskAttribute.GroupTaskId);
      if (groupTaskId > 0) {
        targetTask = this.tasks.get(groupTaskId);
      }

      if (!targetTask) {
        break;
      }

      const taskExtra: DownloadKernel.ITaskExtra = targetTask.getExtraObject();
      if (!taskExtra) {
        break;
      }

      const flags: DownloadKernel.TaskAttributeFlags = taskExtra?.getUserData(TaskUserDataGUID.guidTaskAttributeFlags, 'number') ?? DownloadKernel.TaskAttributeFlags.None;
      ret = !!(flags & DownloadKernel.TaskAttributeFlags.SparseFilePreferred);
    } while (0);
    return ret;
  }

  public downloadSDKCrash(errorCode: DownloadKernel.TaskError.DownloadSDKCrash | DownloadKernel.TaskError.DownloadSDKMissing): void {
    this.sdkCrash = true;
    if (this.startTasks.size > 0) {
      const tasks: Task[] = [];

      this.startTasks.forEach((taskId: number) => {
        const task: Task = this.tasks.get(taskId) as Task;
        if (task) {
          task.setStatusAndErrorCode(DownloadKernel.TaskStatus.Failed, errorCode);
          tasks.push(task);
        }
      });

      // 把正在下载中的清0
      this.startTasks.clear();
      eventManager.fireTaskEvent(DownloadKernel.TaskEventType.StatusChanged, tasks);
    }

    this.notifySDKCrash();
  }

  public enableCommonP2pChannel(enable: boolean): void {
    this.isEnableP2pChannel = enable;

    // 更新Strategy，但是这个值只有在setStartInfo才会取
    for (const [taskId, item] of this.tasks) {
      const taskStrategy: number = this.getTaskStrategy(taskId);
      (item as Task).setDispatchStrategy(taskStrategy);
    }
  }

  public enableCommonP2sChannel(enable: boolean): void {
    this.isEnableP2sChannel = enable;
    for (const [taskId, item] of this.tasks) {
      const taskStrategy: number = this.getTaskStrategy(taskId);
      (item as Task).setDispatchStrategy(taskStrategy);
    }
  }

  public getTotalTaskDownloadSpeed(includeBackground: boolean, includePrivate?: boolean): number {
    let ret: number = 0;
    this.startTasks.forEach((taskId: number) => {
      do {
        const task: DownloadKernel.ITask = this.getTaskFromTaskId(taskId);
        if (!task) {
          continue;
        }
        if (includePrivate === undefined || includePrivate === null) {
          includePrivate = true;
        }
        if (!includePrivate && task.getIsPrivate()) {
          continue;
        }

        const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
        if (taskStatus !== DownloadKernel.TaskStatus.Started) {
          continue;
        }

        const speed: number = task.getDownloadSpeed();
        const isBackground: boolean = task.getTaskAttribute(DownloadKernel.TaskAttribute.Background) as boolean;
        if (isBackground) {
          if (includeBackground) {
            ret += speed;
          }
        } else {
          ret += speed;
        }
      } while (0);
    });
    return ret;
  }

  public getTotalTaskVipSpeed(includeBackground: boolean, includePrivate?: boolean): number {
    let ret: number = 0;
    this.startTasks.forEach((taskId: number) => {
      do {
        const task: DownloadKernel.ITask = this.getTaskFromTaskId(taskId);
        if (!task) {
          continue;
        }

        if (includePrivate === undefined || includePrivate === null) {
          includePrivate = true;
        }
        if (!includePrivate && task.getIsPrivate()) {
          continue;
        }

        const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
        if (taskStatus !== DownloadKernel.TaskStatus.Started) {
          continue;
        }

        const speed: number = task.getVipSpeed();
        const isBackground: boolean = task.getTaskAttribute(DownloadKernel.TaskAttribute.Background) as boolean;
        if (isBackground) {
          if (includeBackground) {
            ret += speed;
          }
        } else {
          ret += speed;
        }
      } while (0);
    });

    return ret;
  }

  public async attachTaskStopOnce(taskId: number): Promise<boolean> {
    const result: Promise<boolean> = new Promise<boolean>(
      (resolve: (value?: boolean | PromiseLike<boolean>) => void): void => {
        const task: DownloadKernel.ITask = this.tasks.get(taskId);
        if (task) {
          const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
          if (
            taskStatus === DownloadKernel.TaskStatus.Succeeded ||
            taskStatus === DownloadKernel.TaskStatus.Seeding ||
            taskStatus === DownloadKernel.TaskStatus.Stopped ||
            taskStatus === DownloadKernel.TaskStatus.Failed
          ) {
            resolve(true);
          } else {
            let cookie: number = 0;
            cookie = eventManager.attachTaskEvent(DownloadKernel.TaskEventType.StatusChanged, (...args: any[]) => {
              do {
                if (!args) {
                  break;
                }
                if (args[0] !== DownloadKernel.TaskEventType.StatusChanged) {
                  break;
                }
                const taskList: DownloadKernel.ITask[] = args[1];
                if (!taskList) {
                  break;
                }

                for (let i: number = 0; i < taskList.length; ++i) {
                  const targetTask: DownloadKernel.ITask = taskList[i];
                  if (!targetTask) {
                    continue;
                  }

                  const targetTaskId: number = targetTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskId);
                  if (taskId !== targetTaskId) {
                    continue;
                  }

                  const taskStatus: DownloadKernel.TaskStatus = targetTask.getTaskStatus();
                  if (
                    taskStatus === DownloadKernel.TaskStatus.Succeeded ||
                    taskStatus === DownloadKernel.TaskStatus.Seeding ||
                    taskStatus === DownloadKernel.TaskStatus.Stopped
                  ) {
                    eventManager.detachTaskEvent(DownloadKernel.TaskEventType.StatusChanged, cookie);
                    resolve(true);
                  }
                  break;
                }
              } while (0);
            });
          }
        } else {
          resolve(false);
        }
      }
    );

    return result;
  }

  // 针对检测磁盘空间导致任务流程中断的bt任务通知文件大小变化
  public notifyBtFileSizeChange(taskId: number): void {
    do {
      const task: Task = this.tasks.get(taskId) as Task;
      if (!task) {
        break;
      }

      const taskType: DownloadKernel.TaskType = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
      if (taskType !== DownloadKernel.TaskType.Bt) {
        break;
      }

      // 针对暂停任务
      let taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
      if (taskStatus !== DownloadKernel.TaskStatus.Stopped) {
        break;
      }

      const btTask: BtTask = task.getExtraObject() as BtTask;
      const fileSize: number = btTask.getFileSize();
      const isChanaged: boolean = task.internalSetFileSize(fileSize);
      if (isChanaged) {
        this.taskDetailChanage([task], DownloadKernel.TaskDetailChangedFlags.FileSize);
      }
    } while (0);
  }

  public async panDeleteSubTask(taskId: number, fileRealIndexs: string): Promise<boolean> {
    const difference = (a: number[], b: number[]) => {
      const s = new Set(b);
      return a.filter(x => !s.has(x));
    };

    let ret: boolean = false;
    do {
      if (!fileRealIndexs) {
        break;
      }

      let indexLists: number[] = null;
      try {
        indexLists = JSON.parse(fileRealIndexs);
      } catch (e) {
        logger.warning(e);
      }

      if (indexLists == null) {
        break;
      }

      const task: Task = this.tasks.get(taskId) as Task;
      if (!task) {
        break;
      }

      const taskType: DownloadKernel.TaskType = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
      if (taskType !== DownloadKernel.TaskType.Group) {
        break;
      }

      const groupTask: GroupTask = task.getExtraObject() as GroupTask;
      const currentIndexs = groupTask.getDownloadTaskIndexs();
      const currentIndexsSet = new Set(currentIndexs);

      let isDelete: boolean = true;
      for (let index of indexLists) {
        if (!currentIndexsSet.has(index)) {
          isDelete = false
          break;
        }
      }


      if (isDelete && (currentIndexs.length - indexLists.length === 1)) {
        const deleteList = difference(currentIndexs, indexLists);
        if (deleteList?.length === 1) {
          groupTask.deleteSubTask(deleteList[0], DownloadKernel.TaskStopReason.SelectDownloadLists);
          ret = true;
        }
      }

    } while (0);

    return ret;
  }

  // 重新选择BT子任务下载
  public async downloadTaskSelectFiles(taskId: number, fileRealIndexs: string): Promise<boolean> {
    let ret: boolean = false;
    do {
      if (!fileRealIndexs) {
        break;
      }

      let indexLists: number[] = null;
      try {
        indexLists = JSON.parse(fileRealIndexs);
      } catch (e) {
        logger.warning(e);
      }

      if (indexLists == null) {
        break;
      }

      const task: Task = this.tasks.get(taskId) as Task;
      if (!task) {
        break;
      }

      const taskType: DownloadKernel.TaskType = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
      if (taskType !== DownloadKernel.TaskType.Bt && taskType !== DownloadKernel.TaskType.Group) {
        break;
      }

      // 如果是在垃圾箱，先还原
      const recycleTime: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.RecycleTime);
      if (recycleTime > 0) {
        this.recoverFromRecycle(taskId);
      }

      // 如果任务已经开始，那么先暂停
      let taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
      if (taskStatus === DownloadKernel.TaskStatus.Started || taskStatus === DownloadKernel.TaskStatus.StartPending || taskStatus === DownloadKernel.TaskStatus.StopPending) {
        await this.stopTaskAW(taskId, DownloadKernel.TaskStopReason.SelectDownloadLists);
        taskStatus = task.getTaskStatus();
      }
      if (taskType === DownloadKernel.TaskType.Bt) {
        // 重新设置选中的下载子任务
        const btTask: BtTask = task.getExtraObject() as BtTask;
        btTask.setDownloadFile(indexLists);
      } else if (taskType === DownloadKernel.TaskType.Group) {
        const groupTask: GroupTask = task.getExtraObject() as GroupTask;
        groupTask.setDownloadTask(indexLists);
      }
      if (taskStatus === DownloadKernel.TaskStatus.StartWaiting) {
        // 等待开始状态不会刷新任务状态，这里主动触发一下
        this.taskStatusChanage([task]);
        if (taskType === DownloadKernel.TaskType.Group) {
          const groupTask: GroupTask = task.getExtraObject() as GroupTask;
          groupTask.updateTaskDetail();
        } else if (taskType === DownloadKernel.TaskType.Bt) {
          const btTask: BtTask = task.getExtraObject() as BtTask;
          const fileSize: number = btTask.getFileSize();
          const isChanaged: boolean = task.internalSetFileSize(fileSize);
          if (isChanaged) {
            this.taskDetailChanage([task], DownloadKernel.TaskDetailChangedFlags.FileSize);
          }

          const subFileList: DownloadKernel.IBtDownloadFileInfo[] = [];
          const btFileMap: Map<number, DownloadKernel.IBtFile> = btTask.getBtFileList();
          for (const [, subBtFile] of btFileMap) {
            const subFile: DownloadKernel.IBtDownloadFileInfo = (subBtFile as BtFile).getBtDownloadFileInfo();
            subFileList.push(subFile);
          }
          eventManager.fireTaskEvent(DownloadKernel.TaskEventType.BtSubFileDetailChanged, taskId, subFileList);
        }
      } else if (
        taskStatus === DownloadKernel.TaskStatus.Succeeded ||
        taskStatus === DownloadKernel.TaskStatus.Seeding
      ) {
        const categoryId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId);
        const completeView: CategoryView = this.getCategoryView(categoryId, DownloadKernel.CategroyViewID.Completed);

        if (completeView) {
          const taskList: DownloadKernel.ITask[] = [];
          taskList.push(task);
          // 从已完成视图移除
          completeView.removeTask(taskList);
        }

        const downloadingView: CategoryView = this.getCategoryView(categoryId, DownloadKernel.CategroyViewID.Downloading);
        if (downloadingView) {
          // 先把任务状态置为暂停
          if (taskType === DownloadKernel.TaskType.Group) {
            // 如果是任务组，先将任务组失败子任务状态设置为暂停
            const taskExtra: GroupTask = task.getExtraObject() as GroupTask;
            const taskCount: number = taskExtra.getSubTaskCount();
            for (let i: number = 0; i < taskCount; ++i) {
              const subTask: DownloadKernel.ITask = taskExtra.getSubTaskByIndex(i);
              if (subTask) {
                const needDownload: number = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.DownloadSubTask);
                if (needDownload !== 1) {
                  continue;
                }
                const subTaskStatus: DownloadKernel.TaskStatus = subTask.getTaskStatus();
                if (subTaskStatus === DownloadKernel.TaskStatus.Failed) {
                  (subTask as Task).updateTaskStatus(DownloadKernel.TaskStatus.Stopped);
                }
              }
            }
          }

          const taskList: DownloadKernel.ITask[] = [];
          taskList.push(task);
          // 插入到正在下载任务视图
          downloadingView.insertTask(taskList, DownloadKernel.TaskInsertReason.ReDownload);

          if (task.updateTaskStatus(DownloadKernel.TaskStatus.Stopped)) {
            task.setStopReason(DownloadKernel.TaskStopReason.SelectDownloadLists);
            this.taskStatusChanage([task]);
            task.setStopReason(DownloadKernel.TaskStopReason.Unknown);
          }
        }
      }
      ret = true;
      // 开始任务走任务排队逻辑
      // this.startTask(taskId);
    } while (0);
    return ret;
  }

  public batchRelateYunTask(
    infos: { panId: string; path: string; taskId: number; user_id: string; path_id: string }[]
  ): void {
    const transactionId: number = ThunderKernel.beginTransaction();
    for (const item of infos) {
      ThunderPanTaskNS.insertTask(
        item.taskId,
        item.panId,
        item.user_id,
        JSON.stringify({ title: item.path, id: item.path_id })
      );
    }
    ThunderKernel.commitTransaction(transactionId);
  }

  private getTaskCategoryView(task: DownloadKernel.ITask): CategoryView {
    let categoryView: CategoryView = null;
    const categoryId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId);
    const categoryViewId: DownloadKernel.CategroyViewID = task.getTaskAttribute(
      DownloadKernel.TaskAttribute.CategoryViewId
    );
    if (categoryId === -1 || categoryId === categoryManager.getZhanCategoryId()) {
      categoryView = categoryViewManager.getCategoryViewFromId(categoryViewId) as CategoryView;
    } else {
      // 私人空间或其他目录的任务
      categoryView = categoryManager.getCategoryView(categoryId, categoryViewId) as CategoryView;
    }
    return categoryView;
  }

  private async loadStorageTask(taskBase: DownloadKernel.ITaskBase): Promise<DownloadKernel.ITask> {
    let ret: boolean = true;
    let newTask: Task = new Task(this);

    if (taskBase !== null) {
      switch (taskBase.taskType) {
        case DownloadKernel.TaskType.P2sp:
          newTask.loadStorageP2spTask(taskBase);
          break;

        case DownloadKernel.TaskType.Bt:
          newTask.loadStorageBtTask(taskBase);
          break;

        case DownloadKernel.TaskType.Emule:
          newTask.loadStorageEmuleTask(taskBase);
          break;

        case DownloadKernel.TaskType.Group:
          newTask.loadStorageGroupTask(taskBase);
          break;

        default:
          ret = false;
          break;
      }
    }

    if (!ret) {
      newTask = null;
    } else {
      if (!this.addTask(newTask)) {
        newTask = null;
      }
    }

    return newTask;
  }

  private getTaskStrategy(taskId: number): number {
    let taskStrategy: number = DownloadKernel.DispatchStrategy.DispatchStrategyOrigin;
    const task: DownloadKernel.ITask = this.tasks.get(taskId);
    if (task) {
      const isOrigin: boolean = task.getOriginOnlyStrategy();
      if (!isOrigin) {
        if (this.isEnableP2pChannel) {
          taskStrategy += DownloadKernel.DispatchStrategy.DispatchStrategyP2p;
        }
        if (this.isEnableP2sChannel) {
          taskStrategy += DownloadKernel.DispatchStrategy.DispatchStrategyP2s;
        }
      }
    }

    return taskStrategy;
  }

  private async redownloadP2spTask(
    oldTask: DownloadKernel.ITask,
    newTaskInfo: DownloadKernel.INewTaskInfo
  ): Promise<DownloadKernel.ITask> {
    let newTask: DownloadKernel.ITask = null;
    if (oldTask) {
      const taskName: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskName);
      const savePath: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
      const url: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskUrl);
      const refUrl: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.RefUrl);
      const cookie: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.Cookie);
      const privateSpace: boolean = oldTask.getIsPrivate();
      const openOnComplete: number = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.OpenOnComplete) as number ?? 0;

      let newTaskBaseInfo: DownloadKernel.INewTaskBaseInfo = null;
      if (newTaskInfo && newTaskInfo.taskBaseInfo) {
        newTaskBaseInfo = newTaskInfo.taskBaseInfo;
        if (!newTaskBaseInfo.taskName || newTaskBaseInfo.taskName === '') {
          newTaskBaseInfo.taskName = taskName;
        }
        if (!newTaskBaseInfo.savePath || newTaskBaseInfo.savePath === '') {
          newTaskBaseInfo.savePath = savePath;
        }
      } else {
        newTaskBaseInfo = { taskName: taskName, savePath: savePath };
      }
      newTaskBaseInfo.openOnComplete = openOnComplete;
      const p2spTask: DownloadKernel.IP2spTask = oldTask.getExtraObject() as DownloadKernel.IP2spTask;
      const newP2spTaskInfo: DownloadKernel.INewP2spTaskInfo = {
        url: url,
        refUrl: refUrl,
        cookie: cookie,
        useOriginResourceOnly: oldTask.getOriginOnlyStrategy() ? 1 : 0,
        nameFixed: p2spTask.getNameFixed(),
        userAgent: p2spTask.getTaskUserAgent(),
      };
      if (newTaskInfo && newTaskInfo.p2spTaskInfo) {
        if (newTaskInfo.p2spTaskInfo.useOriginResourceOnly !== undefined) {
          // 重新创建任务，可以设置只从原始地址下载
          logger.information('redownload redefine orign', newTaskInfo.p2spTaskInfo.useOriginResourceOnly);
          newP2spTaskInfo.useOriginResourceOnly = newTaskInfo.p2spTaskInfo.useOriginResourceOnly ? 1 : 0;
        } else {
          logger.information('redownload extends origin', newP2spTaskInfo.useOriginResourceOnly);
        }
      }

      newTask = await this.createTask(DownloadKernel.TaskType.P2sp, {
        taskBaseInfo: newTaskBaseInfo,
        p2spTaskInfo: newP2spTaskInfo,
        privateSpace: privateSpace
      });
    }

    return newTask;
  }

  private async redownloadBtTask(
    oldTask: DownloadKernel.ITask,
    newTaskInfo: DownloadKernel.INewTaskInfo
  ): Promise<DownloadKernel.ITask> {
    let newTask: DownloadKernel.ITask = null;
    if (oldTask) {
      const originUrl: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskUrl);
      const taskName: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskName);
      const savePath: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
      const privateSpace: boolean = oldTask.getIsPrivate();

      const taskExtra: BtTask = oldTask.getExtraObject() as BtTask;
      const seedFilePath: string = taskExtra.getSeedFilePath();
      const title: string = taskExtra.getDisplayName();
      const infoId: string = taskExtra.getInfoId();
      const tracker: string = taskExtra.getTrackerUrls();
      const indexLists: number[] = taskExtra.getDownloadFile();
      const fileLists: DownloadKernel.IBtFileInfo[] = taskExtra.getBtFileInfoList();
      const subFileScheduler = taskExtra.getBTTaskSubFileScheduler();

      let newTaskBaseInfo: DownloadKernel.INewTaskBaseInfo = null;
      if (newTaskInfo && newTaskInfo.taskBaseInfo) {
        newTaskBaseInfo = newTaskInfo.taskBaseInfo;
        if (!newTaskBaseInfo.taskName || newTaskBaseInfo.taskName === '') {
          newTaskBaseInfo.taskName = taskName;
        }
        if (!newTaskBaseInfo.savePath || newTaskBaseInfo.savePath === '') {
          newTaskBaseInfo.savePath = savePath;
        }
      } else {
        newTaskBaseInfo = { taskName: taskName, savePath: savePath };
      }
      const newBtTaskInfo: DownloadKernel.INewBtTaskInfo = {
        originUrl,
        seedFile: seedFilePath,
        displayName: title,
        fileRealIndexLists: indexLists,
        fileLists: fileLists,
        infoId: infoId,
        tracker: tracker,
        subFileScheduler
      };
      if (
        newTaskInfo &&
        newTaskInfo.btTaskInfo &&
        newTaskInfo.btTaskInfo.fileRealIndexLists &&
        newTaskInfo.btTaskInfo.fileRealIndexLists.length > 0
      ) {
        // 重新创建任务可以支持重新选择子任务
        newBtTaskInfo.fileRealIndexLists = newTaskInfo.btTaskInfo.fileRealIndexLists;
      }

      newTask = await this.createTask(DownloadKernel.TaskType.Bt, {
        taskBaseInfo: newTaskBaseInfo,
        btTaskInfo: newBtTaskInfo,
        privateSpace: privateSpace
      });
    }

    return newTask;
  }

  private async redownloadEmuleTask(
    oldTask: DownloadKernel.ITask,
    newTaskInfo: DownloadKernel.INewTaskInfo
  ): Promise<DownloadKernel.ITask> {
    let newTask: DownloadKernel.ITask = null;
    if (oldTask) {
      const taskName: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskName);
      const savePath: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
      const url: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskUrl);
      const openOnComplete: number = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.OpenOnComplete) as number ?? 0;
      const privateSpace: boolean = oldTask.getIsPrivate();

      let newTaskBaseInfo: DownloadKernel.INewTaskBaseInfo = null;
      if (newTaskInfo && newTaskInfo.taskBaseInfo) {
        newTaskBaseInfo = newTaskInfo.taskBaseInfo;
        if (!newTaskBaseInfo.taskName || newTaskBaseInfo.taskName === '') {
          newTaskBaseInfo.taskName = taskName;
        }
        if (!newTaskBaseInfo.savePath || newTaskBaseInfo.savePath === '') {
          newTaskBaseInfo.savePath = savePath;
        }
      } else {
        newTaskBaseInfo = { taskName: taskName, savePath: savePath };
      }
      newTaskBaseInfo.openOnComplete = openOnComplete;
      let newEmuleTaskInfo: DownloadKernel.INewEmuleTaskInfo = null;
      if (newTaskInfo && newTaskInfo.emuleTaskInfo) {
        newEmuleTaskInfo = newTaskInfo.emuleTaskInfo;
        newEmuleTaskInfo.url = url;
      } else {
        newEmuleTaskInfo = { url: url };
      }

      newTask = await this.createTask(DownloadKernel.TaskType.Emule, {
        taskBaseInfo: newTaskBaseInfo,
        emuleTaskInfo: newEmuleTaskInfo,
        privateSpace: privateSpace
      });
    }

    return newTask;
  }

  private async redownloadGroupTask(
    oldTask: DownloadKernel.ITask,
    newTaskInfo: DownloadKernel.INewTaskInfo
  ): Promise<DownloadKernel.ITask> {
    let newTask: DownloadKernel.ITask = null;
    if (oldTask) {
      const groupTaskName: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskName);
      const groupTaskPath: string = oldTask.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
      const privateSpace: boolean = oldTask.getIsPrivate();
      const taskExtra: GroupTask = oldTask.getExtraObject() as GroupTask;
      const taskCount: number = taskExtra.getSubTaskCount();
      const subTaskList: DownloadKernel.INewTaskInfo[] = [];

      for (let i: number = 0; i < taskCount; ++i) {
        const subTask: DownloadKernel.ITask = taskExtra.getSubTaskByIndex(i);
        if (subTask) {
          const taskName: string = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskName);
          const savePath: string = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
          const url: string = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskUrl);
          const refUrl: string = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.RefUrl);
          const cookie: string = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.Cookie);
          const downloadSubTask: number = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.DownloadSubTask);
          const taskType: DownloadKernel.TaskType = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
          const openOnComplete: number = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.OpenOnComplete) as number ?? 0;

          const subNewTaskBaseInfo: DownloadKernel.INewTaskBaseInfo = { taskName: taskName, savePath: savePath, openOnComplete };
          const subNewTaskInfo: DownloadKernel.INewTaskInfo = {
            taskBaseInfo: subNewTaskBaseInfo,
            privateSpace: privateSpace
          };
          if (taskType === DownloadKernel.TaskType.P2sp) {
            const p2spTask: DownloadKernel.IP2spTask = subTask.getExtraObject() as DownloadKernel.IP2spTask;
            subNewTaskInfo.taskType = taskType;
            subNewTaskInfo.p2spTaskInfo = {
              url: url,
              refUrl: refUrl,
              cookie: cookie,
              downloadSubTask: downloadSubTask,
              nameFixed: p2spTask.getNameFixed(),
              userAgent: p2spTask.getTaskUserAgent(),
            };
            subTaskList.push(subNewTaskInfo);
          } else if (taskType === DownloadKernel.TaskType.Emule) {
            subNewTaskInfo.taskType = taskType;
            subNewTaskInfo.emuleTaskInfo = { url: url, downloadSubTask: downloadSubTask };
            subTaskList.push(subNewTaskInfo);
          }
          if (
            newTaskInfo &&
            newTaskInfo.groupTaskInfo &&
            newTaskInfo.groupTaskInfo.subTaskList &&
            newTaskInfo.groupTaskInfo.subTaskList[i]
          ) {
            // 如果指定子集信息
            const createSubNewTaskInfo: DownloadKernel.INewTaskInfo = newTaskInfo.groupTaskInfo.subTaskList[i];
            if (createSubNewTaskInfo.taskBaseInfo) {
              subNewTaskInfo.taskBaseInfo = createSubNewTaskInfo.taskBaseInfo;
              if (!subNewTaskInfo.taskBaseInfo.taskName || subNewTaskInfo.taskBaseInfo.taskName === '') {
                subNewTaskInfo.taskBaseInfo.taskName = taskName;
              }
              if (!subNewTaskInfo.taskBaseInfo.savePath || subNewTaskInfo.taskBaseInfo.savePath === '') {
                subNewTaskInfo.taskBaseInfo.savePath = savePath;
              }
            }
            if (taskType === DownloadKernel.TaskType.P2sp) {
              if (createSubNewTaskInfo.p2spTaskInfo) {
                subNewTaskInfo.p2spTaskInfo.useOriginResourceOnly = createSubNewTaskInfo.p2spTaskInfo
                  .useOriginResourceOnly
                  ? 1
                  : 0;
              }
            } else if (taskType === DownloadKernel.TaskType.Emule) {
              if (createSubNewTaskInfo.emuleTaskInfo) {
                subNewTaskInfo.emuleTaskInfo.origin = createSubNewTaskInfo.emuleTaskInfo.origin;
              }
            }
          }
        }
      }

      let newTaskBaseInfo: DownloadKernel.INewTaskBaseInfo = null;
      if (newTaskInfo && newTaskInfo.taskBaseInfo) {
        newTaskBaseInfo = newTaskInfo.taskBaseInfo;
        if (!newTaskBaseInfo.taskName || newTaskBaseInfo.taskName === '') {
          newTaskBaseInfo.taskName = groupTaskName;
        }
        if (!newTaskBaseInfo.savePath || newTaskBaseInfo.savePath === '') {
          newTaskBaseInfo.savePath = groupTaskPath;
        }
      } else {
        newTaskBaseInfo = { taskName: groupTaskName, savePath: groupTaskPath };
      }
      const newGroupTaskInfo: DownloadKernel.INewGroupTaskInfo = { subTaskList: subTaskList };

      newTask = await this.createTask(DownloadKernel.TaskType.Group, {
        taskBaseInfo: newTaskBaseInfo,
        groupTaskInfo: newGroupTaskInfo,
        privateSpace: privateSpace
      });
    }

    return newTask;
  }

  private addTask(task: Task, newTaskData?: DownloadKernel.INewTaskInfo): boolean {
    let ret: boolean = false;
    if (task) {
      const taskId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskId);
      this.tasks.set(taskId, task);

      const taskStrategy: number = this.getTaskStrategy(taskId);
      logger.information('set taskStrategy', taskStrategy);
      task.setDispatchStrategy(taskStrategy);
      let taskExtra: DownloadKernel.ITaskExtra = null;
      if (newTaskData && (newTaskData.birdKey || newTaskData.searchSource)) {
        if (newTaskData.taskType === DownloadKernel.TaskType.P2sp) {
          taskExtra = task.getExtraObject() as DownloadKernel.IP2spTask;
        } else if (newTaskData.taskType === DownloadKernel.TaskType.Bt) {
          taskExtra = task.getExtraObject() as DownloadKernel.IBtTask;
        } else if (newTaskData.taskType === DownloadKernel.TaskType.Emule) {
          taskExtra = task.getExtraObject() as DownloadKernel.IEmuleTask;
        } else if (newTaskData.taskType === DownloadKernel.TaskType.Group) {
          taskExtra = task.getExtraObject() as DownloadKernel.IGroupTask;
        } else if (newTaskData.taskType === DownloadKernel.TaskType.Magnet) {
          //
        }

        if (taskExtra) {
          newTaskData.birdKey && taskExtra.setUserData(TaskUserDataGUID.guidBirdKey, 'birdKey', newTaskData.birdKey);
          newTaskData.searchSource &&
            taskExtra.setUserData(TaskUserDataGUID.guidSearchFrom, 'searchFrom', newTaskData.searchSource);
        }
      }
      ret = true;
    }

    return ret;
  }

  public findTask(url: string): number {
    let taskId: number = -1;
    do {
      if (!this.tasks || this.tasks.size < 1) {
        break;
      }

      for (const task of this.tasks.values()) {
        const taskUrl: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskUrl);
        if (taskUrl === url) {
          taskId = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskId);
          break;
        }
      }
    } while (0);
    return taskId;
  }

  public batchFindPanTaskIdByFileId(fileIds: string[], categoryId: number): { [fileId: string]: number } {
    let taskIds: { [fileId: string]: number } = {};
    do {
      if (!fileIds || fileIds.length === 0) {
        break;
      }
      if (!this.tasks || this.tasks.size < 1) {
        break;
      }
      let fileIdsSet = new Set(fileIds);
      for (const [, item] of this.tasks) {
        if (item.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId) === categoryId) {
          const extra: DownloadKernel.ITaskExtra = item.getExtraObject();
          const userData: string = extra.getUserData(TaskUserDataGUID.guidThunderYunDownloadUserData, 'extras');
          if (userData) {
            let userDataObj: { fileId: string; isInSafe: number; linkExpire: string; passCodeToken: string; shareId: string; token: string } = null;
            try {
              userDataObj = JSON.parse(userData);
            } catch (error) {
              //
            }

            if (fileIdsSet.has(userDataObj?.fileId)) {
              taskIds[userDataObj.fileId] = item.getId();
              break;
            }
          }
        }
      }
    } while (false);

    return taskIds;
  }

  public findPanTaskByFileId(fileId: string, categoryId: number): DownloadKernel.ITask {
    let task: DownloadKernel.ITask = null;
    do {
      if (!fileId) {
        break;
      }
      if (!this.tasks || this.tasks.size < 1) {
        break;
      }

      for (const [, item] of this.tasks) {
        if (item.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId) === categoryId) {
          const extra: DownloadKernel.ITaskExtra = item.getExtraObject();
          const userData: string = extra.getUserData(TaskUserDataGUID.guidThunderYunDownloadUserData, 'extras');
          if (userData) {
            let userDataObj: { fileId: string; isInSafe: number; linkExpire: string; passCodeToken: string; shareId: string; token: string } = null;
            try {
              userDataObj = JSON.parse(userData);
            } catch (error) {
              //
            }

            if (userDataObj?.fileId === fileId) {
              task = item;
              break;
            }
          }
        }
      }
    } while (0);
    return task;
  }

  public getBackgroundTasks(): number[] {
    return Array.from(this.backgroundTasks);
  }

  private removeTask(taskId: number): void {
    const task: DownloadKernel.ITask = this.getTaskFromTaskId(taskId);
    if (task) {
      const taskType: DownloadKernel.TaskType = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
      if (taskType === DownloadKernel.TaskType.Group) {
        // 如果是任务组，需要把任务组子任务taskid也移除
        const taskExtra: GroupTask = task.getExtraObject() as GroupTask;
        const taskCount: number = taskExtra.getSubTaskCount();
        for (let i: number = 0; i < taskCount; ++i) {
          const subTask: DownloadKernel.ITask = taskExtra.getSubTaskByIndex(i);
          if (subTask) {
            const subTaskId: number = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.TaskId);
            this.tasks.delete(subTaskId);
          }
        }
      }
    }

    this.backgroundTasks.delete(taskId);
    this.tasks.delete(taskId);
    this.removeStartTask(task);
  }

  private insertToCategoryView(
    task: Task,
    reason: DownloadKernel.TaskInsertReason = DownloadKernel.TaskInsertReason.Unknown
  ): void {
    let categoryViewId: DownloadKernel.CategroyViewID = DownloadKernel.CategroyViewID.Downloading;
    const recycleTime: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.RecycleTime);
    if (recycleTime > 0) {
      categoryViewId = DownloadKernel.CategroyViewID.Recycle;
    } else {
      const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
      if (taskStatus === DownloadKernel.TaskStatus.Succeeded || taskStatus === DownloadKernel.TaskStatus.Seeding) {
        categoryViewId = DownloadKernel.CategroyViewID.Completed;
      }
    }

    const categoryId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId);
    const categoryView: CategoryView = this.getCategoryView(categoryId, categoryViewId);
    // 插入任务视图
    if (categoryView) {
      const taskList: DownloadKernel.ITask[] = [];
      taskList.push(task);
      categoryView.insertTask(taskList, reason);
    }
  }

  private taskStatusChanage(tasks: DownloadKernel.ITask[]): void {
    if (this.isInitDkSuccess) {
      for (let i: number = 0; i < tasks.length; ++i) {
        const task: DownloadKernel.ITask = tasks[i];
        const groupTaskId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.GroupTaskId);
        // 任务组子任务
        if (groupTaskId > 0) {
          // 根据子任务获取任务组任务
          do {
            const subTask: Task = task as Task;
            const subTaskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
            if (
              subTaskStatus === DownloadKernel.TaskStatus.Succeeded ||
              subTaskStatus === DownloadKernel.TaskStatus.Seeding
            ) {
              subTask.onFinish();
            } else if (subTaskStatus === DownloadKernel.TaskStatus.Stopped) {
              subTask.onStop();
            } else if (subTaskStatus === DownloadKernel.TaskStatus.Failed) {
              subTask.onFail();
            }
          } while (0);
        } else {
          if (task.getType() === DownloadKernel.TaskType.Group) {
            do {
              const taskExtra: GroupTask = task.getExtraObject() as GroupTask;
              if (!taskExtra) {
                break;
              }

              const taskStatus: DownloadKernel.TaskStatus = taskExtra.getTaskStatus();
              logger.information('taskStatusChanage taskExtra.taskStatus:', taskStatus);
              if (
                taskStatus === DownloadKernel.TaskStatus.Succeeded ||
                taskStatus === DownloadKernel.TaskStatus.Seeding
              ) {
                if (task.getTaskStatus() !== taskStatus) {
                  // 已完成任务组，取消下载失败任务， task.getTaskStatus() 为7 ，taskExtra.getTaskStatus() 为8，虽然前面视图会增减，但是在-1-》+1的过程中，不会取数据，导致视图刷新的时候，已完成任务数量仍然一致，无法触发 complete-item的 Wath taskId事件
                  eventManager.fireTaskEvent(DownloadKernel.TaskEventType.GroupTaskCountChangeForceUpdate, task.getId());
                }
                this.onTaskFinish(task);
              } else if (taskStatus === DownloadKernel.TaskStatus.Stopped) {
                this.onTaskStop(task);
              } else if (taskStatus === DownloadKernel.TaskStatus.Failed) {
                this.onTaskFailed(task);
              }
            } while (0);
          } else {
            const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
            logger.information('taskStatusChanage task.taskStatus:', taskStatus);
            if (
              taskStatus === DownloadKernel.TaskStatus.Succeeded ||
              taskStatus === DownloadKernel.TaskStatus.Seeding
            ) {
              this.onTaskFinish(task);
            } else if (taskStatus === DownloadKernel.TaskStatus.Stopped) {
              // 如果任务状态是暂停,那么需要从已开始列表里面移除,避免不断去查询该任务的详细信息
              this.onTaskStop(task);
            } else if (taskStatus === DownloadKernel.TaskStatus.Failed) {
              // 如果任务状态是失败,那么需要从已开始列表里面移除,避免不断去查询该任务的详细信息
              this.onTaskFailed(task);
            }
          }
        }
      }
    }
    // 任务事件
    eventManager.fireTaskEvent(DownloadKernel.TaskEventType.StatusChanged, tasks);
  }

  private taskDetailChanage(
    tasks: DownloadKernel.ITask[],
    taskDetailChangedFlags: DownloadKernel.TaskDetailChangedFlags
  ): void {
    const groupSubTasks: Map<number, DownloadKernel.ITask[]> = new Map();
    const normalTasks: DownloadKernel.ITask[] = [];
    tasks.forEach((task: DownloadKernel.ITask) => {
      const groupTaskId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.GroupTaskId);
      if (groupTaskId < 1) {
        normalTasks.push(task);
      } else {
        let subTasks: DownloadKernel.ITask[] = groupSubTasks.get(groupTaskId);
        if (!subTasks) {
          subTasks = [];
          groupSubTasks.set(groupTaskId, subTasks);
        }
        subTasks.push(task);
      }
    });
    // 再发非子任务事件
    if (normalTasks.length > 0) {
      eventManager.fireTaskEvent(DownloadKernel.TaskEventType.DetailChanged, normalTasks, taskDetailChangedFlags);
    }
  }

  // 启动后加载数据库回调
  private async loadTaskBaseCallback(taskBaseList: DownloadKernel.ITaskBase[]): Promise<void> {
    // 任务组子任务
    const data: { [propName: string]: string | number; } = window.__performanceMap['StartupTime'];
    data.extdata += ',loadTaskBaseCallbackStart=' + new Date().getTime();
    const subTaskMap: Map<number, DownloadKernel.ITaskBase[]> = new Map();
    const xzCategoryId: number = categoryManager.getZhanCategoryId() ?? -1;
    let hasNetDiskTask: boolean = false;
    if (taskBaseList) {
      const downloadingTaskList: DownloadKernel.ITask[] = [];
      const completeTaskList: DownloadKernel.ITask[] = [];
      const recycleTaskList: DownloadKernel.ITask[] = [];
      const privateDownloadingTaskLists: Map<number, DownloadKernel.ITask[]> = new Map();
      const privateCompleteTaskLists: Map<number, DownloadKernel.ITask[]> = new Map();
      data.extdata += ',tasksNum=' + taskBaseList.length;
      for (let i: number = 0; i < taskBaseList.length; ++i) {
        const taskBase: DownloadKernel.ITaskBase = taskBaseList[i];
        if (!taskBase) {
          continue;
        }
        // 后台任务
        if (taskBase.recycleTime === -1) {
          continue;
        }

        // 如果从数据库里面加载出来的任务状态如果不是错误、失败或者已完成,那么设置为停止
        if (
          taskBase.taskStatus !== DownloadKernel.TaskStatus.Unkown &&
          taskBase.taskStatus !== DownloadKernel.TaskStatus.Failed &&
          taskBase.taskStatus !== DownloadKernel.TaskStatus.Succeeded &&
          taskBase.taskStatus !== DownloadKernel.TaskStatus.Seeding
        ) {
          taskBase.taskStatus = DownloadKernel.TaskStatus.Stopped;
        }

        const groupTaskId: number = taskBase.groupTaskId;
        if (groupTaskId > 0) {
          // 把任务组子任务提取出来
          let subTaskList: DownloadKernel.ITaskBase[] = subTaskMap.get(groupTaskId);
          if (!subTaskList) {
            subTaskList = [];
          }
          subTaskList.push(taskBase);
          subTaskMap.set(groupTaskId, subTaskList);
        }

        const newTask: DownloadKernel.ITask = await this.loadStorageTask(taskBase);
        if (newTask) {
          const groupTaskId: number = newTask.getTaskAttribute(DownloadKernel.TaskAttribute.GroupTaskId);
          if (groupTaskId < 1) {
            // 不是任务组子任务
            if (taskBase.categoryId > -1 && taskBase.categoryId !== xzCategoryId) {
              // 私人空间任务/云盘取回任务
              const taskStatus: DownloadKernel.TaskStatus = newTask.getTaskStatus();
              if (
                taskStatus === DownloadKernel.TaskStatus.Succeeded ||
                taskStatus === DownloadKernel.TaskStatus.Seeding
              ) {
                let privateCompleteTaskList: DownloadKernel.ITask[] = privateCompleteTaskLists.get(taskBase.categoryId);
                if (!privateCompleteTaskList) {
                  privateCompleteTaskList = [];
                  privateCompleteTaskLists.set(taskBase.categoryId, privateCompleteTaskList);
                }
                privateCompleteTaskList.push(newTask);
              } else {
                let privateDownloadingTaskList: DownloadKernel.ITask[] = privateDownloadingTaskLists.get(
                  taskBase.categoryId
                );
                if (!privateDownloadingTaskList) {
                  privateDownloadingTaskList = [];
                  privateDownloadingTaskLists.set(taskBase.categoryId, privateDownloadingTaskList);
                }
                privateDownloadingTaskList.push(newTask);
              }
            } else {
              if (!hasNetDiskTask) {
                if (taskBase.categoryId !== -1 && taskBase.categoryId !== xzCategoryId && !categoryManager.isPrivateCategory(taskBase.categoryId)) {
                  // 云盘取回任务
                  hasNetDiskTask = true;
                }
              }
              const recycleTime: number = newTask.getTaskAttribute(DownloadKernel.TaskAttribute.RecycleTime);
              if (recycleTime > 0) {
                recycleTaskList.push(newTask);
              } else {
                const taskStatus: DownloadKernel.TaskStatus = newTask.getTaskStatus();
                if (
                  taskStatus === DownloadKernel.TaskStatus.Succeeded ||
                  taskStatus === DownloadKernel.TaskStatus.Seeding
                ) {
                  completeTaskList.push(newTask);
                } else {
                  downloadingTaskList.push(newTask);
                }
              }
            }
          }
        }
      }

      // 插入正在下载视图
      if (downloadingTaskList.length > 0) {
        const categoryView: CategoryView = categoryViewManager.getCategoryViewFromId(
          DownloadKernel.CategroyViewID.Downloading
        ) as CategoryView;
        if (categoryView) {
          categoryView.insertTask(downloadingTaskList, DownloadKernel.TaskInsertReason.LoadTaskBasic);
        }
      }

      // 插入已完成视图
      if (completeTaskList.length > 0) {
        const categoryView: CategoryView = categoryViewManager.getCategoryViewFromId(
          DownloadKernel.CategroyViewID.Completed
        ) as CategoryView;
        if (categoryView) {
          categoryView.insertTask(completeTaskList, DownloadKernel.TaskInsertReason.LoadTaskBasic);
        }
      }

      // 插入垃圾箱
      if (recycleTaskList.length > 0) {
        const categoryView: CategoryView = categoryViewManager.getCategoryViewFromId(
          DownloadKernel.CategroyViewID.Recycle
        ) as CategoryView;
        if (categoryView) {
          categoryView.insertTask(recycleTaskList, DownloadKernel.TaskInsertReason.LoadTaskBasic);
        }
      }

      // 插入私人空间正在下载视图
      for (const [categoryId, privateDownloadingTaskList] of privateDownloadingTaskLists) {
        const categoryView: CategoryView = categoryManager.getCategoryView(
          categoryId,
          DownloadKernel.CategroyViewID.Downloading
        ) as CategoryView;
        if (categoryView) {
          categoryView.insertTask(privateDownloadingTaskList, DownloadKernel.TaskInsertReason.LoadTaskBasic);
        } else {
          logger.warning(
            'getCategoryView failed! categoryId:',
            categoryId,
            ', categoryViewId:',
            DownloadKernel.CategroyViewID.Downloading
          );
        }
      }
      // 插入私人空间已完成视图
      for (const [categoryId, privateCompleteTaskList] of privateCompleteTaskLists) {
        const categoryView: CategoryView = categoryManager.getCategoryView(
          categoryId,
          DownloadKernel.CategroyViewID.Completed
        ) as CategoryView;
        if (categoryView) {
          categoryView.insertTask(privateCompleteTaskList, DownloadKernel.TaskInsertReason.LoadTaskBasic);
        } else {
          logger.warning(
            'getCategoryView failed! categoryId:',
            categoryId,
            ', categoryViewId:',
            DownloadKernel.CategroyViewID.Completed
          );
        }
      }
    }
    data.extdata += ',loadGroupTaskStart=' + new Date().getTime();
    for (const [groupTaskId, subTaskList] of subTaskMap) {
      // 通过groupTaskId找到子任务对应的任务组
      const task: DownloadKernel.ITask = this.getTaskFromTaskId(groupTaskId);
      if (task) {
        const taskExtra: GroupTask = task.getExtraObject() as GroupTask;
        if (taskExtra) {
          let groupTaskPathLength: number = undefined;
          const groupTaskSavePath: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
          if (groupTaskSavePath) {
            groupTaskPathLength = groupTaskSavePath.length + 1;
          }
          for (let i: number = 0; i < subTaskList.length; ++i) {
            const subTaskBase: DownloadKernel.ITaskBase = subTaskList[i];
            const subTaskId: number = subTaskBase.taskId;
            const subTask: DownloadKernel.ITask = this.getTaskFromTaskId(subTaskId);
            if (!subTask) {
              continue;
            }

            this.setGroupTaskRelativePath(subTask as Task, subTaskBase.savePath, groupTaskPathLength);
            // 把子任务添加到任务组
            taskExtra.addSubTask(subTask);
          }
        }
      }
    }
    data.extdata += ',loadGroupTaskEnd=' + new Date().getTime();

    this.eventContainer.fireEvent(NodeEventMesssageNS.eventTaskDataBaseLoadEnd);
    this.isLoadStorageFinish = true;
    data.extdata += ',loadTaskBaseEnd=' + new Date().getTime();
    // await ConfigNS.isInitAw();
    // const isShowed: number = ConfigNS.getValue('Toast', 'FirstShow_NetDiskTaskTransferToast', 0) as number;
    // if (!isShowed && hasNetDiskTask) {
    //   const popFrom: string = 'startup';
    //   popMutual.enqueue(PopView.NET_DISK_MESSAGE, () => {
    //     XLStatNS.trackEventEx('core_event', 'yunpan_download_remind_pop_show');
    //     popMutual.popNow(PopView.NET_DISK_MESSAGE, popFrom);
    //     MessageBox.confirm({
    //       title: '云盘下载提醒',
    //       content: '由于云盘业务调整，下载列表不再显示云盘的下载任务；已有云盘下载任务，可在：云盘-传输列表 查看',
    //       type: MessageBox.ConfirmType.Info,
    //       okText: '查看云盘下载',
    //       cancelText: '我知道了',
    //       cancelVisible: true
    //     })
    //       .then(async (payload: MessageBoxNS.IResolvePayload) => {
    //         popMutual.setFinish(PopView.NET_DISK_MESSAGE);
    //         let clickid = 'close';
    //         if (payload.action === MessageBoxNS.Action.OK) {
    //           // 查看云盘下载
    //           clickid = 'check_yunpan_download';
    //           await clientModule.callServerFunction('SelectNav', NavViewID.Cloud);
    //           await server.callClientFunctionByName(PanClientName, 'ShowTaskDetail', 'download', []);
    //         } else if (payload.action === MessageBoxNS.Action.Cancel) {
    //           clickid = 'know';
    //         }
    //         XLStatNS.trackEventEx('core_event', 'yunpan_download_remind_pop_click', { click_id: clickid });
    //       })
    //       .catch();
    //   }, popFrom);
    // }
    // ConfigNS.setValue('Toast', 'FirstShow_NetDiskTaskTransferToast', 1);
  }

  private setGroupTaskRelativePath(subTask: Task, savePath: string, rootPathLength: number): void {
    if (subTask && savePath && rootPathLength) {
      let relativePath: string = savePath.substr(rootPathLength, savePath.length);
      if (!relativePath) {
        relativePath = '';
      } else if (relativePath.length > 0) {
        relativePath = relativePath + '\\';
      }
      subTask.updateTaskRelativePath(relativePath);
    }
  }

  private onTaskFinish(task: DownloadKernel.ITask): boolean {
    // 设置完成时间
    const finishTask: Task = task as Task;
    const isChanged: boolean = finishTask.onFinish();

    const isBackground: boolean = task.getTaskAttribute(DownloadKernel.TaskAttribute.Background);
    if (!isBackground) {
      const categoryId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.CategoryId);
      const downloadingView: CategoryView = this.getCategoryView(categoryId, DownloadKernel.CategroyViewID.Downloading);
      if (downloadingView) {
        const taskList: DownloadKernel.ITask[] = [];
        taskList.push(task);
        downloadingView.removeTask(taskList);
      }

      const recycleTime: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.RecycleTime);
      // 防止在已完成的之前这个任务被删除到垃圾箱
      if (recycleTime === 0) {
        const completeView: CategoryView = this.getCategoryView(categoryId, DownloadKernel.CategroyViewID.Completed);
        if (completeView) {
          const taskList: DownloadKernel.ITask[] = [];
          taskList.push(task);
          completeView.insertTask(taskList, DownloadKernel.TaskInsertReason.Complete);
        }
      }
    }

    // const taskId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskId);
    // 已完成的任务从开始列表里面移除,并删除下载库任务
    this.removeStartTask(task);
    return isChanged;
  }

  // 下载库不支持重载入,即开始的任务暂停后，不支持再开始
  // 所以暂停的时候需要把之前的下载库任务delete之后再create
  private onTaskStop(task: DownloadKernel.ITask): boolean {
    const stopTask: Task = task as Task;
    const isChanged: boolean = stopTask.onStop();
    // const taskId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskId);
    this.removeStartTask(task);
    return isChanged;
  }

  private onTaskFailed(task: DownloadKernel.ITask): boolean {
    const failTask: Task = task as Task;
    const isChanged: boolean = failTask.onFail();
    // const taskId: number = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskId);
    this.removeStartTask(task);
    return isChanged;
  }

  private moveTaskFiles(src: string, dst: string, taskId: number, taskType: DownloadKernel.TaskType): Promise<boolean> {
    ipcRenderer.send(ThunderChannelList.channelRMFileMove, src, dst, taskId);
    return new Promise<boolean>(
      (resolve: (value: boolean) => void): void => {
        ipcRenderer.once(
          ThunderChannelList.channelMRFileMoveResult,
          (event: any, result: boolean, errCode: number, taskIdBack: number, aborted: boolean) => {
            if (taskIdBack === taskId) {
              if (result) {
                if (aborted) {
                  result = false;
                }
              } else if (errCode === 2 || errCode === 124) {
                // 原始文件不存在时，当成功处理，要将任务设置到新路径 bug2333
                result = true;
              }
              resolve(result);
            }
          }
        );
      }
    );
  }

  private async moveTask(taskId: number, newPath: string, bStart?: boolean, move2Private?: boolean,
    banToast?: boolean, //是否禁止“移动成功”的toast，纯需求驱动改进，从12版本开始，移入到私人空间的toast条有个“查看”按钮，点击后要定位到具体的category，
    ): Promise<boolean> {
    let ret: boolean = false;
    do {
      const task: Task = this.tasks.get(taskId) as Task;
      if (!task) {
        break;
      }

      let src: string = undefined;
      let dst: string = undefined;
      const taskStatus: DownloadKernel.TaskStatus = task.getTaskStatus();
      const taskType: DownloadKernel.TaskType = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
      const savePath: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
      const taskExtType = task.getExtraObject()?.getUserData(TaskUserDataGUID.guidTaskExtType, 'number') ?? 0

      // m3u8 移动文件夹
      if (taskType === DownloadKernel.TaskType.P2sp && taskExtType === DownloadKernel.TaskExtType.M3U8) {
        const taskName: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskName);
        const filePath = path.join(savePath, taskName);
        const newFilePath: string = path.join(newPath, taskName);
        src = filePath;
        dst = newFilePath;
      } else if (await SingleBtTaskNS.isSingleBtTask(task)) {
        let fileName: string = undefined;
        const btTask: DownloadKernel.IBtTask = task.getExtraObject() as DownloadKernel.IBtTask;
        const btFileList: Map<number, DownloadKernel.IBtFile> = btTask.getBtFileList();
        for (const [, btFile] of btFileList) {
          fileName = btFile.getBtFileAttribute(DownloadKernel.BtFileAttribute.FileName);
          break;
        }
        let filePath: string = path.join(savePath, fileName);
        const infoId: string = btTask.getInfoId();
        const torrentFileName: string = infoId + '.torrent';
        let torrentFilePath: string = path.join(savePath, torrentFileName);
        torrentFilePath = torrentFilePath + '\r\n';
        if (taskStatus === DownloadKernel.TaskStatus.Succeeded || taskStatus === DownloadKernel.TaskStatus.Seeding) {
          filePath = filePath + '\r\n';
          src = filePath + torrentFilePath;
        } else {
          let tdFilePath: string = filePath + '.bt.xltd';
          if (!(await FileSystemAWNS.existsAW(tdFilePath))) {
            tdFilePath = filePath + '.bt.td';
          }

          tdFilePath = tdFilePath + '\r\n';
          src = tdFilePath + torrentFilePath;
        }
        dst = newPath;
      } else {
        const taskName: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskName);
        let filePath: string = undefined;
        const singleBtFolder: string = await SingleBtTaskNS.getCorrectSingleBtFolder(task);
        if (singleBtFolder) {
          filePath = path.join(savePath, singleBtFolder);
        } else {
          filePath = path.join(savePath, taskName);
        }
        const newFilePath: string = path.join(newPath, taskName);
        if (taskStatus === DownloadKernel.TaskStatus.Succeeded || taskStatus === DownloadKernel.TaskStatus.Seeding) {
          if (taskType === DownloadKernel.TaskType.P2sp || taskType === DownloadKernel.TaskType.Emule) {
            src = filePath;
            dst = newFilePath;
          } else if (taskType === DownloadKernel.TaskType.Bt) {
            if (singleBtFolder) {
              const btTask: DownloadKernel.IBtTask = task.getExtraObject() as DownloadKernel.IBtTask;
              const infoId: string = btTask.getInfoId();
              const torrentFileName: string = infoId + '.torrent';
              let torrentFilePath: string = path.join(savePath, torrentFileName);
              torrentFilePath = torrentFilePath + '\r\n';
              src = filePath + '\r\n' + torrentFilePath;
            } else {
              src = filePath;
            }
            if (await FileSystemAWNS.existsAW(newFilePath)) {
              dst = newPath;
            } else {
              dst = newFilePath;
            }
          } else if (taskType === DownloadKernel.TaskType.Group) {
            ret = await this.moveGroupTask(taskId, newPath, bStart, banToast);
          }
        } else {
          if (taskType === DownloadKernel.TaskType.P2sp) {
            // 多个文件使用\r\n分隔
            const dataPath: string = filePath + '.xltd\r\n';
            const cfgPath: string = filePath + '.xltd.cfg\r\n';

            src = dataPath + cfgPath;
            dst = newPath;
          } else if (taskType === DownloadKernel.TaskType.Emule) {
            const dataPath: string = filePath + '.emule.xltd\r\n';
            const cfgPath: string = filePath + '.xlemule.cfg\r\n';

            src = dataPath + cfgPath;
            dst = newPath;
          } else if (taskType === DownloadKernel.TaskType.Bt) {
            if (singleBtFolder) {
              const btTask: DownloadKernel.IBtTask = task.getExtraObject() as DownloadKernel.IBtTask;
              const infoId: string = btTask.getInfoId();
              const torrentFileName: string = infoId + '.torrent';
              let torrentFilePath: string = path.join(savePath, torrentFileName);
              torrentFilePath = torrentFilePath + '\r\n';
              src = filePath + '\r\n' + torrentFilePath;
            } else {
              src = filePath;
            }
            if (await FileSystemAWNS.existsAW(newFilePath)) {
              dst = newPath;
            } else {
              dst = newFilePath;
            }

            if (dst.toLowerCase().indexOf(src.toLowerCase()) === 0) {
              logger.information('bt not allow to move child dir');
              if (!banToast) {
                clientModule.callServerFunction('ShowMessageToast', { message: '目标文件夹是源文件夹的子文件夹。', type: 'warning', id: 'movesucc', unique: true,}).catch();
              }
              break;
            }
          } else if (taskType === DownloadKernel.TaskType.Group) {
            ret = await this.moveGroupTask(taskId, newPath, bStart, banToast);
          }
        }
      }

      if (src !== undefined && dst !== undefined) {
        task.setTaskMovingState(true);
        ret = await this.moveTaskFiles(src, dst, taskId, taskType);
        task.setTaskMovingState(false);
      }
      if (ret && taskType !== DownloadKernel.TaskType.Group) {
        task.setSavePath(newPath);
        if (bStart) {
          // 是否开始任务不在这里,走调度策略
          this.eventContainer.fireEvent(move2Private ?
            NodeEventMesssageNS.eventTaskMove2PrivateCaterogyAndStart : NodeEventMesssageNS.eventTaskMoveOutPrivateCategoryAndStart, taskId);
        }
        if (!banToast) {
          clientModule.callServerFunction('ShowMessageToast', { message: '移动成功', type: 'success', id: 'movesucc', unique: true}).catch();
        }
      }
    } while (0);
    return ret;
  }

  private async moveGroupTask(taskId: number, newPath: string, bStart?: boolean, banToast?: boolean): Promise<boolean> {
    let ret: boolean = false;
    do {
      const task: Task = this.tasks.get(taskId) as Task;
      if (!task) {
        break;
      }

      const taskType: DownloadKernel.TaskType = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskType);
      if (taskType !== DownloadKernel.TaskType.Group) {
        break;
      }

      let savePath: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
      const taskName: string = task.getTaskAttribute(DownloadKernel.TaskAttribute.TaskName);
      const newFilePath: string = path.join(newPath, taskName);
      if (newFilePath.toLowerCase() === savePath.toLowerCase()) {
        logger.information('moveGroupTask newPath not change', newFilePath);
        // 未改变目录
        break;
      }

      // 由于当前是复制整个文件夹，不允许移动到子目录
      if (newFilePath.toLowerCase().indexOf(savePath.toLowerCase()) === 0) {
        logger.information('moveGroupTask not allow to move child dir');
        if (!banToast) {
          clientModule.callServerFunction('ShowMessageToast', { message: '目标文件夹是源文件夹的子文件夹。', type: 'warning', id: 'movesucc', unique: true,}).catch();
        }
        break;
      }

      const taskExtra: GroupTask = task.getExtraObject() as GroupTask;
      if (!taskExtra) {
        break;
      }

      const firstTask: Task = taskExtra.getSubTaskByIndex(0) as Task;
      if (!firstTask) {
        break;
      }

      const src: string = path.join(savePath, '*');
      task.setTaskMovingState(true);
      ret = await this.moveTaskFiles(src, newFilePath, taskId, taskType);
      task.setTaskMovingState(false);

      if (ret) {
        const taskCount: number = taskExtra.getSubTaskCount();
        for (let i: number = 0; i < taskCount; ++i) {
          const subTask: Task = taskExtra.getSubTaskByIndex(i) as Task;
          if (subTask) {
            let subTaskNewPath: string = newFilePath;
            const subSavePath: string = subTask.getTaskAttribute(DownloadKernel.TaskAttribute.SavePath);
            if (subSavePath === savePath) {
              // do nothing
            } else {
              if (subSavePath.length > savePath.length) {
                if (subSavePath.substr(0, savePath.length) === savePath) {
                  let subDir: string = subSavePath.slice(savePath.length);
                  if (subDir[0] === '\\') {
                    subDir = subDir.slice(1);
                    subTaskNewPath = path.join(newFilePath, subDir);
                  }
                }
              }
            }

            subTask.setSavePath(subTaskNewPath);
          }
        }
        await FileSystemAWNS.rmdirAW(savePath);
        task.setSavePath(newFilePath);
        if (bStart) {
          await this.startTask(taskId);
        }
        if (!banToast) {
          clientModule.callServerFunction('ShowMessageToast', { message: '移动成功', type: 'success', id: 'movesucc', unique: true,}).catch();
        }

        // 移除jssdk的下载目录
        let notSavePath: string = taskExtra.getUserData(TaskUserDataGUID.guidNotSavePath, 'notSavePath') as string;
        if (notSavePath) {
          savePath = path.normalize(savePath);
          notSavePath = path.normalize(notSavePath);
          if (savePath.indexOf(notSavePath) === 0) {
            const index: number = notSavePath.indexOf(':\\');
            if (index !== -1) {
              const driverRoot: string = notSavePath.substr(0, index + ':\\'.length);
              if (driverRoot) {
                FileSystemAWNS.deleteEmptySubDirs(notSavePath, driverRoot).catch();
              }
            }
          }
        }
      }
    } while (0);
    return ret;
  }

  private splitLastChar(url: string): string {
    let ret: string = url;
    if (Common.isThunderPrivateUrl(url)) {
      if (url.lastIndexOf('==') === url.length - 2) {
        ret = url.substr(0, url.length - 2);
      } else if (url.lastIndexOf('=') === url.length - 1) {
        ret = url.substr(0, url.length - 1);
      }
    }
    return ret;
  }

  private removeStartTask(task: DownloadKernel.ITask): void {
    // console.error('@@@@@@@@@@ remove ', task.getId(), task.getTaskName(), task.getTaskStatus());
    this.startTasks.delete(task?.getId());
  }

  private async createGroupTask(
    newTask: Task,
    newTaskBaseInfo: DownloadKernel.INewTaskBaseInfo,
    newGroupTaskInfo: DownloadKernel.INewGroupTaskInfo,
    categoryId: number
  ): Promise<boolean> {
    let ret: boolean = false;

    const transactionId: number = ThunderKernel.beginTransaction();
    do {
      newTask.createGroupTask(false, newTaskBaseInfo, newGroupTaskInfo, categoryId);
      const taskExtra: GroupTask = newTask.getExtraObject() as GroupTask;
      if (!taskExtra) {
        break;
      }
      await this.createBatchTask(newGroupTaskInfo.subTaskList, newTask);
      ret = true;
    } while (0);
    ThunderKernel.commitTransaction(transactionId);

    return ret;
  }

  private getTaskCfgType(taskType: DownloadKernel.TaskType): DownloadKernel.TaskCfgType {
    let cfgType: DownloadKernel.TaskCfgType = DownloadKernel.TaskCfgType.Invalid;
    do {
      if (taskType === DownloadKernel.TaskType.P2sp) {
        cfgType = DownloadKernel.TaskCfgType.P2sp;
        break;
      }
      if (taskType === DownloadKernel.TaskType.Emule) {
        cfgType = DownloadKernel.TaskCfgType.Emule;
        break;
      }
    } while (0);
    return cfgType;
  }

  // 判断是否需要重命名
  private async getTaskName(
    taskType: DownloadKernel.TaskType,
    savePath: string,
    taskName: string,
    index: number,
    origin: string
  ): Promise<ITaskNameResult> {
    let newName: string = '';
    while (true) {
      if (taskType === DownloadKernel.TaskType.P2sp || taskType === DownloadKernel.TaskType.Emule) {
        const extName: string = path.extname(taskName);
        const fileName: string = path.basename(taskName, extName);
        if (index === 0) {
          newName = taskName;
        } else {
          newName = fileName + '(' + index.toString() + ')' + extName;
        }
      } else {
        // bt/group 主任务会进来
        if (index === 0) {
          newName = taskName;
        } else {
          newName = taskName + '(' + index.toString() + ')';
        }
        // let filePath: string = path.join(savePath, newName);
        // let stats: fs.Stats = await FileSystemAWNS.lstatAW(filePath);
        // if (stats && stats.isDirectory()) {
        //   // 本地已经存在这个文件夹了，不需要重命名
        //   newName = taskName;
        //   break;
        // }
      }
      let cfgExist: boolean = false;
      const tempPath: string = path.join(savePath, newName);
      if (origin !== 'TD' && origin !== 'file_drag') {
        // 导入任务不判断配置是否存在
        const cfgType: DownloadKernel.TaskCfgType = this.getTaskCfgType(taskType);
        if (cfgType !== DownloadKernel.TaskCfgType.Invalid) {
          cfgExist = await ThunderKernel.isDownloadTaskCfgFileExist(cfgType, tempPath);
        }
      }
      if (!cfgExist && !(await FileSystemAWNS.existsAW(tempPath))) {
        break;
      }
      ++index;
    }
    return { newName: newName, index: index };
  }

  // 是否非当前私人空间任务，
  private isOtherPrivateSpaceTask(task: DownloadKernel.ITask): boolean {
    const currentPrivateCategory: DownloadKernel.ICategory = categoryManager.getCurrentPrivateCategory();
    const currentUserName: string = currentPrivateCategory ? currentPrivateCategory.getUserName() : '';
    const userName: string = task.getUserName();
    // 非私人空间任务或者是当前私人空间的任务才返回结果
    if (!userName || userName === '' || currentUserName === userName) {
      return false;
    }
    return true;
  }

  private notifySDKCrash(): void {
    MessageBox.confirm({
      title: '迅雷下载引擎发生故障!',
      type: MessageBox.ConfirmType.Error,
      content: '迅雷开发者会在最新版本中修复各种故障<br>因此建议您尝试“检查更新”<br>如果您已安装最新版本,请尝试“重启电脑”',
      okText: '检查更新',
      cancelText: '重启电脑'
    })
      .then((payload: MessageBoxNS.IResolvePayload) => {
        if (payload.action === MessageBoxNS.Action.OK) {
          thunderHelper.startManualUpdateWithUI();
        } else if (payload.action === MessageBoxNS.Action.Cancel) {
          thunderHelper.rebootdMachine();
        }
      })
      .catch();
  }

  private async downloadSdkInited(): Promise<boolean> {
    return new Promise<boolean>(
      (resolve: (value: boolean) => void): void => {
        if (this.isInitDkFinish) {
          resolve(this.isInitDkSuccess);
        } else {
          this.eventContainer.attachOnceListener(NodeEventMesssageNS.eventDownloadKernelInitEnd, () => {
            resolve(this.isInitDkSuccess);
          });
        }
      }
    );
  }
}

export namespace DownloadKernelTaskManager {
  const taskManager: DownloadKernel.ITaskManager = new TaskManager();
  export function getTaskManager(): DownloadKernel.ITaskManager {
    return taskManager;
  }
}
