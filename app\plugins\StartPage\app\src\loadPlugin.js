// eslint-disable-next-line @typescript-eslint/no-var-requires

const headTags = document.getElementsByTagName('head');
if (headTags) {
  const head = headTags[0];
  if (head) {
    const link = document.createElement('link');
    link.href = `${__dirname}/index.css`;
    link.rel = 'stylesheet';
    link.type = 'text/css';
    head.appendChild(link);
  }
}

const src = `${__dirname}/index`;
__non_webpack_require__(src);