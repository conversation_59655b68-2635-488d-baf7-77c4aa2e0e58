'use strict';

const path = require('path');
const webpack = require('webpack');
const ExtractTextPlugin = require('extract-text-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');
// https://vue-loader.vuejs.org/migrating.html#migrating-from-v14
const VueLoaderPlugin = require('vue-loader/lib/plugin');
const StatsPlugin = require('stats-webpack-plugin');
const CircularDependencyPlugin = require('circular-dependency-plugin');
const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');

const packageJSON = require('../package.json');
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(path.resolve(packageJSON.build.outDir), buildTarget, 'TaskCenter', packageJSON.version);

const isBuildProduction = process.env.BUILD_ENV === 'production';
const isDeveloping = process.env.RUN_ENV === 'development';
const isAnalyze = process.env.ANALYZE === '1';

const output = isDeveloping ?  { filename: '[name].js', libraryTarget: 'commonjs2', publicPath: '/', path: outDir } // 热更新需要加上publicPath，不然会拉不到mainfest.json
                            : { filename: '[name].js', libraryTarget: 'commonjs2', path: outDir }

let commonRendererConfig = {
  // for webpack v4
  mode: isBuildProduction ? 'production' : 'development',
  target: 'electron-renderer',
  module: {
    rules: [
      {
        test: /\.css$/,
        use: ExtractTextPlugin.extract({
          fallback: 'style-loader',
          use: 'css-loader'
        })
      },
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: {
          extractCSS: true,
          loaders: {
            // Since sass-loader (weirdly) has SCSS as its default parse mode, we map
            // the "scss" and "sass" values for the lang attribute to the right configs here.
            // other preprocessors should work out of the box, no loader config like this necessary.
            scss: 'vue-style-loader!css-loader!sass-loader',
            sass: 'vue-style-loader!css-loader!sass-loader?indentedSyntax',
            ts: 'ts-loader!tslint-loader'
          }
        }
      },
      {
        test: /\.ts$/,
        loader: 'ts-loader',
        exclude: /node_modules/,
        options: {
          // appendTsSuffixTo的含义是碰到.vue结尾的文件时，加上.ts的后缀
          // https://github.com/s-panferov/awesome-typescript-loader/issues/356
          // awesome-typescript-loader暂时不支持.vue文件
          // 这样ts-loader就会去处理.vue文件中的ts代码。
          appendTsSuffixTo: [/\.vue$/],
          transpileOnly: true,
          experimentalWatchApi: true
        }
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        use: {
          loader: 'url-loader',
          options: { fallback: 'file-loader', limit: 100000, name: 'imgs/[name]--[folder].[ext]', publicPath: `../../plugins/TaskCenter/${packageJSON.version}.asar` }
        }
      },
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
        loader: 'url-loader',
        options: { fallback: 'file-loader', limit: 100000, name: 'media/[name]--[folder].[ext]', publicPath: '../' }
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        use: {
          loader: 'url-loader',
          options: {
            fallback: 'file-loader',
            limit: 100000,
            name: 'fonts/[name]--[folder].[ext]',
            publicPath: '../'
          }
        }
      }
    ]
  },
  output: output,
  resolve: {
    extensions: ['.ts', '.js', '.vue', '.json', '.css'],
    symlinks: false,
    alias: { '@': path.join(__dirname, '../src') }
  },
  plugins: [
    new ForkTsCheckerWebpackPlugin({
      vue: true
    }),
    new VueLoaderPlugin(),
    new ExtractTextPlugin({
      filename: '[name].css'
    }),
    new CircularDependencyPlugin({
      // exclude detection of files based on a RegExp
      exclude: /node_modules/,
      // add errors to webpack instead of warnings
      failOnError: true,
      // allow import cycles that include an asyncronous import,
      // e.g. via import(/* webpackMode: "weak" */ './file.js')
      allowAsyncCycles: false,
      // set the current working directory for displaying module paths
      cwd: process.cwd()
    }),
    ...(isAnalyze ? [new StatsPlugin('../../../../../app/stats.json')] : [])
  ],
  // 避免mock process、__dirname等node环境api
  node: false,
  stats: 'errors-only',
  bail: true,
  devtool: isBuildProduction ? 'source-map': 'eval-source-map',
  optimization: {
    removeAvailableModules: false,
    removeEmptyChunks: false,
    ...(isBuildProduction
      ? {
          minimizer: [
            new UglifyJsPlugin({
              sourceMap: buildTarget === 'Release' || buildTarget === 'ProductRelease',
              cache: true,
              parallel: true,
              uglifyOptions: {
                ecma: 6,
                compress: {
                  warnings: false
                }
              }
            }),
            new OptimizeCSSAssetsPlugin()
          ]
        }
      : { minimize: false })
  }
};

if (isDeveloping) {
  commonRendererConfig.plugins.push(new webpack.HotModuleReplacementPlugin());
}

module.exports = commonRendererConfig;
