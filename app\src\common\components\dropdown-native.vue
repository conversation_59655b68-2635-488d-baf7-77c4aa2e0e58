<template>
  <td-dropdown
    v-bind="$attrs"
    v-on="$listeners"
    :menus="menus"
    custom-menu-enabled
    @drop-click="handleDropClick"
  >
    <slot></slot>
  </td-dropdown>
</template>

<script lang="ts">
import asyncRemote = require('@xunlei/async-remote');
import { asyncRemoteCall } from '@/common/renderer-process-call';
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';

@Component
export default class DropdownNative extends Vue {
  /** 下拉菜单项 */
  @Prop() menus: string[];

  /** 下拉菜单控件 */
  dropdown: any;

  @Watch('menus')
  async onMenusChanged(): Promise<void> {
    await this.appendContent();
  }

  /** 添加下拉菜单内容 */
  async appendContent(): Promise<void> {
    if (!this.dropdown) {
      let DropdownWindow: any = await asyncRemote.electron.DropdownWindow.__resolve(); // tslint:disable-line
      this.dropdown = await new DropdownWindow();
    }
    await this.dropdown.clear();

    let DropdownWindowContent: any = await asyncRemote.electron.DropdownWindowContent.__resolve(); // tslint:disable-line
    for (let menu of this.menus) {
      // 增加分割线
      if (menu === 'separator') {
        let dropdownwc: any = await new DropdownWindowContent({
          type: 'separator'
        });
        await this.dropdown.append(dropdownwc);
      } else {
        let dropdownwc: any = await new DropdownWindowContent({
          label: menu.toString(),
          click: (): void => {
            this.$emit('input', menu, true);
          }
        });
        await this.dropdown.append(dropdownwc);
      }
    }
  }

  /** 处理下拉按钮点击 */
  async handleDropClick(): Promise<void> {
    if (!this.dropdown) {
      await this.appendContent();
    }

    let rect: ClientRect = this.$el.getBoundingClientRect();
    const { DropDownWindowSkinNS } = await import('@/common/components-skin/drop-down-window'); // tslint:disable-line
    let left: number = Math.round(((rect.width > 800 ? 800 : rect.width) - 100) / 2);
    DropDownWindowSkinNS.setStyle(this.dropdown, {
      windowPreference: {
        marginLeft: left,
        marginRight: 0,
        fontHeight: -16,
        cornerRadius: 3,
        stringWidth: Math.round(rect.width) - 39 - left
      }
    });
    let currentWnd: any = await asyncRemoteCall.getCurrentWindow();
    this.dropdown.popup({
      window: currentWnd,
      x: Math.round(rect.left),
      y: Math.round(rect.top + rect.height + 4)
    });
  }
}
</script>
