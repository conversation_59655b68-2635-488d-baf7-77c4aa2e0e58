import { Logger } from './common/logger';
import childProcess = require('child_process');
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
//import { ThunderHelper } from './common/util-helper';
import { setTimeout } from 'timers';
import path = require('path');
import requireNodeFile from './common/require-node-file';
import { ThunderHelper } from './common/util-helper';
import https = require('https');
import axios from 'axios';

const contextName: string = 'RegistryPlugin'; // 这个名字需要和config.json 一致，由于客户端写死了，所以我们不能修改这个
const logger: Logger = Logger.getLogger(contextName);

let napiDir: string = __dirname;
if (napiDir.endsWith('.asar')) {
  napiDir += '.unpacked';
}

const registryHelper = requireNodeFile(path.join(napiDir, 'bin/RegistryHelper.node'));
const thunderHelper: any = requireNodeFile(path.join(__rootDir, '../bin/ThunderHelper.node'));

const context: { name: string } = { name: contextName };

// 游戏信息
interface SoftwareInfo {
  name: string;                      // 名称
  regPath: string;                   // 注册表路径
};

// 检查一个注册表是否存在, 判断是否安装
function  checkRegExist(regPath: string): string {
  if (regPath === '') { 
    return '';
  }
  
  let exist: string ='';
  try {
    exist = registryHelper.readRegString(regPath)
  } catch (error) { 
    exist = '';
  }
  return exist;
}

async function confirmCmdTest(): Promise<void> { 
 return new Promise((resolve, reject) => {
    const subprocess = childProcess.exec('winget list --name test', (err, stdout, stderr) => {
      if (err) {
        logger.information("======RegistryPlugin===confirmCmdTest===err=====",err);
        resolve();
        return;
      }

      if (stderr) {
        logger.information("======RegistryPlugin==confirmCmdTest===stderr=====",stderr);
        resolve();
        return;
      }

      if (stdout) { 
        if (!(/\[Y\]/.test(stdout))) { 
          logger.information("======RegistryPlugin===confirmCmdTest===stdout==!(/\[Y\]/.test(stdout)==",stdout);
          resolve();
        }
      }
    });

    // subprocess.stdin.write('N' + '\n');
    subprocess.stdout.on('data', (data) => {
      if (data && /\[Y\]/.test(data)) {
        logger.information("======RegistryPlugin==confirmCmdTest===stdout.on===/\[Y\]/.test(data)==",data);
        subprocess.stdin.write('Y' + '\n');
      } else {
        logger.information("======RegistryPlugin===confirmCmdTest===stdout.on==data==", data);
      }
    })
  });
}


async function getCmdOut(cmd: string): Promise<string> {
  return new Promise((resolve, reject)=>{
    childProcess.exec(cmd, (error,stdout,stderr)=>{
      if (error) {
        resolve('');
        return;
      }
      if (stderr) {
        resolve('');
        return;
      }
      resolve(stdout);
    });
  });
}


async function checkSoftExist(softName: string): Promise<boolean> {
  let bExist: boolean = false;
  let runCmd: string = 'winget list --name ' + softName;  

  let runResult: string = '';
  //let runResultList: string[] = [];
  logger.information("======RegistryPlugin====checkSoftExist=softName:%s=====", softName);
  try {
    runResult = await getCmdOut(runCmd);
  } catch (error) { 
    runResult = '';
  }
  logger.information("======RegistryPlugin====runResult:%s======", runResult);
  const exp = new RegExp(`${softName.toLowerCase()}\\s+.+`);
  if (exp.test(runResult?.toLowerCase())) {
    bExist = true;
  }
  logger.information("======RegistryPlugin====checkSoftExist=softName:%s==Exist:%s===", softName,bExist?'1':'0');
  return bExist;
}

/**
 * 字符串插入函数
 * 自己确认插入位置，若位置不对会抛出异常。
 * @param {string} src 原字符串
 * @param {number} pos 插入的位置，可正着数，可倒着数，正数第一个元素为0，倒着数第一个元素为-1
 * @param {string} val 插入的值
 */
function insertStr(src: string, pos: number, val: string): string {
  if (pos < -src.length - 1 || pos > src.length) { 
    return src;
  }

  if (pos >= 0) {
    return src.slice(0, pos) + val + src.slice(pos);
  }
   
  else { 
    return src.slice(0, src.length + 1 + pos) + val + src.slice(src.length + 1 + pos);
  }
}

async function run(): Promise<void> {
  do {
    logger.information('======RegistryPlugin=====RegistryPlugin start======');

    await confirmCmdTest();
    let remoteItems: any[] = await getCheckItemFromRemote();
    // 能够通过注册表获取的软件列表
    let softwareList: SoftwareInfo[] = [
      {
        name:'steam',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam\\DisplayIcon'
      },
      {
        name:'Epic',
        regPath: 'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run\\EpicGamesLauncher'
      },
      {
        name:'wegame',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\WeGame\\DisplayIcon'
      },
      {
        name:'杉果游戏',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\sonkwo\\DisplayIcon'
      },
      {
        name:'方块游戏',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\方块游戏平台 3.8.8.1\\DisplayIcon'
      },
      {
        name:'蒸汽平台',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam China\\DisplayIcon'
      },
      {
        name:'腾讯手游助手',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MobileGamePC\\DisplayIcon'
      },
      {
        name:'网易mumu模拟器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Classes\\Applications\\Nemux.exe\\shell\\open\\command\\'
      },
      {
        name:'雷电模拟器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\leidian9\\DisplayIcon'
      },
      {
        name:'夜神模拟器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Nox\\DisplayIcon'
      },
      {
        name:'逍遥模拟器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\MEmu\\DisplayIcon'
      },
      {
        name:'蓝叠模拟器',
        regPath: 'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\BlueStacks X\\DisplayIcon'
      },
      {
        name:'按键精灵',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\按键精灵_is1\\DisplayIcon'
      },
      {
        name:'魔兽世界大脚',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\WOW大脚\\DisplayIcon'
      },
      {
        name:'wemod',
        regPath: 'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\WeMod\\DisplayIcon'
      },
      {
        name:'风灵月影',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\5f0074e2-681b-5043-b773-e2d270e94cdd\\DisplayIcon'
      },
      {
        name:'gamebuff',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\GameBuff\\DisplayIcon'
      },
      {
        name:'QQ游戏大厅',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\QQ游戏\\DisplayIcon'
      },
      {
        name:'360游戏大厅',
        regPath: 'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\360Game5\\DisplayIcon'
      },
      {
        name:'37游戏盒子',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\37网游盒子\\DisplayIcon'
      },
      {
        name:'996传奇盒子',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\996box\\DisplayIcon'
      },
      {
        name:'Uplay',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Uplay\\DisplayIcon'
      },
      {
        name:'暴雪战网',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Battle.net\\DisplayIcon'
      },
      {
        name:'Rockstar',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Rockstar Games Launcher\\DisplayIcon'
      },
      {
        name:'拳头',
        regPath: 'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Riot Game Riot_Client.\\UninstallString'
      },
      {
        name:'EA',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Electronic Arts\\EA Desktop\\LauncherAppPath'
      },
      {
        name:'网易uu加速器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\NeteaseGacc\\DisplayIcon'
      },
      {
        name:'迅游加速器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\迅游加速器\\DisplayIcon'
      },
      {
        name:'海豚网易加速器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Dolphin\\DisplayIcon'
      },
      {
        name:'雷神加速器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\{02FA43E9-DF15-4B93-8491-20BACB83DFD0}_is1\\DisplayIcon'
      },
      {
        name:'biubiu加速器',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\biubiu\\DisplayIcon'
      },
      {
        name:'应用宝',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Androws\\DisplayIcon'
      },

      // 加速器支持
      {
        name:'APEX',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1172470\\InstallLocation'
      },
      {
        name:'PUBG',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 578080\\InstallLocation'
      },
      {
        name:'CSGO',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 730\\InstallLocation'
      },
      {
        name:'致命公司',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1966720\\InstallLocation'
      },
      {
        name:'幻兽帕鲁',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1623730\\InstallLocation'
      },
      {
        name:'The Final',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2073850\\InstallLocation'
      },
      {
        name:'暗黑破坏神4',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2344520\\InstallLocation'
      },
      {
        name:'魔兽世界',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\World of Warcraft\\DisplayIcon'
      },
      {
        name:'守望先锋2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2357570\\InstallLocation'
      },
      {
        name:'龙之信条2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2054970\\InstallLocation'
      },
      {
        name:'永劫无间',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1203220\\InstallLocation'
      },
      {
        name:'无畏契约',
        regPath: 'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Riot Game valorant.live\\InstallLocation'
      },
      {
        name:'鹅鸭杀',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1568590\\InstallLocation'
      },
      {
        name:'GTA5',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 271590\\InstallLocation'
      },
      {
        name:'地平线5',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1551360\\InstallLocation'
      },
      {
        name:'英雄联盟',
        regPath: 'HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Riot Game league_of_legends.live\\InstallLocation'
      },
      {
        name:'Warframe（星际战甲）',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 230410\\InstallLocation'
      },
      {
        name:'使命召唤3',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1938090\\InstallLocation'
      },
      {
        name:'绝地潜兵2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 553850\\InstallLocation'
      },
      {
        name:'Russian Fishing 4（俄罗斯钓鱼4）',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 766570\\InstallLocation'
      },
      {
        name:'炉石传说',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Hearthstone\\DisplayIcon'
      },
      {
        name:'War Thunder（战争雷霆）',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 236390\\InstallLocation'
      },
      {
        name:'饥荒联机版',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 322330\\InstallLocation'
      },
      {
        name:'双人成行',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1426210\\InstallLocation'
      },
      {
        name:'博德之门3',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1086940\\InstallLocation'
      },
      {
        name:'NBA 2K24',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2338770\\InstallLocation'
      },
      {
        name:'黎明杀机',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 381210\\InstallLocation'
      },
      {
        name:'胡闹厨房：全都好吃',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1243830\\InstallLocation'
      },
      {
        name:'猛兽派对',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1260320\\InstallLocation'
      },
      {
        name:'背包乱斗：福西法的宝藏',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2427700\\InstallLocation'
      },
      {
        name:'彩虹六号：围攻',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 359550\\InstallLocation'
      },
      {
        name:'战地2042',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1517290\\InstallLocation'
      },
      {
        name:'DOTA2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 570\\InstallLocation'
      },
      {
        name:'游戏王:大师决斗',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1449850\\InstallLocation'
      },
      {
        name:'碧蓝幻想',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 881020\\InstallLocation'
      },
      {
        name:'World of Warships（战舰世界）',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 552990\\InstallLocation'
      },
      {
        name:'星际争霸2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\StarCraft II\\DisplayIcon'
      },
      {
        name:'EA SPORTS FC 24',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2195250\\InstallLocation'
      },
      {
        name:'怪物猎人：世界',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 582010\\InstallLocation'
      },
      {
        name:'地平线4',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1293830\\InstallLocation'
      },
      {
        name:'战地5',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1238810\\InstallLocation'
      },
      {
        name:'荒野大镖客2：救赎',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1174180\\InstallLocation'
      },
      {
        name:'7日杀',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 251570\\InstallLocation'
      },
      {
        name:'街头霸王6',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1364780\\InstallLocation'
      },
      {
        name:'SMITE（神之浩劫）',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 386360\\InstallLocation'
      },
      {
        name:'森林之子',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1326470\\InstallLocation'
      },
      {
        name:'使命召唤-战争地带',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1938090\\InstallLocation'
      },
      {
        name:'命运2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1085660\\InstallLocation'
      },
      {
        name:'Dead Cells（重生细胞）',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 588650\\InstallLocation'
      },
      {
        name:'女神异闻录5',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2254740\\InstallLocation'
      },
      {
        name:'Rust',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 252490\\InstallLocation'
      },
      {
        name:'盗贼之海2023',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1172620\\InstallLocation'
      },
      {
        name:'夜鸦国际服',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\NightCrowsGLauncher\\DisplayIcon'
      },
      {
        name:'星露谷物语',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 413150\\InstallLocation'
      },
      {
        name:'军团要塞2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 440\\InstallLocation'
      },
      {
        name:'铁拳8',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1778820\\InstallLocation'
      },
      {
        name:'模拟人生4',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1222670\\InstallLocation'
      },
      {
        name:'夜下降生2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2076010\\InstallLocation'
      },
      {
        name:'文明6',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 289070\\InstallLocation'
      },
      {
        name:'三位一体',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1436700\\InstallLocation'
      },
      {
        name:'人类一败涂地',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 477160\\InstallLocation'
      },
      {
        name:'雾锁王国',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1203620\\InstallLocation'
      },
      {
        name:'战地1',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1238840\\InstallLocation'
      },
      {
        name:'恐鬼症',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 739630\\InstallLocation'
      },
      {
        name:'怪物猎人：崛起',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1446780\\InstallLocation'
      },
      {
        name:'僵尸毁灭工程',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 108600\\InstallLocation'
      },
      {
        name:'上古卷轴OL',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 306130\\InstallLocation'
      },
      {
        name:'清零计划2：天启派对',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2351560\\InstallLocation'
      },
      {
        name:'严阵以待',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 1144200\\InstallLocation'
      },
      {
        name:'自杀小队：消灭正义联盟',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 315210\\InstallLocation'
      },
      {
        name:'泰拉瑞亚',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 105600\\InstallLocation'
      },
      {
        name:'Valheim',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 892970\\InstallLocation'
      },
      {
        name:'英灵乱战',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 291550\\InstallLocation'
      },
      {
        name:'神力科莎',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 244210\\InstallLocation'
      },
      {
        name:'足球经理2024',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 2252570\\InstallLocation'
      },
      {
        name:'帝国时代2',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 813780\\InstallLocation'
      },
      {
        name:'气球塔防6',
        regPath: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Steam App 960090\\InstallLocation'
      },
      {
        name:"IDM",
        regPath:"HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Internet Download Manager\\DisplayIcon"
      },
      {
        name:"FDM",
        regPath:"HKEY_CURRENT_USER\\SOFTWARE\\Classes\\fdm\\DefaultIcon\\"
      }
    ]; 
    if (remoteItems.length > 0) {
      softwareList = remoteItems.concat(softwareList);
    }

    // 微软商店软件注册表不规律，通过cmd 获取安装软件列表匹配
    let microSoftList: string[] = ['WhatsApp','FaceBook','Twitter']

    let programList: string = '';
    for (let item of softwareList) { 
      let regPath = item.regPath;
      let exist: string = checkRegExist(regPath);
      logger.information('======RegistryPlugin====regPath:%s exist:%s======', regPath, exist ? 'true' : 'false');
      if (!exist) {
        let softwareIndex = regPath.indexOf('SOFTWARE');
        if ((regPath.indexOf('WOW6432Node') < 0) && (softwareIndex > 0) && (softwareIndex + 9 < regPath.length - 1) ) { 
          regPath = insertStr(regPath, softwareIndex + 9, 'WOW6432Node\\');
          exist = checkRegExist(regPath);
          logger.information('======RegistryPlugin===regPath:%s,exist:%s======',regPath , exist ? 'true' : 'false');
        }
      }

      if (exist) { 
        if (programList) {
          programList += `|${item.name}`;
        } else { 
          programList += `${item.name}`;
        }
      }
    }

    for (let item of microSoftList) { 
      let exist: boolean = await checkSoftExist(item);
      if (exist) { 
        if (programList) {
          programList += `|${item}`;
        } else { 
          programList += `${item}`;
        }
      }
    }

    // 编码
    if (programList) { 
      programList = encodeURIComponent(programList);
    }
    
    let strLanguage: string = navigator.language;
    if (!strLanguage) { 
      strLanguage = 'zh-CN';
    }

    strLanguage = encodeURIComponent(strLanguage);
    // 埋点
    let extData: string = `program_list=${programList}`;
    extData += `,sys_lan=${strLanguage}`
    extData += `,is64bits=${thunderHelper.is64bitSystem() ? 1 : 0}`
    clientModule.callServerFunction('TrackEvent', 'xl_game_center_event', 'gamebox_registry_list', '', 0, 0, 0, 0, extData).catch();
  } while (0);
}

async function getCheckItemFromRemote(): Promise<any> {
  let ret = [];
  do {
    let response: any = null;
    try {
      axios.defaults.adapter = require('axios/lib/adapters/http');
      const httpsAgent: https.Agent = new https.Agent({ rejectUnauthorized: false });
      response = await axios.get('https://static-xl.a.88cdn.com/json/check_reg.json', { timeout: 10000, httpsAgent });
    } catch (error) {
      logger.warning(error);
    }

    if (
      response !== null &&
      response.status !== undefined &&
      response.status === 200 &&
      response.data !== undefined &&
      response.data !== null
    ) {
      if (response.data) {
        ret = response.data;
      }
    }
  } while (0);
  return ret;
}


enum ProxyStatus {
  Proxy_None,
  Proxy_Vpn = 0b1,  // 下载助手 11.2.6的需求，当前版本已经从主菜单删除该选项了
  Proxy_IE = 0b10,
  Proxy_All = Proxy_Vpn | Proxy_IE,
}

async function getTaskList(): Promise<string[]> {
  return new Promise((resolve,reject) => {
    childProcess.exec('tasklist /fo csv', (err, stdout, stderr) => {
      if (err) {
        resolve([]);
      }

      if (stderr) {
        resolve([]);
      }

      const r = stdout.split('\n').map((line) => {
        const item = line.trim().split(','); // "Xmp.exe","28628","Console","1","1,176 K"
        const name = item[0];
        return name;
      });
      resolve(r) 
    })
  });
}

async function isVpnProcessRunning(): Promise<{ exist: boolean; name?: string }> {
  let exist: boolean = false;
  let findName: string = '';
  const processNames = await getTaskList();
  if (processNames?.length) {
    const vpnList: string[] = ['"ExpressVPN.SystemService.exe"', '"Surfshark.Service.exe"', '"openvpnserv.exe"', '"Shadowsocks.exe"', '"Panda.exe"', '"NordVPN.exe"', '"FlowVPN.exe"', '"PureVPNService.exe"', '"PrivateVpn.exe"', '"Dashboard.Service.exe"']
    // const vpnList: string[] = ['"ExpressVPN.SystemService', 'surfsharkVPN', '"openvpnserv', '"Shadowsocks', 'pandaVPN', 'nordVPN', 'flowVPN', 'pureVPN', 'privateVPN', 'cyberghost']
    for (let name of processNames) {
      if (!name) {
        continue;
      }

      for (let vpn of vpnList) {
        if (name.toLowerCase().indexOf(vpn.toLowerCase()) === 0) {
          exist = true;
          findName = name;
          break;
        }
      }

      if (exist) {
        break;
      }
    }
  }

  return { exist, name: findName };
}

async function checkVpn(): Promise<void> {
  let status = ProxyStatus.Proxy_None;
  const result = await isVpnProcessRunning();
  if (result?.exist) {
    status |= ProxyStatus.Proxy_Vpn;
  }

  if (ThunderHelper.getIEProxy()) {
    status |= ProxyStatus.Proxy_IE;
  }

  const extData = 'status=' + status + ',vpn_process_name=' + result.name;
  logger.information('trackevent start_page vpn_status', extData);
  clientModule.callServerFunction('TrackEvent', 'start_page', 'vpn_status', '', 0, 0, 0, 0, extData).catch();
}

async function pollingCheck(): Promise<void> {
  let interval = 1 * 60 * 60 * 1000;
  let test = await clientModule.callServerFunction('GetConfigValue', 'ConfigRemoteGlobal', 'VPNInterval');
  if (test) {
    test = Number(test);
    if (!isNaN(test) && test > 0) {
      interval = test * 1000;
    }
  }

  setInterval(() => {
    // 一小时轮询
    logger.information('polling check');
    checkVpn();
  }, interval);
}

setTimeout(() => { 
  clientModule.start(context);
  checkVpn().catch();
  pollingCheck().catch();
  run().catch();
}, 1000);


