import { VNode, VNodeDirective } from 'vue';

const visiblityDirective: any = {
  bind(el: any, { value }: VNodeDirective, vnode: VNode): void {
    el.__vOriginalOpacity = el.style.opacity;
    el.__vOriginalPointerEvents = el.style.pointerEvents;
    el.style.opacity = value ? '1' : '0';
    el.style.pointerEvents = value ? 'auto' : 'none';
  },

  update(el: any, { value, oldValue }: VNodeDirective, vnode: VNode): void {
    /* istanbul ignore if */
    if (value === oldValue) return;
    el.style.opacity = value ? '1' : '0';
    el.style.pointerEvents = value ? 'auto' : 'none';
  },

  unbind(el: any, binding: VNodeDirective, vnode: VNode, oldVnode: VNode, isDestroy: boolean): void {
    if (!isDestroy) {
      el.style.opacity = el.__vOriginalOpacity;
      el.style.pointerEvents = el.__vOriginalPointerEvents;
    }
  }
};

export default visiblityDirective;
