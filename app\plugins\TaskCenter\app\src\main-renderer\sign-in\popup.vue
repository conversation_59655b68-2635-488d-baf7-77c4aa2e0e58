<template>
  <div class="xly-sign-in">
    <header class="xly-sign-in__header">
      <div class="xly-sign-in__gift"></div>
      <div class="xly-sign-in__content">
        <!-- 一周内连续签到满 7 天 -->
        <template v-if="isCompleteFullWeek">
          <h2>恭喜你获得1天超级会员</h2>
          <p>已发放到【奖品】，下周再接再厉</p>
        </template>

        <template v-else>
          <h2>{{ headerTitle }}</h2>
          <p>
            已签<em>{{ alreadyCheckInList.length }}</em>天，
            还差<em>{{ 7 - alreadyCheckInList.length }}</em>天，可领超级会员
          </p>
        </template>
      </div>
      <span class="xly-sign-in__rule" @click="handleShowRules">规则</span>
    </header>

    <SignInRecord
      :todayWeek="todayWeek"
      :reSignedIndex="reSignedIndex"
      :enableRemind="enableRemind"
      :enableRemindDate="enableRemindDate"
      :alreadyCheckInList="alreadyCheckInList"
      :reCheckInCount="reCheckInCount"
      :dailyAwardList="dailyAwardList"
      @reCheckIn="handleCheckIn"
    />

    <div class="xly-sign-in__button">
      <td-button
        size="large"
        :disabled="todayIsAlreadyCheckIn"
        @click="handleCheckIn(todayWeek)"
      >
        {{ checkInButtonText }}
      </td-button>
      <td-button size="large" secondary @click="handleShowAwardList">奖品</td-button>
      <!-- 当日签到奖品 -->
      <div class="xly-popover" :class="{ 'show-award-popover': isShowAwardPopover }">
        <td-media align="middle">
          <img :src="awardPopoverData.icon" slot="media" alt="" />
          <h4>{{ awardPopoverData.name }}<template v-if="awardPopoverData.isShowDouble">*2</template></h4>
          <p class="is-warn">{{ awardPopoverData.usageLimitText }}</p>
        </td-media>
      </div>
    </div>

    <div class="xly-sign-in__footer">
      <div class="xly-sign-in__help">
        开启提醒，每日奖励 <span>翻倍</span>
        <div class="xly-title-hover">
          <i class="xly-icon-help"></i>
          <div class="xly-title-hover__inner">
            开启提醒，将打开「开机自启」功能，并在每天{{remindTimeHour}}:{{remindTimeMinute}}定时为您推送签到提醒通知
          </div>
        </div>
      </div>
      <div class="xly-sign-in__switch">
        <div class="xly-popover" :class="{ 'is-show': isShowRemindPopover }">
          <a class="td-dialog__close" @click="handleHideRemindPopover('close')">
            <i class="td-icon-close"></i>
          </a>
          <h3><i class="td-icon-warning"></i> 确定关闭提醒吗</h3>
          <div class="xly-popover__body">
            <p>关闭后，不再接收到签到提醒并失去翻倍奖励</p>
          </div>

          <div class="xly-popover__footer">
            <td-button @click="handleCloseRemind(true)">关闭</td-button>
            <td-button secondary @click="handleHideRemindPopover('not_yet')">暂不</td-button>
          </div>
        </div>

        <td-switch :value="enableRemind" @change="handleRemindSwitch"></td-switch>

        <div v-show="isShowRemindGuide" class="xly-sign-in__remind">开启提醒，不错过签到</div>
      </div>
    </div>

    <AwardList
      v-if="isShowList"
      :class="{ 'is-show': isShowList }"
      @back="isShowList = false"
    />
    <FinalAward
      v-if="isShowFinal"
      :class="{ 'is-show': isShowFinal }"
      :finalAwardList="finalAwardList"
      @back="isShowFinal = false"
      @openAwardList="handleOpenAwardList"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { setConfigValue, getConfigValue, showPushNotify, showMessageToast, MsgPriority } from '@/common/ipc'
import * as Dayjs from 'dayjs'
import * as SignInApi from '@/api/sign-in'
import type Parent from '../index.vue'

import AwardList from './award-list.vue'
import FinalAward from './final-award.vue'
import SignInRecord from './sign-in-record.vue'

const CONFIG_SECTION = 'TaskCenter_Activity_CheckIn'
const CONFIG_KEY_REMIND = 'EnableCheckInRemind'                       // 是否开启签到提醒
const CONFIG_KEY_REMIND_GUIDE = 'CheckInRemindGuideShow'              // 签到提醒引导是否展示过
const CONFIG_KEY_LAST_REMIND = 'EnableCheckInLastRemindDate'          // 最后一次签到提醒的时间
const CONFIG_KEY_REMIND_SET_DATE = 'EnableCheckInRemindSetDate'       // 开启签到提醒的时间

@Component({
  components: {
    AwardList,
    FinalAward,
    SignInRecord
  }
})
export default class SignInPopUp extends Vue {
  @Prop() isShow!: boolean
  @Prop() gameId!: number
  @Prop() todayWeek!: number
  @Prop() signedIndex!: number[]
  @Prop() reSignedIndex!: number[]
  @Prop() reSignInCount!: number
  @Prop() remindTimeHour!: number
  @Prop() remindTimeMinute!: number
  @Prop() dailyAwardList!: any[]
  @Prop() finalAwardList!: any[]
  @Prop() showSignInEntry!: boolean

  // 类型定义
  $parent!: Parent

  headerTitle = '签到领好礼'                   // 头部文案
  enableRemind = false                        // 是否开启提醒
  isShowRemindPopover = false                 // 是否显示关闭提醒时的二次确认
  isShowRemindGuide = false                   // 是否显示提醒的引导
  isShowList = false                          // 是否显示奖品列表
  isShowFinal = false                         // 是否显示连续签到 7 天的最终奖品

  enableRemindDate: number = Infinity         // 设置开启提醒的时间
  lastRemindDate: number = 0                  // 最后一次签到提醒的时间
  isRequestCheckIn = false                    // 是否正在处理签到请求

  alreadyCheckInList: number[] = []           // 已签到列表
  reCheckInCount = 0                          // 可补签次数
  remindNotifyPushTimer: NodeJS.Timer         // 签到提醒定时器
  configChangeListener: number                // 配置变化监听

  isShowAwardPopover = false                  // 签到成功后，显示当前获得的奖品
  awardPopoverData: any = {}                  // 当前获得的奖品数据
  showAwardPopoverTimer: NodeJS.Timer         // 控制气泡消失的定时器

  get todayIsAlreadyCheckIn () {
    return this.alreadyCheckInList.includes(this.todayWeek)
  }

  get isCompleteFullWeek () {
    return this.alreadyCheckInList.length === 7
  }

  get checkInButtonText () {
    return this.todayIsAlreadyCheckIn ? '已签到' : '签到'
  }

  @Watch('isShow')
  onIsShowChange () {
    if (!this.isShow) {
      this.resetData()
    }
  }

  @Watch('signedIndex', { immediate: true, deep: true })
  onSignedIndexChange () {
    this.alreadyCheckInList = this.signedIndex
  }

  @Watch('reSignInCount', { immediate: true, deep: true })
  onReSignInCountChange () {
    this.reCheckInCount = this.reSignInCount
  }

  resetData () {
    this.isShowRemindPopover = false
    this.isShowRemindGuide = false
    this.isShowList = false
    this.isShowFinal = false
    this.isShowAwardPopover = false
  }

  async handleCheckIn (weekDay: number, retroactiveData?: any) {
    const type = weekDay === this.todayWeek ? 'normal' : 'retroactive'

    this.$eventTrack('signin_task_signin_panel_click', {
      clickid: type === 'normal' ? 'signin' : 'retroactive',
      day_index: weekDay
    })
    // 签到中或已签到，直接返回
    if (!this.showSignInEntry) return
    if (this.isRequestCheckIn) return
    if (this.alreadyCheckInList.includes(weekDay)) return
    this.isRequestCheckIn = true

    // 签到的时间（补签需要传参）
    let date = Dayjs()
    if (type === 'retroactive') {
      date = Dayjs(retroactiveData.date)
    }
    // 是否翻倍判断：大于开启提醒的时间则翻倍
    let isDouble = false
    if (date.isAfter(Dayjs(this.enableRemindDate), 'day') && this.enableRemind) {
      isDouble = true
    }

    const res = await SignInApi.signIn(type, weekDay, this.gameId, isDouble)
    // 签到失败
    if (!res.data) {
      showMessageToast({ message: '操作失败，请稍后重试', type: 'error' })
      this.isRequestCheckIn = false
      return
    }
    // 当次签到的奖品
    const reward = res.data.get_asset_ids
    // 添加已签到日期
    this.alreadyCheckInList.push(weekDay)
    // 连续签到满 7 天，展示相应的奖品
    if (this.isCompleteFullWeek || reward.includes(40) || reward.includes(41)) {
      this.isShowFinal = true
    }

    // 签到成功后显示奖品的气泡，3s 后消失
    this.awardPopoverData = type === 'normal' ? {
      ...this.dailyAwardList[this.todayWeek - 1],
      isShowDouble: date.isAfter(Dayjs(this.enableRemindDate), 'day')
    } : retroactiveData
    // 连续签到的话，清除原有定时器，用新的覆盖
    clearTimeout(this.showAwardPopoverTimer)
    this.isShowAwardPopover = true
    this.$eventTrack('signin_task_reward_tips_show', { type: this.awardPopoverData.trackType })
    this.showAwardPopoverTimer = setTimeout(() => {
      this.isShowAwardPopover = false
    }, 3000)

    // 补签
    if (type === 'retroactive') {
      const isShow = await getConfigValue(CONFIG_SECTION, CONFIG_KEY_REMIND_GUIDE, false) as boolean

      // 用户完成一次补签后，若该用户未开启签到提醒功能，展示签到提醒引导开启的气泡（终身仅展示一次）
      if (!isShow && !this.enableRemind) {
        this.isShowRemindGuide = true
        this.$eventTrack('signin_task_remind_tips_show')
        setConfigValue(CONFIG_SECTION, CONFIG_KEY_REMIND_GUIDE, true)
      }
      // 消费补签次数
      this.reCheckInCount -= 1
    }

    // 通知云添加刷新数据（次数）
    if (reward.includes(37) || reward.includes(43)) {
      client.broadcastEvent('ThunderPanUrlTaskAdded', [])
    }

    this.isRequestCheckIn = false
    // 同步一下签到记录
    this.$parent.getRecord(this.gameId)
  }

  async handleRemindNotifyPush () {
    // 未开启提醒 或 当天已签到 或 不显示签到入口，不处理
    if (!this.enableRemind || this.todayIsAlreadyCheckIn || !this.showSignInEntry) return
    // 当前时间与最后一次记录时间相同（同一天），则不再提醒
    if (Dayjs().isSame(Dayjs(this.lastRemindDate), 'day')) return

    const message = {
      header: '签到提醒',
      title: '叮叮！今日签到提醒',
      text: '签到可得奖励，连续签到可领超级会员哦！',
      icon: require('@/assets/img/sign-in/card-svip.png'),
      okVisible: true,
      okText: '立即签到',
      priority: MsgPriority.Download,     // 优先级最高
      onClick: () => {
        this.$eventTrack('signin_task_signin_remind_pop_click', { clickid: 'signin' })
        client.callServerFunction('BringMainWndToTop')
        client.callServerFunction('SelectNav', 'download-panel')
        // 展开浮窗，并签到
        this.$parent.todayWeek = Dayjs().day()
        this.$parent.showPopUp = true
        this.handleCheckIn(this.$parent.weekDay)
      },
      beforeClose: () => {
        this.$eventTrack('signin_task_signin_remind_pop_click', { clickid: 'close' })
      }
    }

    const nowDate = +Dayjs()
    this.lastRemindDate = nowDate
    // 触发右下角推送提示
    showPushNotify(message, { duration: 60 * 1000 }).then(() => {
      this.$eventTrack('signin_task_signin_remind_pop_show')
      // 设置下一天的提醒定时
      const nextDayRemindTime = +Dayjs().add(1, 'day').set('hour', this.remindTimeHour).set('minute', this.remindTimeMinute).set('second', 0)
      this.handleRemindNotifyPushTimer(nextDayRemindTime)
    })
    // 记录提醒时间
    setConfigValue(CONFIG_SECTION, CONFIG_KEY_LAST_REMIND, nowDate)
  }

  handleRemindNotifyPushTimer (remindTime?: number) {
    if (!this.enableRemind) return

    if (this.remindNotifyPushTimer) {
      clearTimeout(this.remindNotifyPushTimer)
    }
    // 提醒时间 - 当前时间 + 延迟 1s，执行提醒推送
    remindTime = remindTime ?? +Dayjs().set('hour', this.remindTimeHour).set('minute', this.remindTimeMinute).set('second', 0)
    this.remindNotifyPushTimer = setTimeout(() => {
      this.handleRemindNotifyPush()
    }, remindTime - Date.now() + 1000)
  }

  handleRemindSwitch (enable: boolean) {
    this.$eventTrack('signin_task_signin_panel_click', { clickid: enable ? 'remind_open' : 'remind_close' })
    // 已开启签到提醒，弹窗二次确认弹窗
    if (this.enableRemind) {
      this.$eventTrack('signin_task_remind_off_pop_show')
      this.isShowRemindPopover = true
      return
    }

    setConfigValue(CONFIG_SECTION, CONFIG_KEY_REMIND, enable)
    this.enableRemind = enable
    this.isShowRemindGuide = false
    // 打开【开机自启】的功能
    if (enable) {
      setConfigValue('ConfigNormalSession', 'ConfigNormal_AutoRun', true)
      // 记录开启【签到提醒】的时间，用于确认奖品翻倍的具体时间
      const enableDate = +Dayjs()
      setConfigValue(CONFIG_SECTION, CONFIG_KEY_REMIND_SET_DATE, enableDate)
      this.enableRemindDate = enableDate
    }
    // 定时推送签到通知
    this.handleRemindNotifyPushTimer()
  }

  handleCloseRemind (enableTrack: boolean = false) {
    setConfigValue(CONFIG_SECTION, CONFIG_KEY_REMIND, false)
    // 重置开启【签到提醒】的时间
    setConfigValue(CONFIG_SECTION, CONFIG_KEY_REMIND_SET_DATE, Infinity)
    this.enableRemindDate = Infinity
    this.enableRemind = false
    this.handleHideRemindPopover('off', enableTrack)
  }

  handleShowRules () {
    const url = 'https://i.xunlei.com/policy/pc/sign_on_rule.html'
    client.callServerFunction('OpenNewTab', url)
    this.$eventTrack('signin_task_signin_panel_click', { clickid: 'rule' })
  }

  handleHideRemindPopover (type: string, enableTrack: boolean = true) {
    if (enableTrack && type) {
      this.$eventTrack('signin_task_remind_off_pop_click', { clickid: type })
    }
    this.isShowRemindPopover = false
  }

  handleShowAwardList() {
    this.isShowList = true
    this.$eventTrack('signin_task_signin_panel_click', { clickid: 'reward' })
  }

  handleOpenAwardList () {
    this.isShowFinal = false
    this.handleShowAwardList()
  }

  async created () {
    const isAutoRun = await getConfigValue('ConfigNormalSession', 'ConfigNormal_AutoRun', false)
    // 启动就检查是否开启【开机自启】，如果是关闭的，同步关闭【签到提醒】
    if (!isAutoRun) {
      this.handleCloseRemind()
    } else {
      this.enableRemind = await getConfigValue(CONFIG_SECTION, CONFIG_KEY_REMIND, false) as boolean
    }
    // 获取上一次提醒的时间
    this.lastRemindDate = await getConfigValue(CONFIG_SECTION, CONFIG_KEY_LAST_REMIND, 0) as number
    this.handleRemindNotifyPushTimer()
    // 获取开启提醒的时间
    this.enableRemindDate = await getConfigValue(CONFIG_SECTION, CONFIG_KEY_REMIND_SET_DATE, Infinity) as number
    // 配置将【开机自启】关闭后，同步关闭【签到提醒】
    this.configChangeListener = client.attachServerEvent('OnConfigValueChanged', (ctx, section: string, key: string, preV, newV: boolean) => {
      if (section === 'ConfigNormalSession' && key === 'ConfigNormal_AutoRun' && newV === false) {
        this.handleCloseRemind()
      }
    })
  }

  beforeDestroy () {
    client.detachServerEvent('OnConfigValueChanged', this.configChangeListener)
  }
}
</script>

<style lang="css" scoped>
.xly-sign-in {
  top: 50px;
  right: 20px;
  overflow: inherit;
}
.xly-title-hover__inner {
  white-space: normal;
}
.xly-sign-in__footer .xly-title-hover__inner {
  height: auto;
}
.show-award-popover {
  opacity: 1;
  transform: scale(1);
}
</style>