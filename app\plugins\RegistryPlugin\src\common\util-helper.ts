import * as path from 'path';
import requireNodeFile from './require-node-file';
import { mkdirsAW } from './fs-utilities';
__rootDir = path.resolve(process.execPath, '../resources/app').replace(/\\/g, '/');
const thunderHelper: any = requireNodeFile(path.join(__rootDir, '../bin/ThunderHelper.node'));

export namespace ThunderHelper {
  export enum RegistryHKey {
    HKEY_CLASSES_ROOT = 'HKEY_CLASSES_ROOT',
    HKEY_CURRENT_USER = 'HKEY_CURRENT_USER',
    HKEY_LOCAL_MACHINE = 'HKEY_LOCAL_MACHINE',
    HKEY_USERS = 'HKEY_USERS'
  }

  export function isMutexExist(name: string): boolean {
    let exist: boolean = false;
    if (thunderHelper.isMutexExist) {
      exist = thunderHelper.isMutexExist(name);
    }
    return exist;
  }

  export function shellExecute(hwnd: number, operation: string, filePath: string, params: string, workDir: string, showCmd: string): number {
    return thunderHelper.shellExecute(hwnd, operation, filePath, params, workDir, showCmd);
  }

  export function getFileVersion(filePath: string): string {
    return thunderHelper.getFileVersion(filePath);
  }

  const thunderTempSubPath: string = 'Thunder Network\\Thunder7.9\\';
  export async function getThunderTempPath(bCreate?: boolean, subPath?: string): Promise<string> {
    const os: any = await import('os');
    let tempDir: string = path.join(os.tmpdir(), thunderTempSubPath);
    if (subPath) {
      tempDir = path.join(tempDir, subPath);
    }
    if (bCreate !== undefined && bCreate) {
      await mkdirsAW(tempDir);
    }

    return tempDir;
  }

  export function versionCompare(v1: string, v2: string): number {
    const v1Split: string[] = v1.split('.');
    const v2Split: string[] = v2.split('.');
    let result: number = 0;

    for (let i: number = 0; i < v1Split.length; i++) {
      if (Number(v1Split[i]) - Number(v2Split[i]) > 0) {
        result = 1;
        break;
      } else if (Number(v1Split[i]) - Number(v2Split[i]) < 0) {
        result = -1;
        break;
      }
    }

    return result;
  }

  export function getUrlSearchValue(url: string, key: string): string {
    let value: string = '';
    do {
      if (!url) {
        break;
      }
      const urlObj: URL = new URL(url);
      if (!urlObj || !urlObj.search) {
        break;
      }

      if (!key) {
        return urlObj.search;
      }

      if (!urlObj.searchParams.has(key)) {
        break;
      }
      // 此处获取的值是解码后的值
      value = urlObj.searchParams.get(key);
    } while (0);
    return value;
  }

  export function readRegString(hkey: RegistryHKey, subkey: string, value: string): number | string {
    return thunderHelper.readRegString(hkey, subkey, value);
  }

  export async function isProcessRunning(name: string): Promise<boolean> {
    return new Promise<boolean>((resolve: (value?: boolean | PromiseLike<boolean>) => void): void => {
      thunderHelper.asyncFindProcess(name, (results: any[]) => {
        const run: boolean = results && results.length > 0;
        resolve(run);
      });
    });
  }

  export function getIEProxy(): string {
    return thunderHelper.getIEProxy();
  }
}
