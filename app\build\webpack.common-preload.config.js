'use strict';

const path = require('path');
const webpack = require('webpack');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');

const isBuildProduction = process.env.BUILD_ENV === 'production';
const isDeveloping = process.env.RUN_ENV === 'development';

const packageJSON = require('../package.json');
const binDir = path.resolve(packageJSON.build.binDir);
const binName = packageJSON.build.binName;
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(binDir, buildTarget, '/resources/app/out');

let mainConfig = {
  entry: {
    'common-preload': [path.join(__dirname, '../src/common/preload/preload.ts')]
  },
  module: {
    rules: [
      {
        enforce: 'pre',
        test: /\.js$/,
        loader: 'source-map-loader',
        include: path.resolve(__dirname, '../src')
      },
      {
        test: /\.ts$/,
        loader: 'ts-loader',
        include: path.resolve(__dirname, '../src'),
        exclude: /node_modules/,
        options: {
          transpileOnly: true,
          experimentalWatchApi: true
        }
      }
    ]
  },
  output: {
    filename: '[name].js',
    libraryTarget: 'commonjs2',
    path: outDir
  },
  node: false,
  mode: isBuildProduction ? 'production' : 'development',
  devtool: 'source-map',
  optimization: {
    removeAvailableModules: false,
    removeEmptyChunks: false,
    splitChunks: false,
    ...(isBuildProduction
      ? {
          minimizer: [
            new UglifyJsPlugin({
              sourceMap: buildTarget === 'Release',
              cache: true,
              parallel: true,
              uglifyOptions: {
                ecma: 6,
                compress: {
                  warnings: false
                }
              }
            })
          ]
        }
      : {
          minimize: false
        })
  },
  plugins: [],
  resolve: {
    extensions: ['.js', '.json', '.ts'],
    symlinks: false,
    alias: {
      '@': path.join(__dirname, '../src')
    }
  },
  target: 'electron-renderer',
  stats: 'errors-only',
  bail: true
};

module.exports = mainConfig;
