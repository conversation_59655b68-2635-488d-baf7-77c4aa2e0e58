import { MenuItemConstructorOptions, ContextMenuParams, PopupOptions } from 'electron';
import { asyncRemoteCall } from '@/common/renderer-process-call';
import TinyLogger from '@xunlei/tiny-logger';
import { ThunderUtil } from '@/common/util';

// const merge: any = require('lodash.merge');
const logger: TinyLogger = TinyLogger.getLogger('MenuSkinNS');

export namespace MenuSkinNS {
  // 在标签的contextmenu事件中调用该方法弹出默认的右键菜单
  export async function popEditableDefaultContextMenu(
    extraOptionsFunc?: (params: ContextMenuParams) => MenuItemConstructorOptions[],
    pos?: number,
    options?: PopupOptions
  ): Promise<void> {
    const webContents: any = await asyncRemoteCall.getCurrentWebContents();
    webContents.once('context-menu', async (event: Event, params: ContextMenuParams) => {
      logger.verbose(event);
      if (params.isEditable) {
        const inputMenuOptions: MenuItemConstructorOptions[] = [
          {
            label: '撤销', enabled: params.editFlags.canUndo, click: (): void => {
              webContents.undo();
            }
          },
          { type: 'separator' },
          {
            label: '剪切', enabled: params.editFlags.canCut, click: (): void => {
              webContents.cut();
            }
          },
          {
            label: '复制', enabled: params.editFlags.canCopy, click: (): void => {
              webContents.copy();
            }
          },
          {
            label: '粘贴',
            enabled: params.editFlags.canPaste && ThunderUtil.isClipboardTextFormatAvailable(),
            click: (): void => {
              webContents.paste();
            }
          },
          {
            label: '删除', enabled: params.editFlags.canDelete, click: (): void => {
              webContents.delete();
            }
          },
          { type: 'separator' },
          {
            label: '全选', enabled: params.editFlags.canSelectAll, click: (): void => {
              webContents.selectAll();
            }
          }
        ];

        if (extraOptionsFunc !== undefined && typeof extraOptionsFunc === 'function') {
          const extraOptions: MenuItemConstructorOptions[] = extraOptionsFunc(params);
          if (extraOptions !== undefined && extraOptions.length > 0) {
            if (pos === undefined) {
              pos = inputMenuOptions.length;
            } else {
              if (pos < 0) {
                pos = inputMenuOptions.length + 1 + pos;
                if (pos < 0) {
                  pos = 0;
                }
              }
              if (pos > inputMenuOptions.length) {
                pos = inputMenuOptions.length;
              }
            }

            inputMenuOptions.splice(pos, 0, ...extraOptions);
          }
        }

        const menuObj: any = await asyncRemoteCall.getMenu();
        const inputMenu: any = await menuObj.buildFromTemplate(inputMenuOptions);
        // await setStyle(inputMenu, {});
        const wnd: any = await asyncRemoteCall.getCurrentWindow();
        await inputMenu.popup({ window: wnd });
      }
    });
  }
}
