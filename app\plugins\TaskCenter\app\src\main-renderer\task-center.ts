// @ts-nocheck
import TinyLogger from '@xunlei/tiny-logger';
import Vue, { VNode, CreateElement } from 'vue';

// 组件不能全部导入，需要按需引入
import Button from '@xunlei/thunder-ui-vue/lib/button'
import Switch from '@xunlei/thunder-ui-vue/lib/switch'
import Media from '@xunlei/thunder-ui-vue/lib/media'
import Dialog from '@xunlei/thunder-ui-vue/lib/dialog'

const logger: TinyLogger = TinyLogger.getLogger('task-center-entry');

import App from './index.vue';
import { eventTrack } from '@/common/event-track';

// 事件总线
Vue.prototype.$bus = new Vue();
Vue.prototype.$eventTrack = eventTrack;
// 组件
Vue.use(Button)
Vue.use(Switch)
Vue.use(Media)
Vue.use(Dialog)

async function getServerDatas(): Promise<any> {
  // todo 一些前置数据获取
  return {};
}

async function doMount(): Promise<void> {
  logger.information('enter');
  do {
    // 主程序里的插槽
    const slot = document.getElementById('sign-in-button');
    if (!slot) {
      logger.information('get element failed');
      break;
    }

    // todo 如果有灰度开关，读取开关 break
    // 注入样式
    const head: HTMLHeadElement = (document.getElementsByTagName('head'))?.[0];
    if (head) {
      const link = document.createElement('link');
      link.href = `${__dirname}/index.css`;
      link.rel = 'stylesheet';
      link.type = 'text/css';
      link.onload = async () => {
        const options = await getServerDatas();

        // 挂载组件
        new Vue({
          components: {
            App
          },
          data: {
            ...options
          },
          render(createElement: CreateElement): VNode {
            return createElement('app');
          }
        }).$mount('#sign-in-button');
      };
      head.appendChild(link);
    } else {
      logger.warning('append css failed');
    }
  } while (0);
}

doMount().catch();