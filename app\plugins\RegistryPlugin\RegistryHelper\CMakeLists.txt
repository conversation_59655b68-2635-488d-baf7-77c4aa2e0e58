cmake_minimum_required(VERSION 3.15)

#cmake 策略
cmake_policy(SET CMP0091 NEW)
cmake_policy(SET CMP0042 NEW)

# 设置c++版本
set(CMAKE_CXX_STANDARD 20)

#设置自己的插件名称
project(RegistryHelper)

#node api 使用版本
add_definitions(-DNAPI_VERSION=4)

#cmake.js 头文件路径
include_directories(${CMAKE_JS_INC})

#源码头文件包含路径
include_directories(SYSTEM "${CMAKE_CURRENT_SOURCE_DIR}/src")

#源码路径
file(GLOB_RECURSE ALL_SOURCE "${CMAKE_CURRENT_SOURCE_DIR}/src/*.*")

file(GLOB_RECURSE REGISTRY_HELPER_RC "${CMAKE_CURRENT_SOURCE_DIR}/src/*.rc")

#目录结构分组
foreach(fileItem ${ALL_SOURCE})       
    # Get the directory of the source file
    get_filename_component(PARENT_DIR "${fileItem}" DIRECTORY)
    # Remove common directory prefix to make the group
    string(REPLACE "${CMAKE_CURRENT_SOURCE_DIR}/src" "" GROUP "${PARENT_DIR}")
    # Make sure we are using windows slashes
    string(REPLACE "/" "\\" GROUP "${GROUP}")
    # Group into "Source Files" and "Header Files"
    set(GROUP "${GROUP}")
    source_group("src\\${GROUP}" FILES "${fileItem}")
endforeach()

include_directories(SYSTEM "${CMAKE_SOURCE_DIR}/../bugreport/inc")
include_directories(SYSTEM "${CMAKE_SOURCE_DIR}/../3rd/inc")
link_directories("${CMAKE_SOURCE_DIR}/../3rd/lib/$<$<CONFIG:Debug>:debug>$<$<CONFIG:Release>:release>")
add_library(${PROJECT_NAME} SHARED ${ALL_SOURCE} ${REGISTRY_HELPER_RC} ${CMAKE_JS_SRC})
set_target_properties(${PROJECT_NAME} PROPERTIES PREFIX "" SUFFIX ".node")
target_link_libraries(${PROJECT_NAME} ${CMAKE_JS_LIB}
        Shlwapi.lib
        )

add_definitions(
            -DUNICODE
            -D_UNICODE
            -DETW_LOGGER
            -DWIN32_LEAN_AND_MEAN # 解决windows.h和winsock2.h的冲突问题
            )

add_custom_command(TARGET RegistryHelper POST_BUILD
        COMMAND "${CMAKE_COMMAND}" -E copy
        "${CMAKE_SOURCE_DIR}/build/$<$<CONFIG:Debug>:debug>$<$<CONFIG:Release>:release>/RegistryHelper.node"
        "${CMAKE_SOURCE_DIR}/../../bin/$<$<CONFIG:Debug>:debug>$<$<CONFIG:Release>:release>/resources/bin/RegistryHelper.node"
        VERBATIM)

if(MSVC AND CMAKE_JS_NODELIB_DEF AND CMAKE_JS_NODELIB_TARGET)
  # Generate node.lib
  #execute_process(COMMAND ${CMAKE_AR} /def:${CMAKE_JS_NODELIB_DEF} /out:${CMAKE_JS_NODELIB_TARGET} ${CMAKE_STATIC_LINKER_FLAGS})
  
  # 依赖包目录包含，暂时不需要
  # execute_process(COMMAND node -p "require('node-addon-api').include"
  #      WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
  #      OUTPUT_VARIABLE NODE_ADDON_API_DIR
  #     )
  #	 string(REPLACE "\n" "" NODE_ADDON_API_DIR ${NODE_ADDON_API_DIR})
  #	 string(REPLACE "\"" "" NODE_ADDON_API_DIR ${NODE_ADDON_API_DIR})
  #	 target_include_directories(${PROJECT_NAME} PRIVATE ${NODE_ADDON_API_DIR})
  target_link_libraries(${PROJECT_NAME} ${CMAKE_JS_LIB})

  
endif()
