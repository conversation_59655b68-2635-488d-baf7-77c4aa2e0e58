import path = require('path');
import requireNodeFile from './common/require-node-file';
import { copyFileAW, existsAW } from './common/fs-utilities';
const rootDir = path.resolve(process.execPath, '../resources/app').replace(/\\/g, '/');
const thunderHelper: any = requireNodeFile(path.join(rootDir, '../bin/ThunderHelper.node'));

function log(...messages: any[]): void {
  if (process.env.NODE_ENV !== 'production')  {
    console.log('[hotfix-none]', ...messages);
  }
}

function versionCompare(v1: string, v2: string): number {
  const v1Split: string[] = v1.split('.');
  const v2Split: string[] = v2.split('.');
  let result: number = 0;

  for (let i: number = 0; i < v1Split.length; i++) {
    if (Number(v1Split[i]) - Number(v2Split[i]) > 0) {
      result = 1;
      break;
    } else if (Number(v1Split[i]) - Number(v2Split[i]) < 0) {
      result = -1;
      break;
    }
  }

  return result;
}

async function run(): Promise<void> {
  let dir: string = __dirname;
  if (dir.endsWith('.asar')) {
    dir += '.unpacked';
  }
  const birDir: string = dir;

  do {
    const src = path.join(birDir, 'bin/xl_band_evaluate.dll');
    const sdkPath: string = path.normalize(path.join(process.execPath, '../resources/bin/SDK/xl_band_evaluate.dll'));
    if (!(await existsAW(src))) {
      log(src, 'not exist');
      break;
    }
    if (await existsAW(sdkPath)) {
      const srcVersion = thunderHelper.getFileVersion(src) || '0.0.0.0';
      const dstVersion = thunderHelper.getFileVersion(sdkPath) || '0.0.0.0';
      const versionComp = versionCompare(srcVersion, dstVersion);
      if (versionComp <= 0) {
        log('version compare', versionComp);
        break;
      }
    }
    await copyFileAW(src, sdkPath);
  } while (0);

  if (global.__xdasIPCClienInstance?.client?.isInprocess()) {
    log('plugin is running in main-renderer');
  } else {
    setTimeout(() => {
      process.exit();
    });
  }
}

run().catch();
