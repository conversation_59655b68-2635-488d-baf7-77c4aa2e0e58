<template>
  <div class="xly-favorites-dynamic__header">
    <h3>
      {{ headerTitle }}
      <i v-if="false" class="xly-icon-help" title="动态来源于发生了信息变动的收藏网址"></i>
      <div class="xly-favorites-dynamic__help">
        <i class="xly-icon-help" @click.stop="handleShowHelp"></i>
        <div class="xly-favorites-dynamic__drop" v-show="showHelp" @click.stop>
          <h4>动态智能推荐</h4>
          <div class="td-switch" 
            :class="{'is-checeked': isChecked}"
            @click.stop="handleSwitch"
          >
            <div class="td-switch__inner"></div>
            <span class="td-switch__text"></span>
          </div>
          <p>
            {{ helpTitle }}
          </p>
        </div>
      </div>
    </h3>

    <slot></slot>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
@Component({
  components: {
  },
  inheritAttrs: false
})
export default class FavoritesDynamicHeader extends Vue {

  helpTitle: string = '更新动态来源于发生了信息变动的收藏网址。开启动态智能推荐后，会根据您的历史浏览和使用习惯，对您收藏的站点做智能排序和推荐。';
  headerTitle: string = '收藏网站更新';

  isChecked: boolean = true;
  showHelp: boolean = false;
  handleDocClick: () => void = undefined;

  handleShowHelp(): void {
    clientModule.callServerFunction('TrackEvent', 'core_event', 'start_page_bookurl_dynamic_content_click', '', 0, 0, 0, 0, 'click_id=question_mark').catch();
    this.showHelp = !this.showHelp;
  }

  async handleSwitch(): Promise<void> {
    this.isChecked = !this.isChecked;
    clientModule.callServerFunction('TrackEvent', 'core_event', 'start_page_bookurl_dynamic_content_click', '', 0, 0, 0, 0, 'click_id=smart_recommendation_switch,is_smart_recommendation=' + (this.isChecked ? '1' : '0')).catch();
    await clientModule.callServerFunction('SetConfigValue', 'ConfigFavorites', 'SmartRecommend', this.isChecked);
  }

  async mounted(): Promise<void> {
    this.handleDocClick = ((): void => {
      this.showHelp = false;
    }).bind(this);
    document.addEventListener('click', this.handleDocClick);

    this.isChecked = await clientModule.callServerFunction('GetConfigValue', 'ConfigFavorites', 'SmartRecommend') ?? true;
  }

  destroyed(): void {
    if (this.handleDocClick) {
      document.removeEventListener('click', this.handleDocClick);
      this.handleDocClick = null;
    }
  }
}
</script>