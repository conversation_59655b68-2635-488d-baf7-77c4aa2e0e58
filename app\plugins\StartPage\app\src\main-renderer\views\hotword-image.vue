<template>
  <div class="xly-favorites-recommend"  v-show="settingInited && hotwordShow && recData.length > 0">
    <div class="xly-favorites-common__header">
      <h2>搜索发现</h2>
    </div>

    <div class="td-tabs">
      <div class="td-tabs__title">
        <div class="td-tabs__nav">
          <div
            v-for="(item, index) in recData"
            class="td-tabs__item"
            :class="{ 'is-active': index === currentNumber }"
            :key="index"
            @click="currentNumber = index"
            >
            <div>{{ item.title }}</div>
          </div>
        </div>
      </div>

      <div class="td-tabs__content">
        <div
          v-show="index === currentNumber" class="td-tabs__pane"
          v-for="(item, index) in recData"
          :key="index"
        >
          <div class="xly-favorites-module">
            <ul>
              <li
                class="xly-favorites-module__item"
                v-for="(item, index) in recData[currentNumber].data"
                :key="index"
              >
                <div class="xly-favorites-module__main" @click="onItemClick(recData[currentNumber].index, item, item.index, 'image')">
                  <!-- 主图 -->
                  <div class="xly-favorites-module__image">
                    <img :src="item.imageUrl" alt="" />
                  </div>
                  <!-- 左上的标签 -->
                  <div class="xly-favorites-module__label">
                    <img src="item.tag" alt="" />
                  </div>
                  <!-- 下边的信息 -->
                  <div class="xly-favorites-module__info">
                    <p class="xly-favorites-module__score"></p>
                    <div class="xly-favorites-module__number">
                      <span>{{ item.cta }}</span>
                    </div>
                  </div>
                </div>
                <div class="xly-favorites-module__title" @click="onItemClick(recData[currentNumber].index, item, item.index, 'title')">
                  {{ item.title }}
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import TinyLogger from '@xunlei/tiny-logger';
import { Vue, Component, Prop } from 'vue-property-decorator';
import { IAssets, ISlotItem, IAssetsType } from '@/main-renderer/common/data-define';

const assetsLength: number = 3;
const logger: TinyLogger = TinyLogger.getLogger('start-page-hotword-image');

@Component({
  components: {
  }
})
export default class HotwordImage extends Vue {
  @Prop({})
  options: ISlotItem[];

  @Prop({})
  settingInited: boolean;

  @Prop()
  visible: boolean;

  @Prop()
  hotwordShow: boolean;
  
  @Prop()
  showedIds: string[];

  get mainTitle(): string {
    return this?.recData[this.currentNumber]?.title ?? '';
  }

  get recData(): {
    title: string;
    index: number;
    data: (IAssets & { index: number })[]
  }[] {
    const data: { title: string; index: number; data: (IAssets & { index: number })[] }[] = [];
    for (let index: number = 0; index < this.options.length; index++) {
      const slot: ISlotItem = this.options[index];
      if (!slot.getSucceed()) {
        continue;
      }

      const mainAssets: IAssets = slot.getAssets();
      if (!mainAssets?.title) {
        continue;
      }
      if (slot.getSucceed() && slot.getSubSlots()?.length > 0) {
        const subSlots: ISlotItem[] = slot.getSubSlots();
        const assets: (IAssets & { index: number })[] = [];
        for (let i: number = 0; i < subSlots.length; i++) {
          const subSlot: ISlotItem = subSlots[i];
          if (subSlot.getSucceed()) {
            const subAssets: IAssets = subSlot.getAssets();
            if (subAssets.imageUrl && subAssets.title) {
              assets.push({ ...subAssets, index: i });

              if (assets.length >= assetsLength) {
                // 最多取N组有效数据
                break;
              }
            }
          }
        }
        if (assets.length === assetsLength) {
          data.push({
            title: mainAssets.title,
            index,
            data: assets,
          });
        }
      }
    }
    return data;
  }

  reportShow(): void {
    if (this.showedIds.length === assetsLength) {
      logger.information('all hotword slots showed', this.showedIds);
      return;
    }

    const optionIndex: number = this.recData[this.currentNumber].index;
    const slot: ISlotItem = this.options[optionIndex];
    slot.show();
    logger.information('report image hotword showed', slot.id());
    this.$emit('slot-showed', slot.id());
  }

  currentNumber: number = 0;

  mounted(): void {
    this.$watch(() => {
      const { currentNumber, recData } = this;
      return { currentNumber, recData };
    }, () => {
      if (this.visible && this.hotwordShow && this.recData.length > 0) {
        this.reportShow();
      }
    }, { immediate: true });
  }

  handleNext(): void {
    this.currentNumber =
      this.currentNumber + 1 === this.recData.length
        ? 0
        : (this.currentNumber += 1);
  }

  handlePrev(): void {
    this.currentNumber =
      this.currentNumber === 0
        ? this.recData.length - 1
        : (this.currentNumber -= 1);
  }

  onItemClick(optionIndex: number, item: IAssets, slotIndex: number, type: IAssetsType): void {
    logger.information('image hotword click', optionIndex, slotIndex);
    this.options?.[optionIndex].getSubSlots()?.[slotIndex]?.click(type);
  }
}
</script>
