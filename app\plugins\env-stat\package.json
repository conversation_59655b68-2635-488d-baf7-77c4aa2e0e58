{"name": "env-stat", "version": "1.0.1", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "main": "index.js", "scripts": {"release": "cross-env BIN_TARGET=Release webpack", "product-release": "cross-env BUILD_ENV=production BIN_TARGET=ProductRelease webpack", "start-release": "node ./bin/Release/app/index.js", "start-product-release": "node ./bin/ProductRelease/app/index.js"}, "license": "", "dependencies": {"ps-node": "^0.1.6"}, "devDependencies": {"@types/electron": "^1.6.10", "cross-env": "^5.2.0", "node-loader": "^0.6.0", "source-map-loader": "^0.2.4", "ts-loader": "^4.4.2", "typescript": "^2.9.2", "uglifyjs-webpack-plugin": "^1.3.0", "webpack": "^4.16.5", "webpack-cli": "^3.1.2", "webpack-dev-server": "^3.1.5"}}