'use strict';

const path = require('path');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const packageJSON = require('./package.json');
// const webpack = require('webpack');

const binDir = path.resolve(__dirname, './bin/');
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(binDir, buildTarget, `/${packageJSON.name}`);

const isBuildProduction = process.env.BUILD_ENV === 'production';

module.exports = {
  // for webpack v4
  mode: isBuildProduction ? 'production' : 'development',
  target: 'electron-main',
  entry: './src/main/index.ts',
  output: {
    filename: 'index.js',
    libraryTarget: 'commonjs2',
    path: outDir
  },
  module: {
    rules: [{
        enforce: 'pre',
        test: /\.js$/,
        loader: 'source-map-loader'
      },
      {
        test: /\.ts$/,
        loader: 'ts-loader',
        exclude: /node_modules/
      }
    ]
  },
  node: false,
  devtool: isBuildProduction ? 'source-map' : 'cheap-module-eval-source-map',
  resolve: {
    extensions: ['.ts', '.js'],
    alias: {
      '@': path.join(__dirname, 'src')
    }
  },
  optimization: {
    removeAvailableModules: false,
    removeEmptyChunks: false,
    ...(isBuildProduction ? {
      minimizer: [
        new UglifyJsPlugin({
          sourceMap: true,
          cache: true,
          parallel: true,
          uglifyOptions: {
            ecma: 6,
            compress: {
              warnings: false
            }
          }
        })
      ]
    } : {
      minimize: false
    })
  }
};