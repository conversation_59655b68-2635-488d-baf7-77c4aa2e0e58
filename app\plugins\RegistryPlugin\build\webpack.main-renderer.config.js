'use strict';
const path = require('path');
const packageJSON = require('../package.json');
const merge = require('webpack-merge');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const buildTarget = process.env.BIN_TARGET || 'Debug';
const rendererConfig = require('./get-common-renderer-config')('main-renderer');

module.exports = merge(rendererConfig, {
  // 其他配置可以写在这里
  plugins: [
    // 使用 CopyPlugin 将 config.json 同步到构建产物中，并替换版本号信息。
    new CopyWebpackPlugin(
      [{
        from: path.resolve(__dirname, 'config.json'),
        to: path.join(path.resolve(packageJSON.build.outDir), buildTarget, 'TaskCenter'),
        transform(content, path) {
          console.log(content);
          const conf = JSON.parse(content.toString());
          conf.version = packageJSON.version;
          conf.main = `${packageJSON.version}.asar/index.js`;
          return Buffer.from(JSON.stringify(conf), 'utf8');
        }
      }]
    )
  ]
});
