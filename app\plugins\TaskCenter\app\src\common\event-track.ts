import { trackEvent } from "./ipc";

export type Dictionary<T = any> = Record<string | number, T>;

export function eventTrack (attr: string, extData: Dictionary = {}, eventKey = 'core_event'): Promise<number> {
  if (process.env.NODE_ENV !== 'production') {
    console.warn(
      `\neventKey: ${eventKey}, \nattr: ${attr}, \nextData: `, extData
    )
  }
  return trackEvent(
    eventKey,
    attr,
    serialize(extData)
  )
}

function serialize (extData: Dictionary<string>): string {
  return Object.keys(extData)
    .reduce((arr: string[], curKey: string) => {
      return arr.concat([`${curKey}=${extData[curKey]}`])
    }, [])
    .join(',')
}
