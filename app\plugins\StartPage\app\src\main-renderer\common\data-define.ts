/**
 * @description: 启动页数据格式定义
 */

export interface ISearchClassifyOptions {
  placeholder: string;
  text: string;
  url: string;
}

export interface IRecommendSiteOptions {
  iconUrl: string;
  text: string;
  url: string;
}

export interface ISiteItemOptions {
  name?: string;
  url: string;
  iconUrl?: string;
  date?: number; // 最后一次访问时间
  visit?: number; // 访问次数
  default?: boolean; // 用来指定是否默认(xunlei或baidu)
  iconLoaded?: boolean; // 图标是否已加载
}

export interface ISearchNavs {
  notshow_days: number;
  news_show: boolean;
  nocollect_default?: {
    text: string;
    url: string;
  }
}

export interface ISlotConfigOptions {
  image: string;
  badge: string;
  button: string;
  title: string;
  url: string;
}

export interface ISlotConfig {
  class: string;
  options: ISlotConfigOptions[];
}

export enum IAssetsType {
  title = 'title',
  image = 'image',
  subicon = 'subicon',
  icon = 'icon'
}

export interface IAssets {
  title?: string;
  imageUrl?: string;
  iconUrl?: string;
  linkUrl?: string; // title, imageUrl, iconUrl 使用这个跳转链接
  subIconUrl?: string;
  subIconLinkUrl?: string; // subIconUrl 使用这个跳转链接
  text?: string; // 配置文本
  cta?: string; // 按钮文字
}

export enum ITrackFollowServerType {
  show = 'show',
  click = 'click',
  close = 'close'
}

export interface ISlotItem {
  id(): string;
  getSucceed(): boolean; // 请求是否成功
  getAssets(): IAssets; // 获取材质
  getParams(): any; // 返回的扩展参数
  getLandingType(): string; // 返回LandingType, 'LANDING_OPEN_IN_WEBVIEW' | 'LANDING_CALL_APP' | 'LANDING_OPEN_IN_BROWSER'
  trackShowEvent(): void; // 上报展现
  trackClickEvent(assetsType?: IAssetsType): void; // 上报点击
  trackCloseEvent(): void; // 上报关闭
  trackFollowServer(type: ITrackFollowServerType): void; // 向流量系统上报
  show(): void;  // 展现 (上报trackFollowServer与trackShowEvent)
  click(assetsType: IAssetsType): void; // 点击, 打开网页: 当landingType为app, 将打开游戏 (自动上报trackFollowServer与trackClickEvent)
  close(): void; // 关闭 (上报trackFollowServer与trackCloseEvent)
  getSubSlots(): ISlotItem[]; // 多组数据获取
}
