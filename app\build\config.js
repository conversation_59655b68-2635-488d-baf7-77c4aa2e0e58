module.exports = [
  {
    name: 'main',
    type: 'main'
  },
  {
    name: 'common-preload',
    type: 'preload'
  },
  {
    name: 'plugin-preload',
    type: 'preload'
  },
  {
    name: 'plugin-boot',
    type: 'preload'
  },
  {
    name: 'main-renderer',
    type: 'renderer'
  },
  {
    name: 'suspension-renderer',
    type: 'renderer'
  },
  {
    name: 'personal-info-renderer',
    type: 'renderer'
  },
  {
    name: 'message-box-renderer',
    type: 'renderer'
  },
  {
    name: 'secondary-renderer',
    type: 'renderer'
  },
  {
    name: 'notification-renderer',
    type: 'renderer'
  },
  {
    name: 'retry-login-renderer',
    type: 'renderer'
  },
  {
    name: 'modifier-userinfo-renderer',
    type: 'renderer'
  },
  {
    name: 'dropdown-file-renderer',
    type: 'renderer'
  },
  {
    name: 'suspension-xdas-renderer',
    type: 'renderer'
  },
  {
    name: 'web-view-renderer',
    type: 'renderer'
  },
  {
    name: 'upload-oss',
    type: 'pluginentry'
  }, {
    name: 'post-upload-oss',
    type: 'pluginentry'
  }, {
    name: 'skeleton',
    type: 'pluginentry'
  }, {
    name: 'captcha-dlg-renderer',
    type: 'renderer'
  }, {
    name: 'modeless-renderer',
    type: 'renderer'
  }, {
    name: 'embed-webview-preload',
    type: 'preload'
  }, {
    name: 'embeded-webview-renderer',
    type: 'renderer'
  }, {
    name: 'main-renderer-webview-preload',
    type: 'preload'
  }, {
    name: 'tbc-webview-box-preload',
    type: 'preload'
  }, {
    name: 'browser-preload',
    type: 'preload'
  }
];
