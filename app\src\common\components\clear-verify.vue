<template>
  <div class="xly-pop-up" v-show="showPopup">
    <h3>{{ confirmTitle }}</h3>
      <div class="xly-pop-up__footer">
        <td-button ref="yes">{{ okText }}</td-button>
        <td-button secondary ref="cancel">取消</td-button>
      </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component
export default class ClearConfirmCtrl extends Vue {
  showPopup: boolean = false;
  confirmTitle: string = '';
  okText: string = '清空';

  async clearConfirm(title: string, okText?: string): Promise<boolean> {
    this.confirmTitle = title ?? '';
    if (okText) {
      this.okText = okText;
    }
    this.showPopup = true;
    await this.$nextTick();

    const yes: boolean = await new Promise<boolean>(
      (resolve: (val: boolean) => void): void => {
        (this.$refs['yes'] as Vue).$el.addEventListener('click', () => {
          resolve(true);
        }, { once : true });
        (this.$refs['cancel'] as Vue).$el.addEventListener('click', () => {
          resolve(false);
        }, { once: true });
      }
    );

    (this.$parent?.$el as HTMLElement).focus();
    this.showPopup = false;
    return yes;
  }
}
</script>