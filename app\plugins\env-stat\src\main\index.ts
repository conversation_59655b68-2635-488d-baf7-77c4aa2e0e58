import requireNodeFile from '@/main/common/require-node-file';
const path = require('path');

__rootDir = path.resolve(process.execPath, '../resources/app').replace(/\\/g, '/');
let thunderHelper: any = requireNodeFile(path.join(__rootDir, '../bin/ThunderHelper.node'));

let processMap: Map<string, boolean> = new Map();
let ssdList: number[] = [];
interface IMemoryInfo {
  totalPhy: number;
  totalVir: number;
}
let memoryInfo: IMemoryInfo = { totalPhy: 0, totalVir: 0 };
let hardDiskSpaceList: number[] = [];
let cpuList: string[] = [];

async function getAppList(): Promise<string[]> {
  return new Promise<string[]>((resolve: (value?: string[] | PromiseLike<string[]>) => void): void => {
    thunderHelper.getAppList((processList: string[]) => {
      resolve(processList);
    });
  });
}

async function isSSD(): Promise<number[]> {
  return new Promise<number[]>((resolve: (value?: number[] | PromiseLike<number[]>) => void): void => {
    thunderHelper.isSSD((ret: boolean, ssdList: number[]) => {
      resolve(ssdList);
    });
  });
}

async function getMemoryInfo(): Promise<IMemoryInfo> {
  return new Promise<IMemoryInfo>((resolve: (value?: IMemoryInfo | PromiseLike<IMemoryInfo>) => void): void => {
    thunderHelper.getMemoryInfo((totalPhy: number, totalVir: number) => {
      resolve({ totalPhy, totalVir });
    });
  });
}

async function getHardDiskSpaceList(): Promise<number[]> {
  return new Promise<number[]>((resolve: (value?: number[] | PromiseLike<number[]>) => void): void => {
    thunderHelper.getHardDiskSpaceList((hardDiskSpaceList: number[]) => {
      resolve(hardDiskSpaceList);
    });
  });
}

async function getCpuList(): Promise<string[]> {
  return new Promise<string[]>((resolve: (value?: string[] | PromiseLike<string[]>) => void): void => {
    thunderHelper.getCpuList((cpuList: string[]) => {
      resolve(cpuList);
    });
  });
}

async function updateAppList(): Promise<void> {
  let processList: string[] = await getAppList();
  if (processList) {
    for (let i: number = 0; i < processList.length; ++i) {
      let pathParse: any = path.parse(processList[i]);
      if (pathParse && pathParse.base && pathParse.base !== '' && pathParse.base.toLowerCase() !== 'thunder.exe') {
        processMap.set(pathParse.base, true);
      }
    }
  }
}

async function startMonitorSoftware(): Promise<void> {
  await updateAppList();
  setTimeout(() => {
    startMonitorSoftware().catch();
  }, 60 * 1000);
}

async function collectHardEnvInfo(): Promise<void> {
  [ssdList, memoryInfo, hardDiskSpaceList, cpuList] = await Promise.all([
    isSSD(),
    getMemoryInfo(),
    getHardDiskSpaceList(),
    getCpuList()
  ]);
  let extData: string = `memory=`;
  hardDiskSpaceList.forEach((space: number) => {
    extData += `${space}|`;
  });
  let ssdStat: string = 'disk_type=';
  const mediaType: { [type: number]: string } = {
    0: 'Unspecified',
    3: 'HDD',
    4: 'SSD',
    5: 'SCM'
  };
  ssdList.forEach((ssd: number) => {
    let type: string = mediaType[ssd];
    if (type) {
      ssdStat += `${type}|`;
    } else {
      ssdStat += `${ssd}|`;
    }
  });
  extData += `,${ssdStat}`;
  extData += `,ram=${memoryInfo.totalPhy},vitrual_ram=${memoryInfo.totalVir}`;
  extData += ',cpu=';
  cpuList.forEach((cpu: string) => {
    extData += `${encodeURIComponent(cpu)}|`;
  });
  thunderHelper.trackEvent('plugin_stat', 'device_stat', '', 0, 0, 0, 0, extData, 0);
}

async function main(): Promise<void> {
  startMonitorSoftware().catch();

  collectHardEnvInfo().catch();

  // 退出前发统计
  window.addEventListener('beforeunload', () => {
    let contentList: string = '';
    processMap.forEach((value, name): void => {
      contentList += 'name=' + encodeURIComponent(name) + ';';
    });
    if (contentList.length > 0) {
      thunderHelper.trackEvent('plugin_stat', 'pc_application', '', 0, 0, 0, 0, `contentlist=${contentList}`, 0);
    }
  });
}

main().catch();
