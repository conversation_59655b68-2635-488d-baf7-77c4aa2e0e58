import { MenuItemConstructorOptions } from 'electron';
import TinyLogger from '@xunlei/tiny-logger';
const logger: TinyLogger = TinyLogger.getLogger('ConTextMenu');

export namespace ContextMenuNS {
  async function popSingleCopyMenu(params: Electron.Params): Promise<void> {
    const menuItems: MenuItemConstructorOptions[] = [{ label: '复制', role: 'copy', enabled: params.editFlags.canCopy }];
    const { asyncRemoteCall } = await import('@/common/renderer-process-call'); // tslint:disable-line
    const menuObj: any = await asyncRemoteCall.getMenu();
    const menu: any = await menuObj.buildFromTemplate(menuItems);
    const wnd: any = await asyncRemoteCall.getCurrentWindow();
    await menu.popup({ window: wnd });
  }

  async function popDefaultEditMenu(params: Electron.Params): Promise<void> {
    const menuItems: MenuItemConstructorOptions[] = [
      { label: '撤销', role: 'undo', enabled: params.editFlags.canUndo },
      { type: 'separator' },
      { label: '剪切', role: 'cut', enabled: params.editFlags.canCut },
      { label: '复制', role: 'copy', enabled: params.editFlags.canCopy },
      { label: '粘贴', role: 'paste', enabled: params.editFlags.canPaste },
      { label: '删除', role: 'delete', enabled: params.editFlags.canDelete },
      { type: 'separator' },
      { label: '全选', role: 'selectAll', enabled: params.editFlags.canSelectAll }
    ];
    const { asyncRemoteCall } = await import('@/common/renderer-process-call'); // tslint:disable-line
    const menuObj: any = await asyncRemoteCall.getMenu();
    const menu: any = await menuObj.buildFromTemplate(menuItems);
    const wnd: any = await asyncRemoteCall.getCurrentWindow();
    await menu.popup({ window: wnd });
  }

  export function popContextMenu(event: Electron.ContextMenuEvent): void {
    if (!event?.params) {
      return;
    }
    const params = event.params;
    logger.information('params', params);
    if (params.isEditable) {
      popDefaultEditMenu(params).catch();
    } else if (params.selectionText !== '') {
      popSingleCopyMenu(params).catch();
    }
  }

  // 從當前鼠標位置彈出菜單，防止窗口卡頓，菜單顯示位置不對的問題
  export function popupAt(contextMenu: any, param: { window: any; x?: number; y?: number }, buttonEl: Element): void {
    do {
      const contentRect: ClientRect = buttonEl.getBoundingClientRect();
      param.x = param.x !== undefined ? param.x : Math.round(contentRect.left);
      param.y = param.y !== undefined ? param.y : Math.round(contentRect.bottom);
      // let point: Point = screen.getCursorScreenPoint();
      // let bounds: Rectangle = await param.window.getContentBounds();
      // let origin: Point = { x: bounds.x, y: bounds.y };
      // param.x = point.x - origin.x;
      // param.y = point.y - origin.y;
    } while (0);
    contextMenu.popup(param);
  }
}
