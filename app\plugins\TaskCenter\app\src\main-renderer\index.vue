<template>
  <div class="none-dragable">
    <div
      v-show="showSignInEntry"
      ref="entryBtn"
      class="xly-header-sign-in"
      :title="iconTitle"
      :class="{ 'is-animation': showAnimate }"
      @click.stop="handleCheckInPopUpShow()"
    >
      <div class="xly-icon-sign-in"></div>
      <div class="xly-img-sign-in"></div>
    </div>

    <SignInPopUp
      v-show="showPopUp"
      ref="signInPopUp"
      tabindex="0"
      :isShow="showPopUp"
      :gameId="gameId"
      :todayWeek="weekDay"
      :signedIndex="signedIndex"
      :reSignedIndex="reSignedIndex"
      :reSignInCount="reSignInCount"
      :remindTimeHour="remindTimeHour"
      :remindTimeMinute="remindTimeMinute"
      :dailyAwardList="dailyAwardList"
      :finalAwardList="finalAwardList"
      :showSignInEntry="showSignInEntry"
    />

    <SignInGuide
      v-if="showGuideDialog"
      @close="handleGuideClose"
      @checkIn="handleGuideCheckIn"
    />
  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch } from 'vue-property-decorator'
import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { setConfigValue, getConfigValue, getUserInfo, showMessageToast } from '@/common/ipc'
import { SIGN_IN_AWARD } from '@/common/reward-map'
import * as Dayjs from 'dayjs'
import * as SignInApi from '@/api/sign-in'

import SignInPopUp from './sign-in/popup.vue'
import SignInGuide from './sign-in/guide-dialog.vue'

const DIALOG_TYPE = 'task-center-sign-in-guide'
const CONFIG_SECTION = 'TaskCenter_Activity_CheckIn'
const CONFIG_KEY_INDEX_GUIDE = 'CheckInIndexGuideShow'                // 中央引导弹窗是否展示过
const CONFIG_KEY_ENTRY_CLICKED_TIME = 'CheckInEntryClickedTime'       // 入口被点击的时间

// @ts-ignore
window.__dayjs = Dayjs

@Component({
  components: {
    SignInPopUp,
    SignInGuide,
  }
})
export default class App extends Vue {
  // 类型定义
  $refs!: {
    entryBtn: HTMLDivElement
    signInPopUp: SignInPopUp
  }

  isLogined: boolean = false                    // 是否已登录
  userInfo: any = {}                            // 用户信息
  todayWeek: number = Dayjs().day()             // 当前时间是星期几 0-6，0 为周日
  showPopUp: boolean = false                    // 是否展示签到弹窗面板
  isEnableAnimate: boolean = false              // 是否启用入口动画
  showSignInEntry: boolean = false              // 是否展示签到入口
  showGuideDialog: boolean = false              // 是否展示签到引导弹窗（中央弹窗）
  isEntryClicked: boolean = false               // 入口是否被点击过

  gameId: number = 0                            // 游戏 id，每周需要重新生成一次
  records: any[] = []                           // 签到记录
  reSignInCount: number = 0                     // 剩余补签次数
  dailyAwardList: any[] = []                    // 每日签到奖品列表
  finalAwardList: any[] = []                    // 连续签到 7 天的奖品
  remindTimeHour: string = '23'                 // 签到提醒时间（小时），从服务端获取
  remindTimeMinute: string = '59'               // 签到提醒时间（分钟），从服务端获取

  logoutEventId = 0
  loginSucEventId = 0
  userChangeEventId = 0
  personalInfoEventId = 0

  // 转化星期的数据，重新将 1-7 表示周一至周日，方便视图计算
  get weekDay () {
    return this.todayWeek === 0 ? 7 : this.todayWeek
  }

  // 已签到的日期 index
  get signedIndex () {
    return this.records.map(rec => +rec.day_index)
  }

  // 补签到的日期 index
  get reSignedIndex () {
    return this.records.map(rec => {
      if (rec.sign_in_type === 'retroactive') {
        return +rec.day_index
      }
      return null
    }).filter(rec => rec !== null)
  }

  // 是否展示签到按钮动画（未点击、未签到过、过零点未签到则展示）
  get showAnimate () {
    return !this.isEntryClicked && this.isEnableAnimate && !this.signedIndex.includes(this.weekDay)
  }

  get isVip () {
    return this.userInfo?.isVip === '1'
  }

  get isSuperVip () {
    return this.isVip && this.userInfo?.vasType === '5'
  }

  get iconTitle () {
    return this.isLogined ? '签到' : '签到领会员'
  }

  @Watch('showSignInEntry')
  onShowSignInEntryChange (isShow: boolean) {
    if (isShow) {
      // 自动登录可能会在入口出现之后才完成，延迟 5s 上报
      setTimeout(() => {
        this.$eventTrack('signin_task_top_entry_show', {
          is_animation: +this.showAnimate,
          is_login: +this.isLogined
        })
      }, 5000)
    }
  }

  @Watch('showPopUp')
  onShowPopUpChange (isShow: boolean) {
    if (isShow) {
      this.$eventTrack('signin_task_signin_panel_show')
    }
  }

  async getSignInInfo () {
    if (!this.showSignInEntry) return

    const res = await SignInApi.getInfo()

    if (res.data) {
      this.gameId = res.data.game?.game_id

      const allAsset: any = {}
      res.data.game.all_asset.forEach(asset => {
        allAsset[asset.id] = asset
      })

      const signInConfig = res.data.sign_in_config
      // 每日签到奖品
      this.dailyAwardList = signInConfig.ordinary_assets.map(asset => ({
        ...asset,
        ...SIGN_IN_AWARD[asset.asset_id],
        name: allAsset[asset.asset_id].name,
      })).sort((a, b) => a.day_index - b.day_index)
      // 连续签到 7 天的奖品
      this.finalAwardList = signInConfig.continue_sign_in_assets.map(asset => ({
        ...asset,
        ...SIGN_IN_AWARD[asset.asset_id],
        name: allAsset[asset.asset_id].name,
      }))
      // 服务端以 00:00 格式下发，需要切分出来
      const [ hour, minute ] = signInConfig.push_time.split(':')
      this.remindTimeHour = hour ?? '23'
      this.remindTimeMinute = minute ?? '59'
      // game_id 为 0 则表示未创建游戏，需要手动调用创建接口
      if (this.gameId === 0) {
        await this.manualCreateGame()
      }
      // 需要 game_id
      if (this.gameId !== 0) {
        this.getRecord(this.gameId)
      }
    }
  }

  async manualCreateGame () {
    if (!this.showSignInEntry) return

    const res = await SignInApi.createGame()

    if (res.data) {
      this.gameId = res.data.game_id
    }
  }

  async getRecord (gameId: number) {
    if (!this.showSignInEntry) return

    const record = await SignInApi.getRecord(gameId)

    if (record.data) {
      this.records = record.data.sign_in_records
      this.reSignInCount = record.data.remaining_retroactive_sign_in_count
      // 刷新一下提醒的定时器，延迟 100ms 等数据处理
      setTimeout(() => {
        this.$refs.signInPopUp.handleRemindNotifyPushTimer()
      }, 100)
    }
    // 更新当前时间
    this.todayWeek = Dayjs().day()
    // 启动时间自动检测
    this.autoCheckTimer()
  }

  async getGlobalConfig () {
    const res = await client.callServerFunction('GetRemoteGlobalConfigValueAw', 'home')
    const conf = res.check_in_task || {}

    this.showSignInEntry = !!conf.check_in_entry
    this.isEnableAnimate = !!conf.check_in_entry_motion
  }

  autoCheckTimer () {
    // 当前时间
    const nowDay = +Dayjs()
    // 第二天零点
    const secDay = +Dayjs().add(1, 'day').startOf('d')
    // 计算时差，过零点后延迟 2s 请求数据，且重新开始定时器
    setTimeout(() => {
      this.getSignInInfo()
      // 检查签到提醒
      this.$refs.signInPopUp.handleRemindNotifyPushTimer()
      // 重置入口动画
      this.isEntryClicked = false
    }, secDay - nowDay + 2000)
  }

  handleCheckInPopUpShow (): void {
    this.$eventTrack('signin_task_top_entry_click', {
      is_animation: +this.showAnimate,
      is_login: +this.isLogined
    })
    this.isEntryClicked = true
    setConfigValue(CONFIG_SECTION, CONFIG_KEY_ENTRY_CLICKED_TIME, Date.now())

    // 未登录，点击弹出登录窗口
    if (!this.isLogined) {
      client.callServerFunction('ShowLoginDlg', 'signin_entry', true)
      return
    }
    // 获取数据失败了，toast 提示，不弹出浮窗
    if (!this.dailyAwardList.length || this.gameId === 0) {
      showMessageToast({ message: '操作失败，请稍后重试', type: 'warning' })
      return
    }

    this.todayWeek = Dayjs().day()
    this.showPopUp = !this.showPopUp
  }

  handleGuideCheckIn () {
    // 未登录，点击弹出登录窗口
    if (!this.isLogined) {
      client.callServerFunction('ShowLoginDlg', 'signin_pop', true)
      return
    }
    // 获取数据失败了，toast 提示，不弹出浮窗
    if (!this.dailyAwardList.length || this.gameId === 0) {
      showMessageToast({ message: '操作失败，请稍后重试', type: 'warning' })
      return
    }

    this.todayWeek = Dayjs().day()
    this.showPopUp = true
    this.$refs.signInPopUp.handleCheckIn(this.weekDay).then(() => {
      this.handleGuideClose()
    })
  }

  handleGuideClose () {
    this.showGuideDialog = false
    // 需要通知主渲染进程，已关闭弹窗
    client.callServerFunction('ClosePopMutual', DIALOG_TYPE)
  }

  async handleGuideShow () {
    const isShow = await getConfigValue(CONFIG_SECTION, CONFIG_KEY_INDEX_GUIDE, false) as boolean

    if (!isShow && this.showSignInEntry) {
      // 当前用户引导弹窗未展示过，则进入弹窗排队
      client.callServerFunction('EnqueuePopMutual', DIALOG_TYPE, () => {
        this.showGuideDialog = true

        // 需要通知主渲染进程，已展示弹窗
        client.callServerFunction('PopMutualCreated', DIALOG_TYPE)
        // 写入配置，已经展示过了
        setConfigValue(CONFIG_SECTION, CONFIG_KEY_INDEX_GUIDE, true)
      })
    }
  }

  resetData () {
    this.gameId = 0
    this.records = []
    this.reSignInCount = 0
    this.userInfo = {}
    this.showPopUp = false
  }

  async init () {
    if (!this.showSignInEntry) return
    // 获取用户数据
    this.userInfo = await getUserInfo()
    // 获取签到的基础信息，内部获取签到记录
    this.getSignInInfo()
  }

  async created () {
    // 获取配置信息
    await this.getGlobalConfig()
    // 引导签到弹窗，仅一次
    if (this.showSignInEntry) {
      this.handleGuideShow()
    }

    this.isLogined = await client.callServerFunction('IsLogined')
    if (this.isLogined) {
      this.init()
    }
    // 当天是否被点击过
    const clickedTime = await getConfigValue(CONFIG_SECTION, CONFIG_KEY_ENTRY_CLICKED_TIME, 0) as number
    this.isEntryClicked = !Dayjs().isAfter(Dayjs(clickedTime), 'day')

    // 退出登录
    this.logoutEventId = client.attachServerEvent('OnLogout', async (context: any, userid: string) => {
      this.isLogined = false
      this.resetData()
    })
    // 登录成功
    this.loginSucEventId = client.attachServerEvent('OnLoginSuc', (context: any, allUserinfo: any) => {
      this.isLogined = true
      this.init()
    })
    // 用户账号信息变更
    this.userChangeEventId = client.attachServerEvent('OnUserInfoChange', async (ctx, type, jsonString) => {
      this.userInfo = await getUserInfo()
    })
  }

  handleWindowClick (event: MouseEvent) {
    if (!event.composedPath().includes(this.$refs.signInPopUp.$el)
      && !event.composedPath().includes(this.$refs.entryBtn)
    ) {
      this.showPopUp = false
    }
  }

  handleWindowBlur () {
    this.showPopUp = false
  }

  mounted () {
    window.addEventListener('click', this.handleWindowClick, true)
    window.addEventListener('blur', this.handleWindowBlur)
    // 个人信息窗弹出时，隐藏签到浮窗
    this.personalInfoEventId = client.attachServerEvent('HoverWindowReadyToShow', () => {
      if (this.showPopUp) {
        this.showPopUp = false
      }
    })
  }

  beforeDestroy () {
    client.detachServerEvent('OnLogout', this.logoutEventId)
    client.detachServerEvent('OnLoginSuc', this.loginSucEventId)
    client.detachServerEvent('OnUserInfoChange', this.userChangeEventId)
    client.detachServerEvent('HoverWindowReadyToShow', this.personalInfoEventId)
    window.removeEventListener('click', this.handleWindowClick, true)
    window.removeEventListener('blur', this.handleWindowBlur)
  }
}
</script>

<style src="@/assets/css/xly-var.css"></style>
<style src="@/assets/css/xly-common.css"></style>
<style src="@/assets/css/xly-icon.css"></style>
<style src="@/assets/css/xly-header.css"></style>
<style src="@/assets/css/download/xly-sign-in.css"></style>
<style src="@/assets/css/download/xly-dialog-sign-in.css"></style>

<style scoped>
.xly-header-sign-in {
  margin: 0 6px;
  cursor: pointer;
}
</style>
