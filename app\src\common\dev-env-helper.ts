import * as path from 'path';
import { LogLevel } from '@xunlei/tiny-logger';
import requireNodeFile from '@/common/require-node-file';

const thunderHelper: any = requireNodeFile(path.join(__rootDir, '../bin/ThunderHelper.node'));

export namespace DevEnvHelperNS {
  export function isDevToolsEnable(): boolean {
    return isLogEnable();
  }

  export function isLogEnable(): boolean {
    let ret: boolean = true;
    do {
      if (process.env.NODE_ENV !== 'production') {
        break;
      }
      const iniPath: string = path.resolve('C:\\ETW_LOG\\log.ini');
      const enableLog: string = thunderHelper.readINI(iniPath, 'Log', 'enable');
      ret = enableLog === '1';
    } while (false);
    return ret;
  }

  export function getLogOutput(): string {
    let ret: string = process.env.TL_OUTPUT;
    do {
      if (ret && ret !== '') {
        // 优先用环境变量的
        break;
      }
      const iniPath: string = path.resolve('C:\\ETW_LOG\\log.ini');
      ret = thunderHelper.readINI(iniPath, 'Log', 'output');
    } while (false);
    return ret;
  }

  export function getLogLevel(): number {
    let level: LogLevel = LogLevel.Information;
    do {
      let ret: string = process.env.TL_LEVEL;
      if (!ret) {
        const iniPath: string = path.resolve('C:\\ETW_LOG\\log.ini');
        ret = thunderHelper.readINI(iniPath, 'Log', 'level');
      }
      
      level = Number(ret);
      if (isNaN(level)) {
        level = LogLevel.Information;
        break;
      }

      if (level in LogLevel) {
        break;
      }

      level = LogLevel.Information;
    } while (false);
    return level;
  }
}
