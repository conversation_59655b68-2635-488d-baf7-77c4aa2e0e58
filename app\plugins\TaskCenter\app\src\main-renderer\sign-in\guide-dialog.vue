<template>
  <div>
    <td-dialog
      custom-class="xly-dialog-sign-in"
      visible
      :footer-enabled="false"
      @close="handleClose"
    >
      <h3 slot="header">签到领好礼</h3>
      <p>连续签到，领取超级会员</p>
      <img :src="img" alt="">
      <td-button @click="handleCheckIn">立即签到</td-button>
    </td-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'

@Component
export default class GuideDialog extends Vue {
  img = require('@/assets/img/sign-in/card-dialog.png')

  handleClose () {
    this.$eventTrack('signin_task_pop_click', { clickid: 'close' })
    this.$emit('close')
  }

  handleCheckIn () {
    this.$eventTrack('signin_task_pop_click', { clickid: 'signin' })
    this.$emit('checkIn')
  }

  mounted () {
    this.$eventTrack('signin_task_pop_show')
  }
}
</script>