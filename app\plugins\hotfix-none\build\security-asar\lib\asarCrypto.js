const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
/**
 * AES加密的配置 
 * 1.密钥 
 * 2.偏移向量 
 * 3.算法模式ECB(这样才能分段解密)
 */

/**
 * @param data Buffer类型
* AES_128_CBC 加密 
* 128位 
* return base64
*/
function encryption(data, key) {
  if (typeof data === 'string') {
    data = Buffer.from(data);
  }
  var cipherChunks = [];
  var cipher = crypto.createCipheriv('aes-128-ecb', key, Buffer.from([]));
  cipher.setAutoPadding(false);
  let mod = data.length % 16;
  // 用空格补全data到16的倍数
  if (mod !== 0) {
    let addspace = '';
    for (let i = 0; i < (16 - mod); i++) {
      addspace += ' ';
    }
    data = Buffer.concat([data, Buffer.from(addspace)]);
    // console.log('#### ', data.length);
  }

  cipherChunks.push(cipher.update(data));
  cipherChunks.push(cipher.final());
  return Buffer.concat(cipherChunks);
}


/**
* 解密
* return utf8
*/
function decryption(data, key){
  var cipherChunks = [];
  var decipher = crypto.createDecipheriv('aes-128-ecb', key, Buffer.from([]));
  decipher.setAutoPadding(false);
  cipherChunks.push(decipher.update(data));
  cipherChunks.push(decipher.final());
  return Buffer.concat(cipherChunks);
}

/**
 * 加密文件
 */
function encryptFile(filename, outputfile, key) {
  file = fs.readFileSync(filename);
  let value = encryption(file, key);
  console.log('## encryptFile', filename, ' key:', key, ' outputfile:', outputfile);
  fs.writeFileSync(outputfile, value);
  let dec_value = decryption(value, key);
  console.log('### try decrypt value: ', file.toString('utf-8').slice(0, 10), dec_value.toString('utf-8').slice(0, 10));
}

// 从文件加载key:
function loadKey(file) {
  // key实际上就是PEM编码的字符串:
  return fs.readFileSync(path.resolve(__dirname, file), 'utf8');
}

/**
 * 解密文件
 */
function decryptFile(filename, outputfile, key) {
  file = fs.readFileSync(filename);
  console.log('## decryptFile', filename, ' key:', key)
  let value = decryption(file, key);
  fs.writeFileSync(outputfile, value);
}

/**
 * 加密头部, 使用RSA_PKCS1_PADDING
 * 加密后的长度固定为256
 */
function encryptHeader(data) {
  let prvKey = loadKey('./rsa-prv.pem')
  // 使用私钥加密:
  let enc_by_prv = crypto.privateEncrypt({ key: prvKey, padding: crypto.RSA_PKCS1_PADDING }, data);
  return enc_by_prv;
}

/**
 * 解密头部, 使用RSA_PKCS1_PADDING
 */
function decryptHeader(data) {
  let pubKey = loadKey('./rsa-pub.pem')
  let dec_by_pub = crypto.publicDecrypt({ key: pubKey, padding: crypto.RSA_PKCS1_PADDING }, data);
  return dec_by_pub;
}

/**
 * 对header里面的files的字符串计算并比较md5, 杜绝篡改
 */
function calculateMd5(str) {
  let md5 = crypto.createHash('md5');
  md5.update(str);
  return md5.digest("hex");
}

module.exports.encryptFile = encryptFile;
module.exports.decryptFile = decryptFile;
module.exports.encryption = encryption;
module.exports.decryption = decryption;
module.exports.decryptHeader = decryptHeader;
module.exports.encryptHeader = encryptHeader;
module.exports.calculateMd5 = calculateMd5;