'use strict';
const chalk = require('chalk');
const webpack = require('webpack');
// const Multispinner = require('multispinner');
const run = require('parallel-webpack').run;
const getJobs = require('./get-jobs');

process.setMaxListeners(0);

let startTime = new Date().getTime();

let config = require('./config');

let onlyBuildTaskName = process.argv[2];

if (onlyBuildTaskName) {
  let onlyBuildTask = config.filter((task) => task.name === onlyBuildTaskName);
  if (onlyBuildTask.length) {
    console.log(`Only build ${onlyBuildTaskName}`);
    config = onlyBuildTask;
  } else {
    console.log(`Only build args error, the process config is not found. Build All.`);
  }
}

const startLog = chalk.bgBlue.white(' START ') + ' ';
const doneLog = chalk.bgGreen.white(' DONE ') + ' ';
const errorLog = chalk.bgRed.white(' ERROR ') + ' ';
const okayLog = chalk.bgBlue.white(' OKAY ') + ' ';
const timeLog = chalk.bgBlue.white(' TIME ') + ' ';

const jobs = getJobs(config);

build(jobs);

// 构建
function build(jobs) {
  console.log(`${startLog}build
  `);
  // const m = new Multispinner(jobs.map((job) => job.name), { preText: 'building', postText: 'process' });

  let results = `${timeLog}Build time: ${new Date()} `;

  // m.on('success', () => {
  //   process.stdout.write('\x1B[2J\x1B[0f');
  //   console.log(`\n\n${results}`);
  //   let time = new Date().getTime() - startTime;
  //   console.log(
  //     `${okayLog} build finished, use ${Date().toString()} use ${time / 1000}s\n`
  //   );
  //   process.exit();
  // });

  jobs.forEach(({ name, config }) => {
    if (name == "renderer") {
      packByConfig(config)
      .then((result) => {
        let result1 = `${timeLog}Build time: ${new Date()} `;
        result1 += `${doneLog} Task ${name} build Result: \n`;
        result1 += result + '\n\n';
        let time = new Date().getTime() - startTime;
        console.log(`Finished build render ${new Date()} after ${time / 1000}s \n ${result1}`);
        // m.success(name);
      })
      .catch((err) => {
        // m.error(name);
        console.log(`\n  ${errorLog}failed to build ${name} process`);
        console.error(`\n${err}\n`);
        process.exit(1);
      });
    } else {
      multiPackByPath(config).then((result)=> {
        results += `${doneLog} Task ${name} build Result: \n`;
        results += result + '\n\n';
        // m.success(name);
      })
      .catch((err) => {
        // m.error(name);
        console.log(`\n ${errorLog}failed to build ${name} process`);
        console.error(`\n${err}\n`);
        process.exit(1);
      })
    }
    
  });
}

function multiPackByPath(configpath) {
  return new Promise((resolve, reject) => {
    console.log(configpath, new Date())
    let worker = 1
    if (configpath.match("main-renderer") != null) { // main-renderer 用4worker
      worker = 4
    } 
    run(configpath, {
      watch: false,
      maxRetries: 1,
      stats: false, // defaults to false
      maxConcurrentWorkers: worker, // use 2 workers
    }, function(err) {
      console.log(configpath, new Date())
      if (err) {
        for (let key in err) {
          console.error(`\n${err[key]}\n`);
        }
        reject(err.message || err);
      }
        else {
        resolve();
      }
    });
  })
}

// webpack打包
function packByConfig(config) {
  return new Promise((resolve, reject) => {
    webpack(config, (err, stats) => {
      if (err) {
        reject(err.stack || err);
      } else if (stats.hasErrors()) {
        let err = '';

        stats
          .toString({
            chunks: false,
            colors: true,
            modules: false
          })
          .split(/\r?\n/)
          .forEach((line) => {
            err += `    ${line}\n`;
          });

        reject(err);
      } else {
        resolve(
          stats.toString({
            chunks: false,
            colors: true,
            modules: false
          })
        );
      }
    });
  });
}
