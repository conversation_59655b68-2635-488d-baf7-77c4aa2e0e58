#pragma once
#include <string>

namespace xl::text {

  class transcode
  {
    public:
      static void Unicode_to_UTF8(const wchar_t* in, size_t len, std::string& out);

      static void UTF8_to_Unicode(const char* in, size_t len, std::wstring& out);

      static void GBK_to_UTF8(const char* in, size_t len, std::string& out);

      static void UTF8_to_GBK(const char* in, size_t len, std::string& out);

      static void Unicode_to_GBK(const wchar_t* in, size_t len, std::string& out);

      static void GBK_to_Unicode(const char* in, size_t len, std::wstring& out);

      static void Unicode_to_ANSI(const wchar_t* in, size_t len, std::string& out);

      static void ANSI_to_Unicode(const char* in, size_t len, std::wstring& out);
    };
}

