/**
 * @description: DownloadKernel对外的导出接口
 *               包括：数据类型、类、方法
 * @author:      Guo<PERSON>ia<PERSON>
 * @version:     1.0
 */

import { TaskUserDataGUID } from '@/common/task-userdata-guid';

export namespace DownloadKernel {
  // 错误码
  export enum TaskError {
    Unkown = 0, // 未知错误

    // TaskErrorTask
    Create, // 任务创建失败(独立进程)
    InvaldParam, // 任务参数错误
    InvaldLink, // 任务链接失效
    InvaldConfig, // 任务配置文件错误
    Timeout, // 任务超时
    VerifyData, // 任务校验失败
    Forbidden, // 任务被禁止下载
    RangeDispatch, // 多线程加速出错
    FilePathOverRanging, // 文件路径超出系统限制

    // TaskErrorDISK                  = 200+
    FileCreate = 201, // 文件创建失败
    FileWrite, // 文件写入失败
    FileRead, // 文件读取失败
    FileRename, // 文件重命名失败
    FileFull, // 磁盘空间不足

    FileOccupied = 211, // 无法创建文件(文件被占用)
    FileAccessDenied, // 无法创建文件(权限不足)

    // TaskErrorP2sp                  = 400+

    // TaskErrorEmule                 = 500+

    // TaskErrorBt                    = 600+
    BtUploadExist = 601, // BT任务已存在

    // Forbindden                        = 700+
    ForbinddenResource = 701, // 敏感资源
    ForbinddenAccount, // 账号异常无法下载
    ForbinddenArea, // 所在区域无法下载
    ForbinddenCopyright, // 应版权方要求无法下载
    ForbinddenReaction,
    ForbinddenPorn,

    DownloadSDKCrash = 10001, // 下载引擎未启动
    torrentFileNotExist = 10002, // 种子文件不存在

    DownloadSDKMissing = 65662, // 下载引擎丢失  载库构造错误码的实现：(((1 & 0x000000FF) << 16) | (126 & 0x0000FFFF)) : LoadLibrary dll不存在的错误码是126；
  }

  export enum DcdnStatusCode {
    Unkown = -1, // 未知
    Success, // 成功
    QueryFailed, // 查询失败
    ServerError, // 服务错误
    ResourceNotFound, // 没有资源
    AuthorizingFailed, // 鉴权失败
    ForbidByCopyright, // 禁用：盗版资源
    ForbidByPorNoGraphy, // 禁用：黄色资源
    ForbidByReactionary, // 禁用：反动资源
    ForbidByOtherFilter // 禁用：关键字、黑名单等
  }

  // 界面展示任务状态任务状态(非下载库的任务状态)
  export enum TaskStatus {
    Begin = -1,
    Unkown, // 未知错误
    StandBy,
    PreDownloading,
    StartWaiting, // 排队等待启动
    StartPending, // 正在开始（即已经调用异步的开始操作，但还没有完成操作）
    Started, // 正在下载
    StopPending, // 正在停止（即已经调用异步的停止操作，但还没有完成操作）
    Stopped, // 已暂停
    Succeeded, // 下载完成
    Failed, // 下载失败
    Seeding, // 正在做种上传(下载完成)
    DestroyPending, // 正在销毁任务并删除文件（即已经调用异步的销毁操作，但还没有完成操作）
    End
  }

  export enum BtFileStatus {
    Begin = -1,
    StandBy,
    Stopped, // 已暂停
    Started, // 正在下载
    Complete, // 下载完成
    Forbidden, // 禁用(敏感、黄反)
    Error, // 错误(重命名错误等)
    End
  }

  export enum DispatchStrategy { // 资源调度策略（按位标识是否启用）
    DispatchStrategyNone = 0,
    DispatchStrategyOrigin = 1 << 0, // 使用原始资源（原始URL, BT资源, EMULE资源等）
    DispatchStrategyP2s = 1 << 1, // 使用P2S资源（HTTP[S], FTP[S])
    DispatchStrategyP2p = 1 << 2, // 使用P2P资源（迅雷Peer）
    DispatchStrategyAll = -1 // 使用所有类型资源
  }

  export enum TaskAttributeFlags {
    None = 0,
    SparseFilePreferred = (1 << 0),     //尝试使用稀疏文件（只对新建(子)任务和NTFS上适用）。在任务开始之前设置
    FileNameFixed = (1 << 1),
    CheckHtml = (1 << 2),     //需要确认是否是html文件
  }

  // 任务类型
  export enum TaskType {
    Unkown = 0,
    P2sp,
    Bt,
    Emule,
    Group,
    Magnet
  }

  export enum TaskExtType {
    UnKnown = 0,
    M3U8,
  }

  export enum TaskCfgType {
    Invalid = 0,
    P2sp,
    Emule
  }

  // 视图类型
  export enum CategroyViewID {
    Unkown = 'Unkown',
    Downloading = 'Downloading', // 正在下载
    Completed = 'Completed', // 已完成
    Recycle = 'Recycle' // 垃圾箱
  }

  // 任务事件
  export enum TaskEventType {
    Unknow = 0,
    TaskCreated,
    InsertToCategoryView, // 任务插入视图的事件，事件参数：事件名, categoryid, categoryViewId, taskList
    RemoveFromCategoryView, // 任务从视图中移除的事件
    StatusChanged, // 任务状态改变的事件
    DetailChanged, // 任务详情改变的事件
    ChannelInfoChanged, // 任务通道信息改变的事件, 实际上该事件不是在下载时触发的
    DcdnStatusChanged, // dcdn状态改变事件
    TaskDescriptionChanged, // 任务描述改变事件通知
    TaskUserRead, // 任务设置已读属性事件通知（已废弃）
    TaskRenamed, // 任务重命名完成事件通知
    TaskMovedEnd, // 移动任务结束事件通知
    TaskMovingStateChange, // 任务移动状态变化
    BtSubFileDetailChanged, // BT子文件详情(包括状态)改变事件通知
    BtSubFileLoaded, // BT子文件加载
    BtSubFileForbidden, // BT部分子文件被禁用事件
    BtSubFileDcdnStatusChanged, // BT子文件dcdn状态改变事件
    BtSubFileSelectChanged, // bt任务的选择下载的个数变更
    TaskCategoryMovedEnd, // 移动任务到另一目录结束事件通知
    GroupTaskSubFileDetailChanged, // 任务组子任务详情更新
    GroupTaskSubFileSelectChanged, // 任务组子任务个数变更
    TaskDestroying, // 任务正在销毁
    TaskDestroyed, // 任务销毁完成
    GroupTaskCountChangeForceUpdate, //
  }

  // 任务属性
  export enum TaskAttribute {
    // 枚举                含义      类型                  默认值
    NumberStrart = 0, // number               0
    TaskId, // taskId
    VirtualId, // 虚拟taskId
    SrcTotal, // 资源总数
    SrcUsing, // 正在使用的资源数
    FileSize, // 文件大小(单位 字节)
    ReceivedSize, // 以接收的总大小包括无效的(单位 字节)
    DownloadSize, // 已下载的有效大小(单位 字节)
    TotalDownloadSize, // 已下载的有效总大小(单位 字节 只针对任务组)
    CreateTime, // 任务创建时间
    CompletionTime, // 任务完成时间
    DownloadingPeriod, // 下载历时(单位秒)
    Progress, // 任务进度
    RecycleTime, // 删除到垃圾箱的时间属性
    FileCreated, // 是否已创建文件
    Forbidden, // 是否禁用
    CategoryId, // 等于-1表示普通下载，大于-1表示私人空间
    UserRead, // 用户是否已读属性
    OpenOnComplete, // 是否设置已完成打开
    GroupTaskId, // 任务组taskid
    DownloadSubTask, // 任务组子任务是否下载
    NameType, // 命名方式   1
    OwnerProduct, // 产品 0
    FileIndex, // 任务组子任务索引,非任务组子任务忽略
    NameFixed, // 文件名确定
    ValidDownloadSize, // 对应 sdk 中 XLTaskInfo::nTotalCalcRecved, 已下载且已校验过的数据大小, 总是递增的
    RealDownloadSize, // 对应 sdk 中 XLTaskInfo::nTotalDownload， 已校验+未校验, 有可能减小，减小时，则发生了纠错
    ResourceLegal, // 是否合法资源,注意这个是业务逻辑触发才有值
    TaskType, // 任务类型               TaskType              TaskType.Unkown
    ErrorCode, // 错误码                 TaskError             TaskError.Unkown
    PlayPosition, // 播放位置
    Duration, // 播放总时长
    NetDiskOriginalResourceErrorCode,
    NumberEnd,

    BooleanStart = 0x1000, // boolean   false
    Destroyed, // 是否已删除属性
    Background, // 是否是后台任务
    Moving, // 正在移动
    BooleanEnd,

    StringStart = 0x2000, // string  ''
    TaskName, // 任务名称
    SavePath, // 保存的路径
    RelativePath, // 相对路径(任务组子任务)
    TaskUrl, // url
    RefUrl, // 引用页
    Cid, // cid
    Gcid, // gcid
    Cookie, // cookie
    ProductInfo, // 产品信息
    Origin, // 来源
    Description, // 任务描述
    UserData, // 用户自定义数据
    OriginName, // sdk从http响应头分析到的文件名
    HTTPContentType, // sdk从http响应头得到的ContentType字段
    CategoryViewId, // 任务视图id       CategroyViewID        CategroyViewID.Unkown
    YunTaskId, // 云盘id
    ClipboardProcessName, // 剪贴板来源进程
    StringEnd,

    ObjectStart = 0x3000, // Object  null
    ObjectEnd
  }

  // TaskDetailChanged事件，属性改变标记
  export enum TaskDetailChangedFlags {
    UnKnown = 0, // 未知（无变化）
    SrcTotal = 1, // 资源总数
    SrcUsing = 1 << 1, // 正在使用的资源数
    FileSize = 1 << 2, // 文件大小(单位 字节)
    ReceivedSize = 1 << 3, // 以接收的总大小包括无效的(曾经被废弃的也算)(单位 字节)
    DownloadSize = 1 << 4, // (已校验 + 未校验)(单位 字节)
    CompletionTime = 1 << 5, // 任务完成时间
    DownloadingPeriod = 1 << 6, // 下载历时(单位秒)
    Progress = 1 << 7, // 任务进度
    RecycleTime = 1 << 8, // 删除到垃圾箱的时间属性
    FileCreated = 1 << 9, // 是否已创建文件
    Forbidden = 1 << 10, // 是否禁用
    UserRead = 1 << 11, // 用户是否已读属性
    OpenOnComplete = 1 << 12, // 是否设置已完成打开
    DownloadSubTask = 1 << 13, // 任务组子任务是否下载
    TaskName = 1 << 14, // 任务名称
    SavePath = 1 << 15, // 保存的路径
    Cid = 1 << 16, // cid
    Gcid = 1 << 17, // gcid
    UserData = 1 << 18, // 用户自定义数据
    CategoryViewId = 1 << 19, // 任务视图id
    ErrorCode = 1 << 20, // 错误码
    TaskSpeed = 1 << 21, // 速度
    ChannelInfo = 1 << 22, // 通道信息
    ValidDownloadSize = 1 << 23, // 有效下载字节数(已校验)
    OriginName = 1 << 24, // sdk从http响应头分析到的文件名
    HTTPContentType = 1 << 25, // sdk从http响应头得到的ContentType字段
    PlayPosition = 1 << 26, // sdk从http响应头得到的ContentType字段
    Duration = 1 << 27, // sdk从http响应头得到的ContentType字段
    YunTaskId = 1 << 28,
    NetDiskOriginalResourceErrorCode = 1 << 29,
  }

  // BtSubFileDetailChanged事件，属性改变标记
  export enum BtSubFileDetailChangedFlags {
    UnKnown = 0 // 未知（无变化）
  }

  // GroupTaskSubFileDetailChanged事件，属性改变标记
  export enum GroupTaskSubFileDetailChangedFlags {
    UnKnown = 0 // 未知（无变化）
  }

  // bt子文件属性
  export enum BtFileAttribute {
    // 枚举                含义      类型                  默认值
    NumberStrart = 0, // number               0
    TaskId, // taskId
    FileStatus, // 子文件状态
    DownloadSize, // 已下载大小
    FileSize, // 文件大小(单位 字节)
    EnableDcdn, // 是否开启DCDN
    ErrorCode, // 错误码
    DcdnStatus, // DCDN状态码
    RealIndex, // 实际文件索引
    FileOffset,
    Visible, // 是否可见
    Download, // 是否需要下载
    UserRead, // 子文件是否已读
    PlayPosition, // 子文件视频播放位置
    Duration, // 子文件视频时长
    DonationPremium, // cdn加速流量
    NumberEnd,

    StringStart = 0x1000, // string  ''
    FinalName, // 文件最终名称
    RelativePath, // 相对路径
    FileName, // 文件名
    FilePath, // 文件路径
    Cid, // 子文件cid
    Gcid, // 子文件gcid
    TraceId, // 传输库实时生成给到上层
    StringEnd,

    BooleanStart = 0x2000, // boolean   false
    IsForbidResource, // 是否敏感资源
    BooleanEnd,
  }

  export enum KeyType {
    P2spUrl, // P2sp任务的URL
    BtInfoId, // BT任务的InfoId
    EmuleFileHash, // Emule任务的FileHash
    MagnetUrl, // 磁力链任务url
    GroupTaskName // 任务组名
  }

  export interface IFindP2spResult {
    taskId: number;
    index: number;
    url: string;
  }

  export interface IFindEmuleResult {
    taskId: number;
    index: number;
    fileHash: string;
  }

  export interface IFindBtResult {
    taskId: number;
    index: number;
    infoId: string;
  }

  export interface IFindGroupResult {
    taskId: number;
    index: number;
    taskName: string;
  }

  export enum SearchKeyType {
    // 任务名称包含关键字
    NameInclude = 1,
    // BT任务显示名称包含关键字
    BtDisplayNameInclude = 2
  }

  export enum FilterKeyType {
    // 后缀名相等
    ExtEqual = 1,
    // 文件名like且后缀名相等
    NameLikeAndExtEqual = 2,
    // 任务类型
    TaskTypeEqual = 4
  }

  export enum KeyFilter {
    All, // 全部
    CommonForeground, // 普通前台任务（不含临时、预下载任务）
    CommonBackground, // 普通后台任务（不含临时、预下载任务）
    Temporary, // 临时任务（暂未支持）
    PreDownload, // 预下载任务（暂未支持）
    PrivateForeground // 私人前台任务（不含临时、预下载任务）
  }

  // 插入任务的原因
  export enum TaskInsertReason {
    Unknown = -1,
    LoadTaskBasic, // 初始化
    Create, // 创建任务
    Complete,// 任务下载完成
    Recycle, // 删除到回收站
    Recover, // 回收站恢复
    ReDownload, // 重新下载
    MoveThoughCategory // 跨目录间移动
  }

  // 任务状态改变的原因
  export enum TaskStopReason {
    Unknown = -1,
    Manual, // 用户手动暂停（含手机遥控、批量）
    PauseAll, // 用户手动暂停批量（仅用户点击全部停止，其他情况，都算Manual
    DeleteTask, // 用户删除暂停（含彻底删除）
    TaskJammed, // 任务卡在99%，需要暂停重启,
    LowSpeed, // 低速任务被移动至队尾而暂停
    MaxDownloadReduce, // 最大任务个数减少引起的任务暂停
    MoveTask, // 任务移动重启任务而暂停任务
    SelectDownloadLists, // 选择下载列表（任务组或bt任务)
    PrivateLoginOut, // 私人空间的任务由于退出登录而停止
    FreeDownload, // 开启空闲下载前先暂停当前所有任务
    Exit, // 用户退出客户端暂停
    ForceFailure, // 强制任务失败
  }

  export enum TaskDeleteFileReason {
    Unknow = -1,
    ReDownload, // 重新下载
    DeleteTask, // 删除任务
    DeleteSubFile, // 任务组详情页彻底删除
  }

  // 查询sql返回值, key为数据库字段, value是查找到的值
  export interface IKeyValue {
    key: string;
    value: string;
  }

  // 下载库全局统计信息
  export interface IGlobalStat {
    size: number;
    recvdBytes: number;
    sentBytes: number;
    dhtNodeCount: number;
    isUPnPReady: number;
  }

  export interface ITaskAttribute {
    attr: TaskAttribute;
    value: any;
  }

  export interface IKeyInfo {
    keyType: KeyType;
    keyValue: string;
    keyFilter: KeyFilter;
  }

  export interface ITaskSizeInfo {
    fileSize: number;
    downloadSize: number;
    receivedSize: number;
  }

  export interface ITaskChannelInfo {
    originSize?: number;
    p2pSize?: number;
    p2sSize?: number;
    offlineSize?: number;
    dcdnSize?: number;
    enableDcdn?: number;
    dcdnResult?: number;
    dcdnStatus?: DcdnStatusCode;
    // 非真实的通道数据，只是任务临时的会员增量（包括offlineSize、dcdnSize）
    // 因为H资源不能会员加速，所以会把会员增量转移到p2p上面，但是后面需求要
    // 通过会员增量进行逻辑UI变更，所以加了这个变量。
    memoryVipSize?: number;
    // 云播统计的时候，需要知道真实的dcnd量，但是在没加速的情况下，上面的dcdnsize又转移到了p2pSize，所以临时存储专门给云播统计用
    dcdnSizeForYunPlayStat?: number;
  }

  export enum RichBoolean {
    unknownValue = -1,    //默认值，表示未知
    falseValue = 0,       //false
    trueValue = 1,        //true
  }

  export interface IQueryTaskInfoCallbackInfo {
    channelInfo?: ITaskChannelInfo;
    virtualId?: number;
    srcTotal?: number;
    srcUsing?: number;
    fileSize?: number;
    totalTime?: number;
    downloadSize?: number;
    receivedSize?: number;
    taskStatus?: number;
    taskError?: number;
    originalName?: string;
    httpContentType?: string;
    finalName?: string;
    cid?: string;
    gcid?: string;
    totalAvailablePeer?: number;
    validDownloadSize?: number;
    originalResource?: number;
    p2sResource?: number;
    p2pResource?: number;
    vipResource?: number;
    netDiskOriginalResourceErrorCode?: number; // 是否归档资源
    isHtml?: RichBoolean;  // 是否html
  }

  export interface IBtDownloadFileInfo {
    realIndex: number;
    fileStatus: number; // DownloadKernel.BtFileStatus
    downloadSize: number;
    fileSize: number;
    enableDcdn?: number;
    errorCode?: number;
    dcdnStatus?: number;
    relativePath?: string;
    finalName?: string;
    cid?: string;
    gcid?: string;
    userRead?: number;
    isSupportPlay?: boolean;
    playPosition?: number;
    duration?: number;
    donationPremium?: number;
    traceId?: string;
  }

  export interface IQueryBtTaskInfoCallbackInfo extends IQueryTaskInfoCallbackInfo {
    fileLists?: IBtDownloadFileInfo[];
  }

  export interface IBtFileInfo {
    fileSize?: number;
    realIndex?: number;
    fileOffset?: number;
    fileName?: string;
    filePath?: string;
    select?: boolean; // 注意只有智能勾选时，才初始化该字段
  }

  export interface IBtTaskInfo {
    title: string;
    infoId: string;
    trackerUrls: string[];
    fileLists: IBtFileInfo[];
  }

  export interface ITaskEventInfo {
    eventType: TaskEventType;
    taskList: ITask[];
    categoryViewId?: CategroyViewID;
    userData?: string;
  }

  export interface ITaskBase {
    taskId?: number;
    taskType?: TaskType;
    taskStatus?: number;
    isMoving?: boolean;
    fileCreated?: number;
    recycleTime?: number;
    forbidden?: number;
    fileSize?: number;
    receiveSize?: number;
    downloadSize?: number; // 已校验+未检验 XLTaskInfo::nTotalDownload, 但更新此值时, 新值<旧值 不更新，参见 tasks.ts:updateTaskInfo
    downloadSpeed?: number;
    vipSpeed?: number;
    srcTotal?: number;
    srcUsing?: number;
    createTime?: number;
    completionTime?: number;
    downloadingPeriod?: number;
    errorCode?: number;
    nameType?: number;
    userRead?: number;
    openOnComplete?: number;
    categoryId?: number;
    ownerProduct?: number;
    downloadSubTask?: number;
    groupTaskId?: number;
    taskName?: string;
    savePath?: string;
    url?: string;
    refUrl?: string;
    cid?: string;
    gcid?: string;
    description?: string;
    productInfo?: string;
    origin?: string;
    userData?: string;
    channelInfo?: ITaskChannelInfo;
    nTotalAvailablePeer?: number;
    vipDownloadSizeCur?: number;
    validDownloadSize?: number; // 已经校验的大小 sdk 中 XLTaskInfo::nTotalCalcRecved
    realDownloadSize?: number; // 已校验+未检验 XLTaskInfo::nTotalDownload
    shiftChannelSize?: number;
    isBackground?: boolean;
    isOneClickDownload?: boolean;
    playPosition?: number;
    duration?: number;
    clipboardProcessName?: string;
    originalResource?: number; //原始地址未废弃资源数(原始URL、BT、Emule资源)
    p2sResource?: number; //P2S未废弃资源数(Server镜像资源)
    p2pResource?: number; //P2P未废弃资源数(迅雷Peer资源)
    vipResource?: number; //会员未废弃资源数(迅雷会员资源)
    traceId?: string;
    netDiskOriginalResourceErrorCode?: number;
  }

  export interface IBtFileData {
    taskId: number;
    status: number; // DownloadKernel.BtFileStatus
    realIndex: number;
    visible: number;
    download: number;
    fileSize: number;
    fileOffset: number;
    filePath: string;
    fileName: string;
  }

  export interface ICreateP2spTaskOptions {
    url: string;
    refUrl: string;
    cookie: string;
    savePath: string;
    fileName: string;
    flags: TaskAttributeFlags;
  }

  export interface INewTaskBaseInfo {
    savePath: string;
    taskName: string;
    groupTaskId?: number;
    description?: string;
    openOnComplete?: number;
    productInfo?: string;
    origin?: string;
    fileSize?: number;
    cid?: string;
    gcid?: string;
    import?: boolean;
    clipboardProcessName?: string;
    taskExtType?: TaskExtType;
    attributeFlags?: TaskAttributeFlags;
    taskIcon?: string;
    checkIsHtml?: boolean;
  }

  export interface INewEmuleTaskInfo {
    url: string;
    origin?: string;
    downloadSubTask?: number;
  }

  export interface INewP2spTaskInfo extends INewEmuleTaskInfo {
    refUrl?: string;
    cookie?: string;
    useOriginResourceOnly?: number;
    originResourceThreadCount?: number;
    nameFixed?: number; // 是否不自动重命名，0：会根据下载库提供的OriginName/ContentType分析出新文件名，
    userAgent?: string;
    httpHeaderField?: string; // 设置http头的 Authorization
    sdkDepotInfo?: { pkg_name: string; soft_id: number; }; // 应用分发的统计信息
  }

  export interface INewBtTaskInfo {
    refUrl?: string;
    originUrl?: string;
    seedFile: string;
    displayName: string;
    fileRealIndexLists: number[];
    fileLists: IBtFileInfo[];
    infoId: string;
    tracker: string;
    subFileScheduler?: DownloadKernel.XLBTTaskSubFileSchedulerType;
    filterState?: { [realIndex: number]: '0' | '1' | '2' };
  }

  export interface INewGroupTaskInfo {
    taskIcon?: string;
    subTaskList: INewTaskInfo[];
  }

  export interface INewMagnetTaskInfo {
    url: string;
    torrentFilePath: string;
  }

  export interface INewTaskInfo {
    taskType?: TaskType;
    taskBaseInfo: INewTaskBaseInfo;
    p2spTaskInfo?: INewP2spTaskInfo;
    btTaskInfo?: INewBtTaskInfo;
    emuleTaskInfo?: INewEmuleTaskInfo;
    groupTaskInfo?: INewGroupTaskInfo;
    magnetTaskInfo?: INewMagnetTaskInfo;
    isBackground?: boolean;
    taskBase?: ITaskBase; // 加载数据库中的任务
    privateSpace?: boolean; // 是否创建到私人空间
    categoryId?: number; // 创建到指定category
    birdKey?: string;
    searchSource?: string; // 搜索创建的下载来源
    isOneClickDownload?: boolean; // 是否一键下载创建的任务，无需落盘，重新下载等不继承
  }

  export interface ISubNetUploader {
    peerId: string;
    hostIp: string;
    tcpPort: number;
    udpPort: number;
    peerCapability: number;
  }

  export interface ICategoryData {
    categoryId: number;
    parentId: number;
    nextId: number;
    status: number;
    name: string;
    description: string;
    defaultPath: string;
    userName: string;
    password: string;
    passwordV2: string;
    accountPassword: string;
  }

  export enum XLResourceFrom {
    // 资源来源，需要使用与（&）操作来判定是否相等，不可直接用等于号（=）判断
    RESOURCE_FROM_MEMBER = 1 << 0,
    RESOURCE_FROM_OFFLINE = 1 << 1,
    RESOURCE_FROM_CRYSTAL_LARGE = 1 << 2,
    RESOURCE_FROM_CRYSTAL_SMALL = 1 << 3,
    RESOURCE_FROM_DCDN = 1 << 4, // P2P资源类型
    RESOURCE_FROM_FREEDCDN = 1 << 5
  }

  export interface IXLServerResourceInfo {
    resFrom: XLResourceFrom; // 资源来源
    url: string; // 下载URL
    refUrl: string; // 引用页
    cookies: string; // Cookie
    maxConnection: number; // 建议的资源最大连接个数
  }

  export enum XLDownloadStrategy {
    XL_TASKDOWNLOAD_STRATEGY_NORMALDOWNLOAD = 0,
    XL_TASKDOWNLOAD_STRATEGY_DOWNLOADINGPLAYING,
    XL_TASKDOWNLOAD_STRATEGY_ONLINEPLAYING
  }

  export enum XLBTTaskSubFileSchedulerType {
    XL_BTTaskSubFileInvalidValue = 0,
    XL_BTTaskSubFileDefaultScheduler = 1, // 表示采用内部默认的调度顺序.
    XL_BTTaskSubFileSequnecialScheduler = 2, // 表示顺序调度子任务.
  }

  // 视图管理接口
  // 获取所有视图, 返回视图数组
  export interface ICategoryViewManager {
    getCategoryViews(): ICategoryView[];
    // 获取视图总数
    getCategoryViewCount(): number;
    // 通过视图id获取视图对象
    getCategoryViewFromId(id: CategroyViewID): ICategoryView;
  }

  // 任务事件接口
  export interface ITaskEventManager {
    // 监听任务事件
    attachTaskEvent(eventType: TaskEventType, callback: (...args: any[]) => void): number;
    // 一次性监听任务事件
    attachTaskEventOnce(eventType: TaskEventType, callback: (...args: any[]) => void): number;
    // 取消监听
    detachTaskEvent(eventType: TaskEventType, cookie: number): void;
  }

  // 任务管理接口
  export interface ITaskManager {
    // 创建任务
    // 如果创建任务失败,返回的task为null
    createTask(taskType: TaskType, newTaskInfo: INewTaskInfo): Promise<ITask>;
    // 批量创建任务(p2sp、emule)
    createBatchTask(newTaskInfos: INewTaskInfo[], groupTask?: ITask): Promise<ITask[]>;
    // 开始任务
    startTask(taskId: number): Promise<boolean>;
    // 使任务处于等待状态
    startWaitingTask(taskId: number): boolean;
    // 批量使任务处于等待状态
    startWaitingTasks(taskIds: number[]): boolean;
    // 暂停任务
    stopTask(taskId: number, reason: TaskStopReason): Promise<void>;
    // 批量暂停任务
    batchStopTask(taskIds: number[], reason: TaskStopReason): Promise<void>;
    // 暂停任务AW
    stopTaskAW(taskId: number, reason: TaskStopReason): Promise<boolean>;
    // 删除任务,删除任务记录
    // deleteFile: 是否删除文件
    deleteTask(taskId: number, deleteFile: boolean, reason: DownloadKernel.TaskDeleteFileReason): Promise<boolean>;
    // 批量删除任务,批量删除任务记录
    // deleteFile: 是否删除文件
    deleteBatchTask(taskIds: number[], deleteFile: boolean, reason: DownloadKernel.TaskDeleteFileReason): Promise<boolean>;
    // 删除到垃圾箱
    removeToRecycle(taskId: number): Promise<void>;
    // 批量删除到垃圾箱
    batchRemoveToRecycle(taskIds: number[]): Promise<void>;
    // 从垃圾箱还原任务
    recoverFromRecycle(taskId: number): boolean;
    // 强制任务失败：适用场景，云盘取回，当云盘任务已失效，则调用强行失败
    forcedFailureTask(taskId: number, errorCode: number): Promise<boolean>;
    // 从垃圾箱还原任务
    batchRecoverFromRecycle(taskIds: number[]): void;
    // 重新下载
    reDownloadTask(taskId: number, newTaskInfo?: INewTaskInfo): Promise<ITask>;
    // 移动任务到目录
    moveTaskToPath(taskId: number, savePath: string, move2Private?: boolean, banToast?: boolean): Promise<boolean>;
    // 移动任务到目录id
    moveCategoryTask(taskId: number, categoryId: number): boolean;
    // 获取该视图下的所有任务总数
    getTaskCount(): number;
    // 通过taskId获取task对象
    getTaskFromTaskId(id: number): ITask;
    // 从性能方面考虑，子任务的稀疏文件属性关联在主任务上，提供接口获取是否开始强制下载标志位的接口
    isTaskForceDownload(taskId: number): boolean;
    // 批量通过关键字查找任务
    batchFindTaskAsync(keyInfos: IKeyInfo[]): Promise<ITask[]>;
    // 是否已加载完数据库历史任务
    isLoadStorageTaskFinish(): boolean;
    // 监听任务状态停止
    attachTaskStopOnce(taskId: number): Promise<boolean>;
    // 重新选择BT/任务组子任务下载
    downloadTaskSelectFiles(taskId: number, fileRealIndexs: string): Promise<boolean>;
    // 批量关联云盘任务
    batchRelateYunTask(
      infos: { panId: string; path: string; taskId: number; user_id: string; path_id: string }[]
    ): void;
    // 寻找任务,传入taskurl，返回taskid，如果不存在，返回-1
    findTask(url: string): number;
    // 云盘任务通过fileid查找任务
    findPanTaskByFileId(fileId: string, categoryId: number): ITask;
    batchFindPanTaskIdByFileId(fileIds: string[], categoryId: number): { [fileId: string]: number };
    // 得到后台任务
    getBackgroundTasks(): number[];
    // 广告任务id
    adTaskId: number;
  }

  // 视图对象接口
  export interface ICategoryView {
    // 获取视图id
    getCategoryViewId(): CategroyViewID;
    // 获取该视图下的所有任务
    getTasks(): Map<number, ITask>;
    // 获取该视图下的所有任务总数
    getTaskCount(): number;
  }

  // 分类管理对象接口
  export interface ICategoryManager {
    init(): Promise<void>;
    // 获取所有目录列表
    getCategorys(): Map<number, ICategory>;
    // 通过名字获取目录
    getCategoryByName(name: string): Map<number, ICategory>;
    // 获取目录总数
    getCategoryCount(): number;
    // 通过id获取目录
    getCategoryFromId(id: number): ICategory;
    // 通过id获取目录下的视图
    getCategoryView(categoryId: number, categoryViewId: CategroyViewID): ICategoryView;
    // 通过目录名和userid获取通用目录
    getCategoryFromUserId(name: string, userId: string, description?: string): ICategory;
    // 通过userid获取私人空间目录
    getPrivateCategoryFromUserId(userId: string): ICategory;
    // 获取当前用户私人空间目录
    getCurrentPrivateCategory(): ICategory;
    // 设置当前用户信息
    setUserInfo(userId: string, accountPassword: string): void;
    // 是否私人空间目录
    isPrivateCategory(categoryId: number): boolean;
    // 获取小站categoryid
    getZhanCategoryId(): number;
  }

  // 私有分类对象接口
  export interface ICategory extends ICategoryViewManager {
    // 获取视图分类id
    getId(): number;
    // 父目录id
    setParentId(parentId: number): void;
    getParentId(): number;
    // 下一兄弟目录id
    setNextId(nextId: number): void;
    getNextId(): number;
    // 目录状态(暂时未用)
    setStatus(status: number): void;
    getStatus(): number;
    // 名称
    setName(name: string): void;
    getName(): string;
    // 描述
    setDescription(description: string): void;
    getDescription(): string;
    // 默认保存路径
    setDefaultPath(defaultPath: string): void;
    getDefaultPath(): string;
    // 用户名
    setUserName(userName: string): void;
    getUserName(): string;
    // 密码（无密码是L""）
    setPassword(password: string, passwordV2: string, save: boolean, callback: Function): void;
    getPassword(): string;
    // 密码V2（更改密码格式，保留之前格式以便版本回退兼容）
    getPasswordV2(): string;
    // 用户密码
    setAccountPassword(accountPassword: string): void;
    getAccountPassword(): string;

    getCategoryData(): ICategoryData;
  }

  // 任务对象接口
  export interface ITask {
    // 获取任务taskId
    getId(): number;
    // 获取任务类型
    getType(): TaskType;
    // 任务重命名(仅限于已完成的任务,重命名成功后会有taskDetailChanged的事件通知)
    reName(newName: string): Promise<void>;
    // 设置是否已读,1: 已读，0:未读
    setUserRead(isRead: number): void;
    // 是否已读,1: 已读，0:未读
    getUserRead(): number;
    // 设置任务描述(详情页里面的备注)
    setDescription(desc: string): void;
    // 设置原始地址下载线程数(p2sp任务)
    setOriginThreadCount(threadCount: number): void;
    // 设置原始地址下载(p2sp任务)
    setOriginOnlyStrategy(isOrigin: boolean): void;
    // 获取是否从原始地址下载(p2sp任务)
    getOriginOnlyStrategy(): boolean;
    // 设置下载策略（nStrategy等于0是乱序下载，等于1是顺序下载，等于2是云播使用的）
    setDownloadStrategy(strategy: XLDownloadStrategy): void;
    // 设置子任务并发数
    setSubTaskConcurrency(concurrency: number): number;
    // 设置任务是否受当前调度视图的调度：同时下载任务数、低速移后
    setFreeFromSchedule(isFree: boolean): void;
    // 设置下载完成时自动打开
    setOpenOnComplete(open: number): void;
    // 获取任务派生类对象
    getExtraObject(): ITaskExtra;
    // 获取任务状态
    getTaskStatus(): TaskStatus;
    // 获取任务的前一个状态
    getTaskPreStatus(): TaskStatus;
    // 获取下载速度
    getDownloadSpeed(): number;
    // 获取会员加速度
    getVipSpeed(): number;
    // 获取任务属性
    getTaskAttribute(attrType: TaskAttribute): any;
    // 获取各个通道的下载大小
    getTaskChannelInfo(): ITaskChannelInfo;
    // 获取任务的数据信息
    getTaskBaseInfo(): ITaskBase;
    // 是否支持播放
    isSupportPlay(): boolean;
    getVideoFileCount(): number;
    isFreeFromSchedule(): boolean;
    // 边下边播url,如果是bt任务,fileIndex是子文件索引，其他任务为-1
    getPlayUrl(fileIndex: number): Promise<string>;
    // 开启DCDN加速，fileIndex默认-1
    enableDcdnWithVipCert(vipCert: string, fileIndex?: number): void;
    // 更新DCDN证书，fileIndex默认-1
    updateDcdnWithVipCert(vipCert: string, fileIndex?: number): void;
    // 开启DCDN加速，fileIndex默认-1
    enableDcdnWithVipCertWithTraceId(traceId: string, vipCert: string, fileIndex?: number): void;
    // 更新DCDN证书，fileIndex默认-1
    updateDcdnWithVipCertWithTraceId(traceId: string, vipCert: string, fileIndex?: number): void;
    // 关闭DCDN加速，fileIndex默认-1
    disableDcdnWithVipCert(fileIndex?: number): void;
    // 设置视图id
    setCategoryViewId(viewID: CategroyViewID): void;
    // 获取任务拥有者名(私人空间为userid)
    getUserName(): string;
    // 判断是否为私人任务
    getIsPrivate(): boolean;
    // 获取保存的任务名
    getTaskName(): string;
    // 获取保存的路径
    getSavePath(): string;
    // 设置保存的路径
    setSavePath(savePath: string): void;
    // 获取virtualTaskId
    getVirtualTaskId(): number;
    // 设置状态原因码
    setStopReason(reason: TaskStopReason): void;
    // 获取状态原因码
    getStopReason(): TaskStopReason;
    // 设置目录id
    setCategoryId(categoryId: number): void;

    // 设置文件大小
    setFileSize(fileSize: number): boolean;
    // 设置gcid
    setGcid(gcid: string): boolean;

    // 设置获取黄反标志
    setResourceLegalStatus(legalStatus: number): void;
    getResourceLegalStatus(): number;

    // 批量设置开始
    beginBatchSetTaskAttribute(): void;
    // 批量设置结束，期间的所有修改只会触发一次DetailChanged事件
    commitBatchSetTaskAttribute(): void;
    // 获取任务数据提供者总数
    getTotalAvailablePeer(): number;
    // 获取任务本次会员通道下载的字节数
    getVipDownloadSizeCur(): number;
    // 设置加速状态
    setAcclerating(bAcc: boolean): void;
    // 获取任务加速状态
    getAcclerating(): boolean;
    // 获取是否一键下载生成的任务 (仅启动生命周期内有效)
    getIsOneClickDownload(): boolean;
    // 获取当前转移加速通道流量字节数
    getShiftChannelSize(): number;
    // 获取上次状态变化标志
    getDetailChangedFlags(): TaskDetailChangedFlags;
    // 设置下载大小
    setDownloadSize(downloadSize: number): boolean;
    // 设置播放点
    setPlayPosition(position: number): boolean;
    // 设置总时长
    setDuration(duration: number): boolean;
    // 添加server
    addServer(fileIndex: number, serverResourceInfo: IXLServerResourceInfo): void;
    // 删除server
    discardServer(fileIndex: number, url: string): void;
    // 设置P2SP任务原始地址新的URL。可在任务生命周期的任意时刻调用。
    redirectOriginalResource(url: string): void;
    // 设置task url
    setTaskUrl(url: string): boolean;
    // 设置Bt子任务为敏感资源
    setBtIndexFileForbid(fileIndex: number, forbid: boolean): void;
    // 限制任务速度
    setTaskDownloadSpeedLimit(speed: number): void;
    // IDC限速
    updateNetDiskTaskMinExpectedSpeed(speed: number): void;
    // 清除 extStat 缓存
    clearExtStatCache(): void
    // 缓存 extStat 数据
    setExtStatCache(k: string | Map<string, any>, v: any): void
    // 设置码率
    UpdateTaskVideoByteRatio(fileIndex: number, bytesPerSecond: number): void
    // 获取任务的traceid，用于token设置的统计，
    getRunningTraceId(): string;
    // 获取p2sp下载链接是否是html
    getP2spDlUrlIsHtml(): boolean
  }

  export interface IPanTaskExtraUserData {
    fileId: string;
    isInSafe: 0 | 1;
    linkExpire: string;
    passCodeToken: string;
    shareId: string;
    token: string;
    link: string;
  }

  export interface ITaskRequestExtraUserData {
    skipFiles?: boolean;
  }

  export interface ITaskExtraInfo {
    extras?: IPanTaskExtraUserData;
  }

  // guidThunderYunDownloadUserData 、guidDownloadCompleteRunParams 、guidCreateShortcut key为非基本类型存储的时候需要 JSON.stringify()
  export type TaskExtraRecord = {
    [TaskUserDataGUID.guidThunderYunDownloadUserData]: ITaskExtraInfo;
    [TaskUserDataGUID.guidDownloadCompleteInstall]: {
      installFile?: string;
      isImmediatelyInstall?: string;
      excludePath?: string;
    };
    [TaskUserDataGUID.guidDownloadCompleteRunParams]: {
      runParams?: Record<string | number, any>;
      skipFiles?: boolean;
    };
    [TaskUserDataGUID.guidCreateShortcut]: {
      createShortcut?: Record<string | number, any>;
    };
    [TaskUserDataGUID.guidNotSavePath]: {
      notSavePath?: string;
    };
    [TaskUserDataGUID.guidHttpHeaderField]: {
      Authorization?: string;
    };
    [TaskUserDataGUID.guidSearchFrom]: {
      searchFrom?: string;
    };
    [TaskUserDataGUID.guidBirdKey]: {
      birdKey?: string;
    };
  };

  // 任务派生类的基类对象接口
  export interface ITaskExtra {
    // 获取任务对象
    getTask(): ITask;

    // 设置任务优先级
    setTaskPriorityLevel(fileIndex: number, priority?: number): void;

    // 存取用户数据(只支持64位整数和字符串)
    setUserData(userGuid: string, key: string, value: any): boolean;
    getUserData(userGuid: string, key?: string): any;

    // 存取用户所有数据，bencoding编码
    setAllUserData(userData: string): void;
    getAllUserData(): string;
  }

  // P2sp任务对象接口
  export interface IP2spTask extends ITaskExtra {
    // 获取线程数
    getThreadCount(): number;
    // 获取是否限速
    getSpeedLimit(): number;
    // 获取是否固定Name
    getNameFixed(): number;
    // 获取展示url
    getDisplayUrl(): string;
    // 获取cookie
    getCookie(): string;
    // 获取UA
    getTaskUserAgent(): string;
  }

  // bt子文件对象接口
  export interface IBtFile {
    // 获取子文件信息
    getBtFileAttribute(attrType: BtFileAttribute): any;
    setBtFileAttribute(attrType: BtFileAttribute, value: any): void;
    getUserRead(): number;
    setUserRead(userRead: number): boolean;
    isSupportPlay(): boolean;
    setPlayPosition(playPosition: number): boolean;
    setDuration(duration: number): boolean;
  }

  // Bt任务对象接口
  export interface IBtTask extends ITaskExtra {
    // 获取Bt任务子文件个数
    getFileCount(): number;
    // 获取Bt任务infoId
    getInfoId(): string;
    // 获取Bt任务DisplayName
    getDisplayName(): string;
    // 获取Bt任务tracker url
    getTrackerUrls(): string;
    // 获取Bt任务种子文件路径
    getSeedFilePath(): string;
    // 获取Bt任务需要下载的子文件列表
    getDownloadFile(): number[];
    // 设置Bt任务需要下载的子文件列表
    // 会覆盖之前设置的,如果之前的子文件没有下载完成,则取消该子文件下载
    setDownloadFile(fileList: number[]): void;
    // 获取Bt任务列表
    getBtFileList(): Map<number, IBtFile>;
    // 获取指定的Bt子集
    getBtFileByRealIndex(realIndex: number): IBtFile;
  }

  // 电驴任务对象接口
  export interface IEmuleTask extends ITaskExtra {
    // 获取文件hash
    getFileHash(): string;
    // 获取config路径
    getConfigPath(): string;
  }

  // 任务组任务对象接口
  export interface IGroupTask extends ITaskExtra {
    // 获取任务组子任务个数
    getSubTaskCount(): number;
    // 获取下载中(即勾选下载)的任务组子任务个数
    getDownloadTaskCount(): number;
    // 获取任务组子任务对象
    getSubTaskByIndex(index: number): ITask;
    // 设置子任务大小
    setSubTaskFileSize(fileIndex: number, fileSize: number): boolean;
    // 获取子任务id信息
    getSubTaskIds(): number[];

    getAllUserDataJson(): {UserData?: Record<string, Record<string, any>>};

    genUploadSettingFile(): Promise<string>;
  }

  export interface IGroupFileInfo {
    fileSize?: number;
    realIndex?: number;
    fileName?: string;
    filePath?: string;
    taskId?: number; // 组里的子任务真实taskId
  }

  // native重构，播放的时候获取相关的任务信息
  export interface ITaskPlayInfo {
    fileName: string;
    cid: string;
    gcid: string;
    playUrl: string;
    fileIndex?: number;
  }

  export enum ETaskStatDescription{
    ForSubFileCurrentDownloading = 0,     //如果子任务未启动，则丢弃该统计项
    ForSubFileOneDownloading = 1,    //如果子任务未启动，则跟随下一次上报。一旦上报，后续该子任务的启动就不再上报此统计
    ForSubFileEveryDownloading = 2,    //子任务的每一次启动都上报
  }

  export interface ITaskStatSlot {
    realIndex: number; // -1 means it's for the main task.
    statInfo: string; // something like 'key_1=value_1,key_2=value_2'
    reportStrategy: ETaskStatDescription;
  }
}
