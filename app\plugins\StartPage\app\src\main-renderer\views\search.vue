<template>
  <!-- 搜索 -->
  <div class="xly-start-search">
    <input ref="input" type="text" :placeholder="placeholder" @keydown.enter="onClickSearch(true)" @keydown.ctrl.a.exact="$refs.input.select()"/>
    <div class="xly-start-search__button" @click="onClickSearch(true)" @mouseover="canShowDrop=true" @mouseleave="canShowDrop=false">
      <button>{{ buttonText }} <i v-show="options && options.length > 1" class="xly-start-search__arrow"></i></button>
      <ul v-show="options && options.length > 1 && canShowDrop" class="xly-start-search__drop">
        <li v-for="(item, index) in options" :key="index" @click.stop="onClickClassify(item, index)">{{ item.text }}</li>
      </ul>
    </div>
  </div>  
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { ISearchClassifyOptions } from '@/main-renderer/common/data-define';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';

@Component({
  components: {
  }
})
export default class Search extends Vue {
  current: number = 0;
  canShowDrop: boolean = false;

  @Prop({})
  options: ISearchClassifyOptions[];

  get buttonText(): string {
    return this.options?.[this.current]?.text ?? '';
  }

  get placeholder(): string {
    return this.options?.[this.current]?.placeholder ?? '';
  }

  async created(): Promise<void> {
    // 获取用户上次选择的搜索分类
    const lastestSelect = await clientModule.callServerFunction('GetConfigValue', 'StartPage', 'classify', '');
    if (lastestSelect) {
      for (let index: number = 0; index < this.options.length; index++) {
        const option = this.options[index];
        if (option.text === lastestSelect) {
          this.current = index;
          break;
        }
      }
    }
  }

  getInputValue(): string {
    const input: HTMLInputElement = this.$refs['input'] as HTMLInputElement;
    return input.value.trim();
  }

  onClickSearch(forceSearch: boolean = true): void {
    const value = this.getInputValue();
    if (value || forceSearch) {
      const item = this.options[this.current];
      let searchUrl: string = item.url;
      searchUrl = searchUrl.replace('$word$', encodeURIComponent(value));

      const encodeText: string = encodeURIComponent(item.text);
      const searchFrom = 'dltab_startup_search-' + encodeText;
      clientModule.callServerFunction('OpenNewTab', searchUrl, JSON.stringify(
        { extData: JSON.stringify({ search_from: searchFrom }) })).catch();

      const extData: string = `search_from=${encodeText},content=${encodeURIComponent(value)}`;

      clientModule.callServerFunction('TrackEvent', 'download_detail', 'dltab_startup_search_start', '', 0, 0, 0, 0,
        extData);
    }
  }

  async onClickClassify(item: ISearchClassifyOptions, index: number): Promise<void> {
    this.canShowDrop = false;
    this.current = index;
    this.onClickSearch(false);
    (this.$refs['input'] as HTMLInputElement).focus();
    await clientModule.callServerFunction('SetConfigValue', 'StartPage', 'classify', item.text);
  }
}
</script>
