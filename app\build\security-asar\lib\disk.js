'use strict'

const fs = require('./wrapped-fs')
const path = require('path')
const pickle = require('chromium-pickle-js')
const crypto = require('./asarCrypto')

const Filesystem = require('./filesystem')
const { head } = require('lodash')
let filesystemCache = {}

async function copyFile (dest, src, filename) {
  const srcFile = path.join(src, filename)
  const targetFile = path.join(dest, filename)

  const [content, stats] = await Promise.all([fs.readFile(srcFile), fs.stat(srcFile), fs.mkdirp(path.dirname(targetFile))])
  return fs.writeFile(targetFile, content, { mode: stats.mode })
}

async function streamTransformedFile (originalFilename, outStream, transformed) {
  return new Promise((resolve, reject) => {
    const stream = fs.createReadStream(transformed ? transformed.path : originalFilename)
    stream.pipe(outStream, { end: false })
    stream.on('error', reject)
    stream.on('end', () => resolve())
  })
}

// 将文件写入
const writeFileListToStream = async function (dest, filesystem, out, list, metadata) {
  for (const file of list) {
    if (file.unpack) { // the file should not be packed into archive
      const filename = path.relative(filesystem.src, file.filename)
      await copyFile(`${dest}.unpacked`, filesystem.src, filename)
    } else {
      await streamTransformedFile(file.filename, out, metadata[file.filename].transformed)
    }
  }
  return out.end()
}

module.exports.writeFilesystem = async function (dest, filesystem, files, metadata) {
  // let headerPickle = pickle.createEmpty();
  // headerPickle.writeString(JSON.stringify(filesystem.header));
  let headerPickle = JSON.stringify(filesystem.header);
	//加密header
  // let aesCipher = crypto.encryption(headerPickle.toBuffer(), aes_conf.key);
  let aesCipher = crypto.encryption(Buffer.from(headerPickle), aes_conf.key);
  let md5Header = crypto.calculateMd5(aesCipher);
	
  //HEADER_LENGTH + MD5 + aes_key
	let rsaPlaintextPickle = pickle.createEmpty();
  rsaPlaintextPickle.writeUInt32(aesCipher.length);
  console.log(`================len=${aesCipher.length}`);
  rsaPlaintextPickle.writeString(md5Header);
  rsaPlaintextPickle.writeString(aes_conf.key);
	
  let rsaCipher = crypto.encryptHeader(rsaPlaintextPickle.toBuffer());
	let rsaCipherSizePickle = pickle.createEmpty();
  rsaCipherSizePickle.writeUInt32(rsaCipher.length);
  
	const out = fs.createWriteStream(dest);
	await new Promise((resolve, reject) => {
		out.on('error', reject)
    out.write(rsaCipherSizePickle.toBuffer());
		out.write(rsaCipher);
		return out.write(aesCipher, () => resolve());
	});
	return writeFileListToStream(dest, filesystem, out, files, metadata);
}
// 写文件内容, 流程是先写sizeBuf, 再写headerBuf
// header的样式如下"rsa_encrypted_key""aes_encrypted_header"
// rsa_encrypted_key 'key xxxxx''md5 xxxxxx', 长度固定为256
// aes_encrypted_header 为用aes_128_ecb模式加密的字符串
// 密钥加密出来的base64字符串长度固定为344, 非base64字符串长度242
// module.exports.writeFilesystemOld = async function (dest, filesystem, files, metadata) {
//   const headerPickle = pickle.createEmpty()
  
//   let headerMd5 = crypto.calculateMd5(JSON.stringify(filesystem.header));
//   let aes = aes_conf.key + headerMd5;
//   // 用rsa加密aes密钥, 以及md5
//   console.warn('### aes header: ', aes);
//   console.log('filesystem.header: ', JSON.stringify(filesystem.header));
//   let enc_buf = crypto.encryptHeader(aes); // base64 encoding, encrypted string
//   console.warn('### encoded aes header: ', Buffer.from(enc_buf, 'latin1').toString('base64'));
//   let fileheader = filesystem.header;
//   let headerstr = crypto.encryption(JSON.stringify(fileheader), aes_conf);
//   let header = enc_buf + headerstr; // 头部也使用aes加密
//   console.log('asar file header: ', '  enc_buf.length:', enc_buf.length, '  headerstr:', headerstr.length);
//   headerPickle.writeString(header);
//   const headerBuf = headerPickle.toBuffer()

//   // header的长度
//   const sizePickle = pickle.createEmpty()
//   sizePickle.writeUInt32(headerBuf.length)
//   const sizeBuf = sizePickle.toBuffer()

//   const out = fs.createWriteStream(dest)
//   await new Promise((resolve, reject) => {
//     out.on('error', reject)
//     out.write(sizeBuf)
//     return out.write(headerBuf, () => resolve())
//   })
//   return writeFileListToStream(dest, filesystem, out, files, metadata)
// }

// 读出头部, 在这里处理解密
module.exports.readArchiveHeaderSync = function (archive) {
  const fd = fs.openSync(archive, 'r')
  let size
  let buffer
  let header
  try {
    const sizeBuf = Buffer.alloc(8)
    if (fs.readSync(fd, sizeBuf, 0, 8, null) !== 8) {
      throw new Error('Unable to read header size')
    }

    const sizePickle = pickle.createFromBuffer(sizeBuf)
    // size = sizePickle.createIterator().readUInt32()
    let nRsaCipherLength = sizePickle.createIterator().readUInt32();
    buffer = Buffer.alloc(nRsaCipherLength)
    if (fs.readSync(fd, buffer, 0, nRsaCipherLength, 8) !== nRsaCipherLength) {
      throw new Error('Unable to read header')
    }
    let rsaPlaintext = crypto.decryptHeader(buffer);
    let headerPicklePickle = pickle.createFromBuffer(rsaPlaintext).createIterator();
    let nAesCipherLength = headerPicklePickle.readUInt32();
    let headermd5 = headerPicklePickle.readString();
    console.log('### headermd5: ', headermd5);
    let aeskey = headerPicklePickle.readString();
    global.aeskey = aeskey;
    console.log('### aeskey: ', aeskey);
    let data = Buffer.alloc(nAesCipherLength);
    fs.readSync(fd, data, 0, nAesCipherLength, 8 + nRsaCipherLength);
    
    let header_str = crypto.decryption(data, aeskey);
    console.log('### header_str: ', header_str.toString());
    header = JSON.parse(header_str);
    let filesMd5 = crypto.calculateMd5(data);
    console.log('### fileMd5:', filesMd5, ' === 校验结果: ', filesMd5 === headermd5);
    header.headerSize = nRsaCipherLength + nAesCipherLength
  } finally {
    fs.closeSync(fd)
  }
  return { header: { files: header.files }, headerSize: header.headerSize }
}

module.exports.readFilesystemSync = function (archive) {
  if (!filesystemCache[archive]) {
    const header = this.readArchiveHeaderSync(archive);
    const filesystem = new Filesystem(archive);
    filesystem.header = header.header;
    filesystem.headerSize = header.headerSize;
    filesystemCache[archive] = filesystem;
  }
  return filesystemCache[archive];
};

module.exports.uncacheFilesystem = function (archive) {
  if (filesystemCache[archive]) {
    filesystemCache[archive] = undefined;
    return true;
  }
  return false;
};

module.exports.uncacheAll = function () {
  filesystemCache = {};
};

// 读文件, 并用aes解密
module.exports.readFileSync = function (filesystem, filename, info) {
  let buffer = Buffer.alloc(info.size);
  if (info.size <= 0) { return buffer; }
  if (info.unpacked) {
    // it's an unpacked file, copy it.
    buffer = fs.readFileSync(path.join(`${filesystem.src}.unpacked`, filename));
  } else {
    // Node throws an exception when reading 0 bytes into a 0-size buffer,
    // so we short-circuit the read in this case.
    const fd = fs.openSync(filesystem.src, 'r');
    try {
      const offset = 8 + filesystem.headerSize + parseInt(info.offset);
      fs.readSync(fd, buffer, 0, info.size, offset);
      if (info.encrypted) {
        buffer = crypto.decryption(buffer, aeskey);
      }
    } finally {
      fs.closeSync(fd);
    }
  }
  return buffer;
};
