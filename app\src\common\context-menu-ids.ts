// ************************************************************************
// *
// * @description: 菜单 id 定义
// *
// ************************************************************************

// 菜单id变量命名规则：'contextMenu' + 模块名/业务名（可缩写） + 具体菜单项
// 菜单id字符串命名规则：全部小写，单词间以'_间隔'，模块名/业务名 + 具体菜单项
export namespace ContextMenuIdsNS {
  // 下载列表右键菜单id定义
  export const contextMenuDownloadImportUncomplete: string = 'context_menu_download_import_uncompleted';
  export const contextMenuDownloadFolder: string = 'context_menu_download_folder';
  export const contxtMenuSubtitleSearch: string = 'context_menu_subtitle_search';
  export const contextMenuDownloadStart: string = 'context_menu_download_start';
  export const contextMenuDownloadPause: string = 'context_menu_download_pause';
  export const contextMenuDownloadStartAll: string = 'context_menu_download_start_all';
  export const contextMenuDownloadPauseAll: string = 'context_menu_download_pause_all';
  export const contextMenuDownloadDelete: string = 'context_menu_download_delete';
  export const contextMenuDownloadDestroy: string = 'context_menu_download_destroy';
  export const contextMenuDownloadMoveFilePrivateSpace: string = 'context_menu_download_move_file_private_space';
  export const contextMenuDownloadMoveFileOutPrivateSpace: string = 'context_menu_download_move_file_out_private_space';
  export const contextMenuDownloadMoveFileOther: string = 'context_menu_download_move_file_other';
  export const contextMenuDownloadMoveFileLastest: string = 'context_menu_download_move_file_lastest';
  export const contextMenuDownloadMoveTop: string = 'context_menu_download_move_top';
  export const contextMenuDownloadMoveBottom: string = 'context_menu_download_move_bottom';
  export const contextMenuDownloadMerge: string = 'context_menu_download_merge';
  export const contextMenuDownloadVipAcc: string = 'context_menu_download_vip_acc';
  export const contextMenuDownloadCopyLink: string = 'context_menu_download_copy_link';
  export const contextMenuDownloadTorrentSaveAs: string = 'context_menu_download_torrent_save_as';
  export const contextMenuDownloadCopyMagnet: string = 'context_menu_download_copy_magnet';
  export const contextMenuDownloadReport: string = 'context_menu_download_report';
  export const contextMenuDownloadAppeal: string = 'context_menu_download_appeal';
  export const contextMenuDownloadOnlineUnzip: string = 'context_menu_download_online_unzip';

  export const contextMenuPlayFile: string = 'context_menu_play_file';
  export const contextMenuDlnaPlayFile: string = 'context_menu_dlna_play_file';
  export const contextMenuDownloadFile: string = 'context_menu_download_file';
  export const contextMenuDownloadOpenAs: string = 'context_menu_download_open_as';
  export const contextMenuDownloadRename: string = 'context_menu_download_rename';
  export const contextMenuDownloadCopyFileLastest: string = 'context_menu_download_copy_file_lastest';
  export const contextMenuDownloadCopyFileOther: string = 'context_menu_download_copy_file_other';
  export const contextMenuDownloadRedownload: string = 'context_menu_download_redownload';
  export const contextMenuDownloadRecover: string = 'context_menu_download_recover';
  export const contextMenuDownloadClear: string = 'context_menu_download_clear';
  export const contextMenuDownloadByShoulei: string = 'context_menu_download_shoulei';
  export const contextMenuDownloadAdd2Cloud: string = 'context_menu_download_add2cloud';
  export const contextMenuDownloadShare: string = 'context_menu_download_share';
  export const contextMenuDwonloadBtSubFileScheduler: string = 'context_menu_download_scheduler';

  export const contextMenuDownloadShowDetail: string = 'context_menu_download_show_detail';
  export const contextMenuDownloadGoPanDir: string = 'context_menu_download_go_pan_dir'; // 打开云盘目录

  // 下载列表横条的菜单id
  export const contextMenuDownloadSelectAll: string = 'context_menu_download_select_all';
  export const contextMenuDownloadDeleteNonexisting: string = 'context_menu_download_delete_non_existing';
  export const contextMenuDownloadAsReadedAll: string = 'context_menu_download_as_readed_all';
  export const contextMenuDownloadOpenHistory: string = 'context_menu_download_open_history';
  export const contextMenuPrivateGuide: string = 'context_menu_private_guide';

  // 任务栏更多的菜单id
  export const contextMenuToolbarMoreOpenSearch: string = 'context_menu_toolbar_more_opensearch';
  export const contextMenuToolbarMoreOpenHelp: string = 'context_menu_toolbar_more_openhelp';
  export const contextMenuToolbarMoreDestroy: string = 'context_menu_toolbar_more_destroy';
  export const contextMenuToolbarMoreConfig: string = 'context_menu_toolbar_more_config';
  export const contextMenuToolbarMoreSortByDefault: string = 'context_menu_toolbar_more_sort_default';
  export const contextMenuToolbarMoreSortByTime: string = 'context_menu_toolbar_more_sort_time';
  export const contextMenuToolbarMoreSortByType: string = 'context_menu_toolbar_more_sort_type';
  export const contextMenuToolbarMoreSortByName: string = 'context_menu_toolbar_more_sort_name';
  export const contextMenuToolbarMoreSortBySize: string = 'context_menu_toolbar_more_sort_size';
  export const contextMenuToolbarMoreSortByStatus: string = 'context_menu_toolbar_more_sort_status';
  export const contextMenuToolbarMoreSortByProgress: string = 'context_menu_toolbar_more_sort_progress';

  // 下载tab切换我的电脑/私人空间的菜单id
  export const contextMenuTabChangeCategoryMyComputer: string = 'context_menu_tab_change_category_mycomputer';
  export const contextMenuTabChangeCategoryPrivateSpace: string = 'context_menu_tab_change_category_privatespace';

  // 私人空间设置按钮的菜单id
  export const contextMenuToolbarPrivateSpaceSelectAll: string = 'context_menu_toolbar_privatespace_select_all';
  export const contextMenuToolbarPrivateSpaceDownloadSetting: string =
    'context_menu_toolbar_privatespace_download_setting';
  export const contextMenuToolbarPrivateSpacePasswwordChange: string =
    'context_menu_toolbar_privatespace_passwword_change';
  export const contextMenuToolbarPrivateSpaceTaskManager: string =
    'context_menu_toolbar_privatespace_download_task_manager';
  // 打开任务位置
  export const contextMenuOpenTaskPosition: string = 'context_menu_search_openfile_position';
  // 手雷传输文件
  export const contextMenuShouleiFileTransfer: string = 'context_menu_shoulei_file_transfer';

  export const contextMenuSwitchDetail: string = 'context_menu_switch_detail'; // 打开详情页
  // thunder11 顶部新建按钮
  export const contextMenuPanUpload: string = 'context_menu_pan_upload'; // 本地上传
  export const contextMenuPanAddLink: string = 'context_menu_pan_add_link'; // 添加链接
  export const contextMenuPanAddBt: string = 'context_menu_pan_add_Bt'; // 添加bt
  export const contextMenuPanCreateFolder: string = 'context_menu_pan_create_folder'; // 新建文件夹
  // 用于xx模块的菜单定义
}
