<template>
  <!-- 推荐 -->
  <div class="xly-start-recommend" v-show="recData.length > 0">
    <div class="xly-start-recommend__title">
      <h3>{{ mainTitle }}</h3>
      <div class="xly-start-recommend__page" v-show="recData.length > 1" @mouseover="onMouseOver" @mouseout="onMouseOut">
        <i class="s-icon-left" @click="handlePrev"></i>
        <p>{{ currentNumber + 1 }}/{{ recData.length }}</p>
        <i class="s-icon-right" @click="handleNext"></i>
      </div>
    </div>

    <transition-group
      name="fade"
      tag="div"
      class="xly-start-recommend__content"
      v-if="recData.length > 0"
    >
      <div
        class="xly-start-module"
        v-for="number in [currentNumber]"
        :key="number"
      >
        <ul>
          <li
            class="xly-start-module__item"
            v-for="(item, index) in recData[currentNumber].data"
            :key="index"
            @mouseover="onMouseOver"
            @mouseout="onMouseOut"
          >
            <div class="xly-start-module__main" @click="onItemClick(recData[currentNumber].index, item, item.index, 'image')">
              <!-- 主图 -->
              <div class="xly-start-module__image">
                <img :src="item.imageUrl" alt="" />
              </div>
              <!-- 左上的标签 -->
              <div class="xly-start-module__label">
                <img :src="item.tag" alt="" />
              </div>
              <!-- 遮罩 -->
              <div class="xly-start-module__mask">
                <button
                  class="xly-start-module__button is-zhan"
                  v-show="item.cta"
                >
                  {{ item.cta }}
                </button>
              </div>
            </div>
            <div class="xly-start-module__title" @click="onItemClick(recData[currentNumber].index, item, item.index, 'title')">
              {{ item.title }}
            </div>
          </li>
        </ul>
      </div>
    </transition-group>
  </div>
</template>

<script lang="ts">
import TinyLogger from '@xunlei/tiny-logger';
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import { IAssets, ISlotItem, IAssetsType } from '@/main-renderer/common/data-define';

const assetsLength: number = 2;
const logger: TinyLogger = TinyLogger.getLogger('start-page-recommend');

@Component({
  components: {
  }
})
export default class Search extends Vue {
  // 加变量后续方便定位问题
  startReason: string;
  stopReason: string;

  @Prop({})
  options: ISlotItem[];

  @Prop({
    default: 4
  })
  rotation: number;

  @Prop()
  visible: boolean;

  @Prop()
  settingInited: boolean;

  @Prop()
  subscribeShow: boolean;
  
  @Prop()
  showedIds: string[];

  @Watch('visible')
  onVisibleChange(val: boolean): void {
    if (val) {
      this.startRotation('select none');
    } else {
      this.stopRotation('taskdetail show');
    }
  }

  get mainTitle(): string {
    return this?.recData[this.currentNumber]?.title ?? '';
  }

  get recData(): {
    title: string;
    index: number;
    data: (IAssets & { index: number })[]
  }[] {
    const data: { title: string; index: number; data: (IAssets & { index: number })[] }[] = [];
    for (let index: number = 0; index < this.options.length; index++) {
      const slot: ISlotItem = this.options[index];
      if (!slot.getSucceed()) {
        continue;
      }

      const mainAssets: IAssets = slot.getAssets();
      if (!mainAssets?.title) {
        continue;
      }
      if (slot.getSucceed() && slot.getSubSlots()?.length > 0) {
        const subSlots: ISlotItem[] = slot.getSubSlots();
        const assets: (IAssets & { index: number })[] = [];
        for (let i: number = 0; i < subSlots.length; i++) {
          const subSlot: ISlotItem = subSlots[i];
          if (subSlot.getSucceed()) {
            const subAssets: IAssets = subSlot.getAssets();
            if (subAssets.imageUrl && subAssets.title) {
              assets.push({ ...subAssets, index: i });

              if (assets.length >= assetsLength) {
                // 最多取N组有效数据
                break;
              }
            }
          }
        }
        if (assets.length === assetsLength) {
          data.push({
            title: mainAssets.title,
            index,
            data: assets,
          });
        }
      }
    }
    return data;
  }

  reportShow(): void {
    if (this.showedIds.length === assetsLength) {
      logger.information('all recommend slots showed', this.showedIds);
      return;
    }

    const optionIndex: number = this.recData[this.currentNumber].index;
    const slot: ISlotItem = this.options[optionIndex];
    slot.show();
    logger.information('report recommend showed', slot.id());
    this.$emit('slot-showed', slot.id());
  }

  currentNumber: number = 0;
  timer: number = null;

  mounted(): void {
    if (this.visible) {
      this.startRotation('mounted');
    }

    this.$watch(() => {
      const { currentNumber, recData } = this;
      return { currentNumber, recData };
    }, () => {
      if (this.visible && this.subscribeShow && this.recData.length > 0) {
        this.reportShow();
      }
    }, { immediate: true });
  }

  onMouseOver(): void {
    this.stopRotation('mouseover');
  }

  onMouseOut(): void {
    this.startRotation('mouseout');
  }

  startRotation(reason: string): void {
    if (this.options.length > 1) {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      this.startReason = reason;
      this.timer = setInterval(this.handleNext, this.rotation * 1000) as any;
    }
  }

  stopRotation(reason: string): void {
    this.stopReason = reason;
    clearTimeout(this.timer);
    this.timer = null;
  }

  handleNext(): void {
    // logger.information('rotation ing');
    this.currentNumber =
      this.currentNumber + 1 === this.recData.length
        ? 0
        : (this.currentNumber += 1);
  }

  handlePrev(): void {
    this.currentNumber =
      this.currentNumber === 0
        ? this.recData.length - 1
        : (this.currentNumber -= 1);
  }

  onItemClick(optionIndex: number, item: IAssets, slotIndex: number, type: IAssetsType): void {
    logger.information('recommend click', optionIndex, slotIndex);
    this.options?.[optionIndex].getSubSlots()?.[slotIndex]?.click(type);
  }
}
</script>
