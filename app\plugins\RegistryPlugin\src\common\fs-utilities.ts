import fs = require('fs');
import util = require('util');
import path = require('path');
import { Logger } from './logger';

const logger: Logger = Logger.getLogger('fs-utilities');
const promisify: any = util.promisify;

export async function readFileAW(filePath: string): Promise<Buffer> {
  let ret: Buffer = null;
  if (filePath !== undefined) {
    const readFile: Promisify = promisify(fs.readFile);
    try {
      ret = await readFile(filePath);
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function writeFileAW(filePath: string, data: any): Promise<boolean> {
  let ret: boolean = false;
  if (filePath !== undefined && data !== null) {
    const writeFile: Promisify = promisify(fs.writeFile);
    try {
      await writeFile(filePath, data);
      ret = true;
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function existsAW(filePath: string): Promise<boolean> {
  let ret: boolean = false;
  if (filePath !== undefined) {
    const exists: Promisify = promisify(fs.access);
    try {
      await exists(filePath);
      ret = true;
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function dirExistsAW(filePath: string): Promise<boolean> {
  let ret: boolean = false;
  do {
    ret = await existsAW(filePath);
    if (!ret) {
      break;
    }
    const stats: fs.Stats = await lstatAW(filePath);
    if (!stats) {
      break;
    }
    ret = stats.isDirectory();
  } while (0);
  return ret;
}

export async function unlinkAW(filePath: string): Promise<boolean> {
  let ret: boolean = false;
  if (filePath !== undefined) {
    const unlink: Promisify = promisify(fs.unlink);
    try {
      await unlink(filePath);
      ret = true;
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function renameAW(oldPath: string, newPath: string): Promise<void> {
  if (oldPath !== undefined && newPath !== undefined) {
    const rename: Promisify = promisify(fs.rename);
    try {
      await rename(oldPath, newPath);
    } catch (err) {
      logger.warning(err);
    }
  }
}

/**
 * @description 获取文件夹下子文件及子文件夹名称<br />
 * @method readdirAW
 * @param {string} dirName 文件夹路径
 * @return {string[]} 成功为 string[] 对象，失败为 null
 * @example
 * let result: string[] = await readdirAW('d:/1/'); <br />
 * @static
 */
export async function readdirAW(dirName: string): Promise<string[]> {
  let ret: string[] = null;
  if (dirName !== undefined) {
    const readdir: Promisify = promisify(fs.readdir);
    try {
      ret = await readdir(dirName);
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function copyFileAW(src: string, dest: string): Promise<boolean> {
  let result: Promise<boolean>;
  if (src.toLowerCase() !== dest.toLowerCase() && (await existsAW(src))) {
    const readStream: fs.ReadStream = fs.createReadStream(src);
    const writeStream: fs.WriteStream = fs.createWriteStream(dest);
    result = new Promise<boolean>((resolve: (value?: boolean | PromiseLike<boolean>) => void): void => {
      const pipeWriteStram: fs.WriteStream = readStream.pipe(writeStream);
      pipeWriteStram.on('finish', () => {
        resolve(true);
      });
      // 需要主动捕获error事件: 可能磁盘空间不足，导致拷贝失败，这里流程不让中断先 error.code === 'ENOSPC'
      pipeWriteStram.on('error', (err: NodeJS.ErrnoException) => {
        logger.warning('copyFileAW error', err.code);
        resolve(false);
      });
    });
  } else {
    result = new Promise<boolean>((resolve: (value?: boolean | PromiseLike<boolean>) => void): void => {
      resolve(false);
    });
  }

  return result;
}

export async function copyDirsAW(src: string, dest: string): Promise<boolean> {
  let ret: boolean = false;
  let stats: fs.Stats = await lstatAW(src);
  if (stats.isDirectory()) {
    ret = await mkdirsAW(dest);
    const files: string[] = (await readdirAW(src)) || [];
    for (let i: number = 0; i < files.length; i++) {
      const filePath: string = path.join(src, files[i]);
      const newPath: string = path.join(dest, files[i]);
      ret = await existsAW(filePath);
      if (ret) {
        stats = await lstatAW(filePath);
        if (stats.isDirectory()) {
          ret = await copyDirsAW(filePath, newPath);
        } else {
          ret = await copyFileAW(filePath, newPath);
        }
      }
    }
  }
  return ret;
}

export async function lstatAW(filePath: string): Promise<fs.Stats> {
  let ret: fs.Stats = null;
  if (filePath !== undefined) {
    const lstat: Promisify = promisify(fs.lstat);
    try {
      ret = await lstat(filePath);
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function mkdirAW(dirName: string): Promise<boolean> {
  let ret: boolean = false;
  if (dirName !== undefined) {
    const mkdir: Promisify = promisify(fs.mkdir);
    try {
      await mkdir(dirName);
      ret = true;
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

/**
 * @description 创建文件夹, 当父文件夹不存在时也会创建父文件夹<br />
 * @method mkdirsAW
 * @param {string} dirName 文件夹路径
 * @return {boolean} 成功为 true，失败为 false
 * @example
 * let result: boolean = await mkdirsAW('d:/2/3/'); <br />
 * @static
 */
export async function mkdirsAW(dirName: string): Promise<boolean> {
  let ret: boolean = false;
  logger.information('mkdirsAW', dirName);
  if (dirName !== undefined) {
    if (await existsAW(dirName)) {
      ret = true;
    } else {
      if (path.dirname(dirName) === dirName) {
        ret = false;
      } else if (await mkdirsAW(path.dirname(dirName))) {
        ret = await mkdirAW(dirName);
      }
    }
  }
  return ret;
}

