<template>
  <!-- 链接 -->
  <div class="xly-start-link">
    <div
      class="xly-start-link__item"
      v-for="(item, index) in options"
      :key="index"
      @click="onClickSite(item, index)"
    >
      <img :src="item.iconUrl" alt="" />
      <span>{{ item.text }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { IRecommendSiteOptions } from '@/main-renderer/common/data-define';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';

@Component({
  components: {
  }
})
export default class Sites extends Vue {
  @Prop({})
  options: IRecommendSiteOptions[];

  onClickSite(item: IRecommendSiteOptions, index: number): void {
    // todo
    const searchFrom = 'dltab_startup_search-web_navigation';
    clientModule.callServerFunction('OpenNewTab', item.url, JSON.stringify(
      { extData: JSON.stringify({ search_from: searchFrom }) })).catch();

    clientModule.callServerFunction('TrackEvent', 'download_detail', 'dltab_startup_web_navigation_click', '', 0, 0, 0, 0,
        `click_id=${encodeURIComponent(item.text)},rn=${index}`);
  }
}
</script>
