/**
 * @description: 日志类
 * @author:      suhuaiqiang
 * @version:     1.0
 */

import fs = require('fs');
import path = require('path');

export class Logger {
  private static logDir: string = path.join(__dirname, '../log');
  private static started: boolean = false;
  private static logger: any = null;
  private keyword: string = '';

  static start(): void {
    if (!Logger.started) {
      Logger.started = true;
      Logger.mkDir();
      Logger.configure();
    }
  }

  static getLogger(keyword: string): Logger {
    return new Logger(keyword);
  }

  constructor(keyword: string) {
    this.keyword = keyword;
  }

  information(...messages: any[]): void {
    if (Logger.logger) {
      Logger.logger.info(`[${this.keyword}]`, ...messages);
    }
  }

  warning(...messages: any[]): void {
    if (Logger.logger) {
      Logger.logger.warn(`[${this.keyword}]`, ...messages);
    }
  }

  error(...messages: any[]): void {
    if (Logger.logger) {
      Logger.logger.error(`[${this.keyword}]`, ...messages);
    }
  }

  private static mkDir(): boolean {
    let ret: boolean = false;
    try {
      fs.mkdirSync(Logger.logDir);
      ret = true;
    } catch (err) {
      //
    }
    return ret;
  }

  private static configure(): void {
    const log4js: any = require('log4js');
    Logger.logger = log4js.getLogger('Thunder');
    log4js.configure({
      appenders: {
        cheese: {
          type: 'file',
          filename: Logger.logName,
          maxLogSize: 100 * 1024 * 1024,
          backups: 10
        }
      },
      categories: { default: { appenders: ['cheese'], level: 'info' } }
    });
  }

  private static get logName(): string {
    const now: Date = new Date();
    const year: number = now.getFullYear(); // 年
    const month: number = now.getMonth() + 1; // 月
    const day: number = now.getDate(); // 日
    const hh: number = now.getHours(); // 时
    const mm: number = now.getMinutes(); // 分
    const ss: number = now.getSeconds(); // 秒

    let clock: string = year + '-';
    if (month < 10) clock += '0';
    clock += month + '-';

    if (day < 10) clock += '0';
    clock += day + '-[';

    if (hh < 10) clock += '0';
    clock += hh + '-';

    if (mm < 10) clock += '0';
    clock += mm + '-';

    if (ss < 10) clock += '0';
    clock += ss;
    clock += ']';

    let name: string = `${Logger.logDir}//${clock}.log`;
    return name;
  }
}

if (process.env.NODE_ENV !== 'production') {
  // Logger.start();
}
