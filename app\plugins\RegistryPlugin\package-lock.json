{"name": "xlregistryplugin", "version": "1.0.4", "lockfileVersion": 1, "requires": true, "dependencies": {"@types/node": {"version": "10.12.26", "resolved": "http://xnpm.repo.xunlei.cn/@types%2fnode/-/node-10.12.26.tgz", "integrity": "sha1-LewZ8feYHJXLVLq49hjstdyYPQ4=", "dev": true}, "@webassemblyjs/ast": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fast/-/ast-1.7.10.tgz", "integrity": "sha1-DPxh1hKGJAty/FIst1VhNpnupAo=", "dev": true, "requires": {"@webassemblyjs/helper-module-context": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10", "@webassemblyjs/wast-parser": "1.7.10"}}, "@webassemblyjs/floating-point-hex-parser": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2ffloating-point-hex-parser/-/floating-point-hex-parser-1.7.10.tgz", "integrity": "sha1-7mPXKcYxGoWGPjaaRz+Zg/mE5Nk=", "dev": true}, "@webassemblyjs/helper-api-error": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fhelper-api-error/-/helper-api-error-1.7.10.tgz", "integrity": "sha1-v8s7vll3U1dHV5CirXsonwmy8Zg=", "dev": true}, "@webassemblyjs/helper-buffer": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fhelper-buffer/-/helper-buffer-1.7.10.tgz", "integrity": "sha1-CoxiTGetCyFNLgA4WZIaGYjLFRs=", "dev": true}, "@webassemblyjs/helper-code-frame": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fhelper-code-frame/-/helper-code-frame-1.7.10.tgz", "integrity": "sha1-CrfiL60CQaFzF4xzl2/A7fUIMs4=", "dev": true, "requires": {"@webassemblyjs/wast-printer": "1.7.10"}}, "@webassemblyjs/helper-fsm": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fhelper-fsm/-/helper-fsm-1.7.10.tgz", "integrity": "sha1-CRXncT+7tzViCp0+T6PXlR+XrGQ=", "dev": true}, "@webassemblyjs/helper-module-context": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fhelper-module-context/-/helper-module-context-1.7.10.tgz", "integrity": "sha1-m+uD9ydA9ayAdTE7XKxeeWUQ91U=", "dev": true}, "@webassemblyjs/helper-wasm-bytecode": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fhelper-wasm-bytecode/-/helper-wasm-bytecode-1.7.10.tgz", "integrity": "sha1-eXsec0u8/eqDmWac3FgwjvHH/8A=", "dev": true}, "@webassemblyjs/helper-wasm-section": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fhelper-wasm-section/-/helper-wasm-section-1.7.10.tgz", "integrity": "sha1-wOo3A8YV17w+NQfDt5kch2ey8g4=", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/helper-buffer": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10", "@webassemblyjs/wasm-gen": "1.7.10"}}, "@webassemblyjs/ieee754": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fieee754/-/ieee754-1.7.10.tgz", "integrity": "sha1-YsFyi37w9m74Ih4pZqCv1120MN8=", "dev": true, "requires": {"@xtuc/ieee754": "^1.2.0"}}, "@webassemblyjs/leb128": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fleb128/-/leb128-1.7.10.tgz", "integrity": "sha1-Fn4LtLBtdwFYV3KnP7qfTfhUOfY=", "dev": true, "requires": {"@xtuc/long": "4.2.1"}}, "@webassemblyjs/utf8": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2futf8/-/utf8-1.7.10.tgz", "integrity": "sha1-tnKPW29QNkq8FVvgKflnDmaFYFo=", "dev": true}, "@webassemblyjs/wasm-edit": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fwasm-edit/-/wasm-edit-1.7.10.tgz", "integrity": "sha1-g/4xQPWlj1owuRRwK+nw5Zo5kJI=", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/helper-buffer": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10", "@webassemblyjs/helper-wasm-section": "1.7.10", "@webassemblyjs/wasm-gen": "1.7.10", "@webassemblyjs/wasm-opt": "1.7.10", "@webassemblyjs/wasm-parser": "1.7.10", "@webassemblyjs/wast-printer": "1.7.10"}}, "@webassemblyjs/wasm-gen": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fwasm-gen/-/wasm-gen-1.7.10.tgz", "integrity": "sha1-TeADgGrinJerNwd4JGm1MplXAXQ=", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10", "@webassemblyjs/ieee754": "1.7.10", "@webassemblyjs/leb128": "1.7.10", "@webassemblyjs/utf8": "1.7.10"}}, "@webassemblyjs/wasm-opt": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fwasm-opt/-/wasm-opt-1.7.10.tgz", "integrity": "sha1-0VHjFhGTSlVsgnif3uxBqBSZPCo=", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/helper-buffer": "1.7.10", "@webassemblyjs/wasm-gen": "1.7.10", "@webassemblyjs/wasm-parser": "1.7.10"}}, "@webassemblyjs/wasm-parser": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fwasm-parser/-/wasm-parser-1.7.10.tgz", "integrity": "sha1-A2e+e/jwnj5qvJX45IO5IGSH7GU=", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/helper-api-error": "1.7.10", "@webassemblyjs/helper-wasm-bytecode": "1.7.10", "@webassemblyjs/ieee754": "1.7.10", "@webassemblyjs/leb128": "1.7.10", "@webassemblyjs/utf8": "1.7.10"}}, "@webassemblyjs/wast-parser": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fwast-parser/-/wast-parser-1.7.10.tgz", "integrity": "sha1-BY9Zi1L3MLI/yHTUd1tihrYkcmQ=", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/floating-point-hex-parser": "1.7.10", "@webassemblyjs/helper-api-error": "1.7.10", "@webassemblyjs/helper-code-frame": "1.7.10", "@webassemblyjs/helper-fsm": "1.7.10", "@xtuc/long": "4.2.1"}}, "@webassemblyjs/wast-printer": {"version": "1.7.10", "resolved": "http://xnpm.repo.xunlei.cn/@webassemblyjs%2fwast-printer/-/wast-printer-1.7.10.tgz", "integrity": "sha1-2BeQnSRQrpbGa3YHYk2YozuEIjs=", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/wast-parser": "1.7.10", "@xtuc/long": "4.2.1"}}, "@xtuc/ieee754": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/@xtuc%2fieee754/-/ieee754-1.2.0.tgz", "integrity": "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=", "dev": true}, "@xtuc/long": {"version": "4.2.1", "resolved": "http://xnpm.repo.xunlei.cn/@xtuc%2flong/-/long-4.2.1.tgz", "integrity": "sha1-XIXWYvdvodNFdXZsXc1mFavNMNg=", "dev": true}, "@xunlei/node-net-ipc": {"version": "1.0.23", "resolved": "http://xnpm.repo.xunlei.cn/@xunlei%2fnode-net-ipc/-/node-net-ipc-1.0.23.tgz", "integrity": "sha512-b/h+71cEN8vYJqrAesJAlhUy+FOWuH1IZ9U4xFTK02yJgdU44yJG+z0lZO3p//r3VB5DN/FJFh0I1FTABS2c9g=="}, "accepts": {"version": "1.3.5", "resolved": "http://xnpm.repo.xunlei.cn/accepts/-/accepts-1.3.5.tgz", "integrity": "sha1-63d99gEXI6OxTopywIBcjoZ0a9I=", "dev": true, "requires": {"mime-types": "~2.1.18", "negotiator": "0.6.1"}}, "acorn": {"version": "5.7.3", "resolved": "http://xnpm.repo.xunlei.cn/acorn/-/acorn-5.7.3.tgz", "integrity": "sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk=", "dev": true}, "acorn-dynamic-import": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/acorn-dynamic-import/-/acorn-dynamic-import-3.0.0.tgz", "integrity": "sha1-kBzu5Mf6rvfgetKkfokGddpQong=", "dev": true, "requires": {"acorn": "^5.0.0"}}, "ajv-errors": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ajv-errors/-/ajv-errors-1.0.0.tgz", "integrity": "sha1-7PAh+hCP0X37Xms4Py3SM+Mf/Fk=", "dev": true}, "ajv-keywords": {"version": "3.2.0", "resolved": "http://xnpm.repo.xunlei.cn/ajv-keywords/-/ajv-keywords-3.2.0.tgz", "integrity": "sha1-6GuBnGAs+IIa1jdBNpjx3sAhhHo=", "dev": true}, "ansi-colors": {"version": "3.2.1", "resolved": "http://xnpm.repo.xunlei.cn/ansi-colors/-/ansi-colors-3.2.1.tgz", "integrity": "sha1-ljgEfkIT80KKEZRKfUsxy6Cj/5U=", "dev": true}, "ansi-html": {"version": "0.0.7", "resolved": "http://xnpm.repo.xunlei.cn/ansi-html/-/ansi-html-0.0.7.tgz", "integrity": "sha1-gTWEAhliqenm/QOflA0S9WynhZ4=", "dev": true}, "ansi-regex": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/ansi-regex/-/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true}, "ansi-styles": {"version": "3.2.1", "resolved": "http://xnpm.repo.xunlei.cn/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "anymatch": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "dev": true, "requires": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "aproba": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/aproba/-/aproba-1.2.0.tgz", "integrity": "sha1-aALmJk79GMeQobDVF/DyYnvyyUo=", "dev": true}, "arr-diff": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/arr-diff/-/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true}, "arr-flatten": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/arr-flatten/-/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=", "dev": true}, "arr-union": {"version": "3.1.0", "resolved": "http://xnpm.repo.xunlei.cn/arr-union/-/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "dev": true}, "array-flatten": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/array-flatten/-/array-flatten-2.1.1.tgz", "integrity": "sha1-Qmu52oQJDBg42BLIFQryCoMx4pY=", "dev": true}, "array-union": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/array-union/-/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "dev": true, "requires": {"array-uniq": "^1.0.1"}}, "array-uniq": {"version": "1.0.3", "resolved": "http://xnpm.repo.xunlei.cn/array-uniq/-/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=", "dev": true}, "array-unique": {"version": "0.3.2", "resolved": "http://xnpm.repo.xunlei.cn/array-unique/-/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "dev": true}, "asn1.js": {"version": "4.10.1", "resolved": "http://xnpm.repo.xunlei.cn/asn1.js/-/asn1.js-4.10.1.tgz", "integrity": "sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=", "dev": true, "requires": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "assert": {"version": "1.4.1", "resolved": "http://xnpm.repo.xunlei.cn/assert/-/assert-1.4.1.tgz", "integrity": "sha1-mZEtWRg2tab1s0XA8H7vwI/GXZE=", "dev": true, "requires": {"util": "0.10.3"}, "dependencies": {"inherits": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/inherits/-/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=", "dev": true}, "util": {"version": "0.10.3", "resolved": "http://xnpm.repo.xunlei.cn/util/-/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "dev": true, "requires": {"inherits": "2.0.1"}}}}, "assign-symbols": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/assign-symbols/-/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "dev": true}, "async": {"version": "2.6.1", "resolved": "http://xnpm.repo.xunlei.cn/async/-/async-2.6.1.tgz", "integrity": "sha1-skWiPKcZMAROxT+kaqAKPofGphA=", "dev": true, "requires": {"lodash": "^4.17.10"}}, "async-each": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/async-each/-/async-each-1.0.1.tgz", "integrity": "sha1-GdOGodntxufByF04iu28xW0zYC0=", "dev": true}, "atob": {"version": "2.1.2", "resolved": "http://xnpm.repo.xunlei.cn/atob/-/atob-2.1.2.tgz", "integrity": "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=", "dev": true}, "axios": {"version": "0.18.1", "resolved": "http://xnpm.repo.xunlei.cn/axios/-/axios-0.18.1.tgz", "integrity": "sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=", "requires": {"follow-redirects": "1.5.10", "is-buffer": "^2.0.2"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "requires": {"ms": "2.0.0"}}, "follow-redirects": {"version": "1.5.10", "resolved": "http://xnpm.repo.xunlei.cn/follow-redirects/-/follow-redirects-1.5.10.tgz", "integrity": "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=", "requires": {"debug": "=3.1.0"}}, "is-buffer": {"version": "2.0.4", "resolved": "http://xnpm.repo.xunlei.cn/is-buffer/-/is-buffer-2.0.4.tgz", "integrity": "sha1-PlcvI8hBGlz9lVfISeNmXgspBiM="}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "balanced-match": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/balanced-match/-/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "base": {"version": "0.11.2", "resolved": "http://xnpm.repo.xunlei.cn/base/-/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "dev": true, "requires": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "base64-js": {"version": "1.3.0", "resolved": "http://xnpm.repo.xunlei.cn/base64-js/-/base64-js-1.3.0.tgz", "integrity": "sha1-yrHmEY8FEJXli1KBrqjBzSK/wOM=", "dev": true}, "batch": {"version": "0.6.1", "resolved": "http://xnpm.repo.xunlei.cn/batch/-/batch-0.6.1.tgz", "integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=", "dev": true}, "big.js": {"version": "3.2.0", "resolved": "http://xnpm.repo.xunlei.cn/big.js/-/big.js-3.2.0.tgz", "integrity": "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=", "dev": true}, "binary-extensions": {"version": "1.12.0", "resolved": "http://xnpm.repo.xunlei.cn/binary-extensions/-/binary-extensions-1.12.0.tgz", "integrity": "sha1-wteA9T1Fu6gxeokC1M7q86Y4WxQ=", "dev": true}, "bl": {"version": "1.2.3", "resolved": "http://xnpm.repo.xunlei.cn/bl/-/bl-1.2.3.tgz", "integrity": "sha1-Ho3YAULqyA1xWMnczAR/tiDgNec=", "requires": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "readable-stream": {"version": "2.3.7", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}}}, "bluebird": {"version": "3.5.2", "resolved": "http://xnpm.repo.xunlei.cn/bluebird/-/bluebird-3.5.2.tgz", "integrity": "sha1-G+CQjgVKdRdUVJwnBInBUF1KsVo=", "dev": true}, "bn.js": {"version": "4.11.8", "resolved": "http://xnpm.repo.xunlei.cn/bn.js/-/bn.js-4.11.8.tgz", "integrity": "sha1-LN4J617jQfSEdGuwMJsyU7GxRC8=", "dev": true}, "body-parser": {"version": "1.18.3", "resolved": "http://xnpm.repo.xunlei.cn/body-parser/-/body-parser-1.18.3.tgz", "integrity": "sha1-WykhmP/dVTs6DyDe0FkrlWlVyLQ=", "dev": true, "requires": {"bytes": "3.0.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "~1.6.3", "iconv-lite": "0.4.23", "on-finished": "~2.3.0", "qs": "6.5.2", "raw-body": "2.3.3", "type-is": "~1.6.16"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "bonjour": {"version": "3.5.0", "resolved": "http://xnpm.repo.xunlei.cn/bonjour/-/bonjour-3.5.0.tgz", "integrity": "sha1-jokKGD2O6aI5OzhExpGkK897yfU=", "dev": true, "requires": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}}, "brace-expansion": {"version": "1.1.11", "resolved": "http://xnpm.repo.xunlei.cn/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "2.3.2", "resolved": "http://xnpm.repo.xunlei.cn/braces/-/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "dev": true, "requires": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "brorand": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/brorand/-/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true}, "browserify-aes": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/browserify-aes/-/browserify-aes-1.2.0.tgz", "integrity": "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=", "dev": true, "requires": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "browserify-cipher": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/browserify-cipher/-/browserify-cipher-1.0.1.tgz", "integrity": "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=", "dev": true, "requires": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "browserify-des": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/browserify-des/-/browserify-des-1.0.2.tgz", "integrity": "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=", "dev": true, "requires": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "browserify-rsa": {"version": "4.0.1", "resolved": "http://xnpm.repo.xunlei.cn/browserify-rsa/-/browserify-rsa-4.0.1.tgz", "integrity": "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=", "dev": true, "requires": {"bn.js": "^4.1.0", "randombytes": "^2.0.1"}}, "browserify-sign": {"version": "4.0.4", "resolved": "http://xnpm.repo.xunlei.cn/browserify-sign/-/browserify-sign-4.0.4.tgz", "integrity": "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg=", "dev": true, "requires": {"bn.js": "^4.1.1", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.2", "elliptic": "^6.0.0", "inherits": "^2.0.1", "parse-asn1": "^5.0.0"}}, "browserify-zlib": {"version": "0.2.0", "resolved": "http://xnpm.repo.xunlei.cn/browserify-zlib/-/browserify-zlib-0.2.0.tgz", "integrity": "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=", "dev": true, "requires": {"pako": "~1.0.5"}}, "buffer": {"version": "4.9.1", "resolved": "http://xnpm.repo.xunlei.cn/buffer/-/buffer-4.9.1.tgz", "integrity": "sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg=", "dev": true, "requires": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}}}, "buffer-alloc": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/buffer-alloc/-/buffer-alloc-1.2.0.tgz", "integrity": "sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow=", "requires": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "buffer-alloc-unsafe": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/buffer-alloc-unsafe/-/buffer-alloc-unsafe-1.1.0.tgz", "integrity": "sha1-vX3CauKXLQ7aJTvgYdupkjScGfA="}, "buffer-crc32": {"version": "0.2.13", "resolved": "http://xnpm.repo.xunlei.cn/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="}, "buffer-fill": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/buffer-fill/-/buffer-fill-1.0.0.tgz", "integrity": "sha1-+PeLdniYiO858gXNY39o5wISKyw="}, "buffer-from": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/buffer-from/-/buffer-from-1.1.1.tgz", "integrity": "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=", "dev": true}, "buffer-indexof": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/buffer-indexof/-/buffer-indexof-1.1.1.tgz", "integrity": "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=", "dev": true}, "buffer-xor": {"version": "1.0.3", "resolved": "http://xnpm.repo.xunlei.cn/buffer-xor/-/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "dev": true}, "builtin-status-codes": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/builtin-status-codes/-/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "dev": true}, "bytes": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/bytes/-/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=", "dev": true}, "cacache": {"version": "10.0.4", "resolved": "http://xnpm.repo.xunlei.cn/cacache/-/cacache-10.0.4.tgz", "integrity": "sha1-ZFI2eZnv+dQYiu/ZoU6dfGomNGA=", "dev": true, "requires": {"bluebird": "^3.5.1", "chownr": "^1.0.1", "glob": "^7.1.2", "graceful-fs": "^4.1.11", "lru-cache": "^4.1.1", "mississippi": "^2.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.2", "ssri": "^5.2.4", "unique-filename": "^1.1.0", "y18n": "^4.0.0"}}, "cache-base": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/cache-base/-/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "dev": true, "requires": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}}, "chalk": {"version": "2.4.1", "resolved": "http://xnpm.repo.xunlei.cn/chalk/-/chalk-2.4.1.tgz", "integrity": "sha1-GMSasWoDe26wFSzIPjRxM4IVtm4=", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "chokidar": {"version": "2.0.4", "resolved": "http://xnpm.repo.xunlei.cn/chokidar/-/chokidar-2.0.4.tgz", "integrity": "sha1-NW/04rDo5D4yLRijckYLvPOszSY=", "dev": true, "requires": {"anymatch": "^2.0.0", "async-each": "^1.0.0", "braces": "^2.3.0", "fsevents": "^1.2.2", "glob-parent": "^3.1.0", "inherits": "^2.0.1", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "lodash.debounce": "^4.0.8", "normalize-path": "^2.1.1", "path-is-absolute": "^1.0.0", "readdirp": "^2.0.0", "upath": "^1.0.5"}}, "chownr": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/chownr/-/chownr-1.1.1.tgz", "integrity": "sha1-VHJri4//TfBTxCGH6AH7RBLfFJQ=", "dev": true}, "chrome-trace-event": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/chrome-trace-event/-/chrome-trace-event-1.0.0.tgz", "integrity": "sha1-Rakb0sIMlBHwljtarrmhuV4JzEg=", "dev": true, "requires": {"tslib": "^1.9.0"}}, "cipher-base": {"version": "1.0.4", "resolved": "http://xnpm.repo.xunlei.cn/cipher-base/-/cipher-base-1.0.4.tgz", "integrity": "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "class-utils": {"version": "0.3.6", "resolved": "http://xnpm.repo.xunlei.cn/class-utils/-/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "dev": true, "requires": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "cliui": {"version": "4.1.0", "resolved": "http://xnpm.repo.xunlei.cn/cliui/-/cliui-4.1.0.tgz", "integrity": "sha1-NIQi2+gtgAswIu709qwQvy5NG0k=", "dev": true, "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "string-width": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string-width/-/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "code-point-at": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/code-point-at/-/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "dev": true}, "collection-visit": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/collection-visit/-/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "dev": true, "requires": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://xnpm.repo.xunlei.cn/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://xnpm.repo.xunlei.cn/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "commander": {"version": "2.13.0", "resolved": "http://xnpm.repo.xunlei.cn/commander/-/commander-2.13.0.tgz", "integrity": "sha1-aWS8pnaF33wfFDDFhPB9dZeIW5w=", "dev": true}, "commondir": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/commondir/-/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "dev": true}, "component-emitter": {"version": "1.2.1", "resolved": "http://xnpm.repo.xunlei.cn/component-emitter/-/component-emitter-1.2.1.tgz", "integrity": "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=", "dev": true}, "compressible": {"version": "2.0.15", "resolved": "http://xnpm.repo.xunlei.cn/compressible/-/compressible-2.0.15.tgz", "integrity": "sha1-hXqasKfloH2Ng37UP+Le//ZP4hI=", "dev": true, "requires": {"mime-db": ">= 1.36.0 < 2"}}, "compressing": {"version": "1.5.1", "resolved": "http://xnpm.repo.xunlei.cn/compressing/-/compressing-1.5.1.tgz", "integrity": "sha1-0DGjMRuMLtZWGoQxZx1ahEVASC0=", "requires": {"flushwritable": "^1.0.0", "get-ready": "^1.0.0", "iconv-lite": "^0.5.0", "mkdirp": "^0.5.1", "pump": "^3.0.0", "streamifier": "^0.1.1", "tar-stream": "^1.5.2", "yauzl": "^2.7.0", "yazl": "^2.4.2"}, "dependencies": {"iconv-lite": {"version": "0.5.2", "resolved": "http://xnpm.repo.xunlei.cn/iconv-lite/-/iconv-lite-0.5.2.tgz", "integrity": "sha1-r21ijcz7RjtzZNl/cV5LdLjIwrg=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "pump": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/pump/-/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "compression": {"version": "1.7.3", "resolved": "http://xnpm.repo.xunlei.cn/compression/-/compression-1.7.3.tgz", "integrity": "sha1-J+DhdqryYPfywoE8PkQK258Zk9s=", "dev": true, "requires": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.14", "debug": "2.6.9", "on-headers": "~1.0.1", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "concat-map": {"version": "0.0.1", "resolved": "http://xnpm.repo.xunlei.cn/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "concat-stream": {"version": "1.6.2", "resolved": "http://xnpm.repo.xunlei.cn/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "dev": true, "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "connect-history-api-fallback": {"version": "1.5.0", "resolved": "http://xnpm.repo.xunlei.cn/connect-history-api-fallback/-/connect-history-api-fallback-1.5.0.tgz", "integrity": "sha1-sGhzk0vF40T+9hGhlqb6rgruAVo=", "dev": true}, "console-browserify": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/console-browserify/-/console-browserify-1.1.0.tgz", "integrity": "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=", "dev": true, "requires": {"date-now": "^0.1.4"}}, "constants-browserify": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/constants-browserify/-/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "dev": true}, "content-disposition": {"version": "0.5.2", "resolved": "http://xnpm.repo.xunlei.cn/content-disposition/-/content-disposition-0.5.2.tgz", "integrity": "sha1-DPaLud318r55YcOoUXjLhdunjLQ=", "dev": true}, "content-type": {"version": "1.0.4", "resolved": "http://xnpm.repo.xunlei.cn/content-type/-/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js=", "dev": true}, "cookie": {"version": "0.3.1", "resolved": "http://xnpm.repo.xunlei.cn/cookie/-/cookie-0.3.1.tgz", "integrity": "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s=", "dev": true}, "cookie-signature": {"version": "1.0.6", "resolved": "http://xnpm.repo.xunlei.cn/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "dev": true}, "copy-concurrently": {"version": "1.0.5", "resolved": "http://xnpm.repo.xunlei.cn/copy-concurrently/-/copy-concurrently-1.0.5.tgz", "integrity": "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=", "dev": true, "requires": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "copy-descriptor": {"version": "0.1.1", "resolved": "http://xnpm.repo.xunlei.cn/copy-descriptor/-/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "dev": true}, "copy-webpack-plugin": {"version": "4.6.0", "resolved": "http://xnpm.repo.xunlei.cn/copy-webpack-plugin/-/copy-webpack-plugin-4.6.0.tgz", "integrity": "sha1-5/QN2KaEd9QF3Rt6hUquMksVi64=", "dev": true, "requires": {"cacache": "^10.0.4", "find-cache-dir": "^1.0.0", "globby": "^7.1.1", "is-glob": "^4.0.0", "loader-utils": "^1.1.0", "minimatch": "^3.0.4", "p-limit": "^1.0.0", "serialize-javascript": "^1.4.0"}, "dependencies": {"globby": {"version": "7.1.1", "resolved": "http://xnpm.repo.xunlei.cn/globby/-/globby-7.1.1.tgz", "integrity": "sha1-+yzP+UAfhgCUXfral0QMypcrhoA=", "dev": true, "requires": {"array-union": "^1.0.1", "dir-glob": "^2.0.0", "glob": "^7.1.2", "ignore": "^3.3.5", "pify": "^3.0.0", "slash": "^1.0.0"}}, "pify": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "core-util-is": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac="}, "create-ecdh": {"version": "4.0.3", "resolved": "http://xnpm.repo.xunlei.cn/create-ecdh/-/create-ecdh-4.0.3.tgz", "integrity": "sha1-yREbbzMEXEaX8UR4f5JUzcd8Rf8=", "dev": true, "requires": {"bn.js": "^4.1.0", "elliptic": "^6.0.0"}}, "create-hash": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/create-hash/-/create-hash-1.2.0.tgz", "integrity": "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=", "dev": true, "requires": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "create-hmac": {"version": "1.1.7", "resolved": "http://xnpm.repo.xunlei.cn/create-hmac/-/create-hmac-1.1.7.tgz", "integrity": "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=", "dev": true, "requires": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "cross-env": {"version": "5.2.0", "resolved": "http://xnpm.repo.xunlei.cn/cross-env/-/cross-env-5.2.0.tgz", "integrity": "sha1-bs1MAV1Xc+YUA57lKQdmabnRJvI=", "dev": true, "requires": {"cross-spawn": "^6.0.5", "is-windows": "^1.0.0"}}, "cross-spawn": {"version": "6.0.5", "resolved": "http://xnpm.repo.xunlei.cn/cross-spawn/-/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dev": true, "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "crypto-browserify": {"version": "3.12.0", "resolved": "http://xnpm.repo.xunlei.cn/crypto-browserify/-/crypto-browserify-3.12.0.tgz", "integrity": "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=", "dev": true, "requires": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}}, "cyclist": {"version": "0.2.2", "resolved": "http://xnpm.repo.xunlei.cn/cyclist/-/cyclist-0.2.2.tgz", "integrity": "sha1-GzN5LhHpFKL9bW7WRHRkRE5fpkA=", "dev": true}, "date-format": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/date-format/-/date-format-2.0.0.tgz", "integrity": "sha1-fPexcvHsVk8AA7OeowLFSY+5jI8="}, "date-now": {"version": "0.1.4", "resolved": "http://xnpm.repo.xunlei.cn/date-now/-/date-now-0.1.4.tgz", "integrity": "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=", "dev": true}, "debug": {"version": "3.2.6", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "requires": {"ms": "^2.1.1"}}, "decode-uri-component": {"version": "0.2.0", "resolved": "http://xnpm.repo.xunlei.cn/decode-uri-component/-/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "dev": true}, "deep-equal": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/deep-equal/-/deep-equal-1.0.1.tgz", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=", "dev": true}, "default-gateway": {"version": "2.7.2", "resolved": "http://xnpm.repo.xunlei.cn/default-gateway/-/default-gateway-2.7.2.tgz", "integrity": "sha1-t+8znl4CSwRUZ69APVA0jbRkLQ8=", "dev": true, "requires": {"execa": "^0.10.0", "ip-regex": "^2.1.0"}}, "define-property": {"version": "2.0.2", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "dev": true, "requires": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "dependencies": {"is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "del": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/del/-/del-3.0.0.tgz", "integrity": "sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=", "dev": true, "requires": {"globby": "^6.1.0", "is-path-cwd": "^1.0.0", "is-path-in-cwd": "^1.0.0", "p-map": "^1.1.1", "pify": "^3.0.0", "rimraf": "^2.2.8"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "depd": {"version": "1.1.2", "resolved": "http://xnpm.repo.xunlei.cn/depd/-/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true}, "des.js": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/des.js/-/des.js-1.0.0.tgz", "integrity": "sha1-wHTS4qpqipoH29YfmhXCzYPsjsw=", "dev": true, "requires": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "destroy": {"version": "1.0.4", "resolved": "http://xnpm.repo.xunlei.cn/destroy/-/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=", "dev": true}, "detect-node": {"version": "2.0.4", "resolved": "http://xnpm.repo.xunlei.cn/detect-node/-/detect-node-2.0.4.tgz", "integrity": "sha1-AU7o+PZpxcWAI9pkuBecCDooxGw=", "dev": true}, "diffie-hellman": {"version": "5.0.3", "resolved": "http://xnpm.repo.xunlei.cn/diffie-hellman/-/diffie-hellman-5.0.3.tgz", "integrity": "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=", "dev": true, "requires": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "dir-glob": {"version": "2.2.2", "resolved": "http://xnpm.repo.xunlei.cn/dir-glob/-/dir-glob-2.2.2.tgz", "integrity": "sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=", "dev": true, "requires": {"path-type": "^3.0.0"}}, "dns-equal": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/dns-equal/-/dns-equal-1.0.0.tgz", "integrity": "sha1-s55/HabrCnW6nBcySzR1PEfgZU0=", "dev": true}, "dns-packet": {"version": "1.3.1", "resolved": "http://xnpm.repo.xunlei.cn/dns-packet/-/dns-packet-1.3.1.tgz", "integrity": "sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo=", "dev": true, "requires": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}}, "dns-txt": {"version": "2.0.2", "resolved": "http://xnpm.repo.xunlei.cn/dns-txt/-/dns-txt-2.0.2.tgz", "integrity": "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=", "dev": true, "requires": {"buffer-indexof": "^1.0.0"}}, "domain-browser": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/domain-browser/-/domain-browser-1.2.0.tgz", "integrity": "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=", "dev": true}, "duplexify": {"version": "3.6.1", "resolved": "http://xnpm.repo.xunlei.cn/duplexify/-/duplexify-3.6.1.tgz", "integrity": "sha1-saeinEq/1jlYXvrszoDWZrHjQSU=", "dev": true, "requires": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "ee-first": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "dev": true}, "elliptic": {"version": "6.4.1", "resolved": "http://xnpm.repo.xunlei.cn/elliptic/-/elliptic-6.4.1.tgz", "integrity": "sha1-wtC3d2kRuGcixjLDwGxg8vgZk5o=", "dev": true, "requires": {"bn.js": "^4.4.0", "brorand": "^1.0.1", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.0"}}, "emojis-list": {"version": "2.1.0", "resolved": "http://xnpm.repo.xunlei.cn/emojis-list/-/emojis-list-2.1.0.tgz", "integrity": "sha1-TapNnbAPmBmIDHn6RXrlsJof04k=", "dev": true}, "encodeurl": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "dev": true}, "end-of-stream": {"version": "1.4.1", "resolved": "http://xnpm.repo.xunlei.cn/end-of-stream/-/end-of-stream-1.4.1.tgz", "integrity": "sha1-7SljTRm6ukY7bOa4CjchPqtx7EM=", "requires": {"once": "^1.4.0"}}, "enhanced-resolve": {"version": "4.1.0", "resolved": "http://xnpm.repo.xunlei.cn/enhanced-resolve/-/enhanced-resolve-4.1.0.tgz", "integrity": "sha1-Qcfgv9/nSsH/4eV61qXGyfN0Kn8=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "memory-fs": "^0.4.0", "tapable": "^1.0.0"}}, "errno": {"version": "0.1.7", "resolved": "http://xnpm.repo.xunlei.cn/errno/-/errno-0.1.7.tgz", "integrity": "sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg=", "dev": true, "requires": {"prr": "~1.0.1"}}, "escape-html": {"version": "1.0.3", "resolved": "http://xnpm.repo.xunlei.cn/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "dev": true}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://xnpm.repo.xunlei.cn/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true}, "eslint-scope": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/eslint-scope/-/eslint-scope-4.0.0.tgz", "integrity": "sha1-UL8wcekzi83EMzF5Sgy1M/ATYXI=", "dev": true, "requires": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}}, "esrecurse": {"version": "4.2.1", "resolved": "http://xnpm.repo.xunlei.cn/esrecurse/-/esrecurse-4.2.1.tgz", "integrity": "sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=", "dev": true, "requires": {"estraverse": "^4.1.0"}}, "estraverse": {"version": "4.2.0", "resolved": "http://xnpm.repo.xunlei.cn/estraverse/-/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=", "dev": true}, "etag": {"version": "1.8.1", "resolved": "http://xnpm.repo.xunlei.cn/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "dev": true}, "eventemitter3": {"version": "3.1.0", "resolved": "http://xnpm.repo.xunlei.cn/eventemitter3/-/eventemitter3-3.1.0.tgz", "integrity": "sha1-CQtNbNvWRe0Qv3UNS1QHlC17oWM=", "dev": true}, "events": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/events/-/events-1.1.1.tgz", "integrity": "sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ=", "dev": true}, "eventsource": {"version": "1.0.7", "resolved": "http://xnpm.repo.xunlei.cn/eventsource/-/eventsource-1.0.7.tgz", "integrity": "sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA=", "dev": true, "requires": {"original": "^1.0.0"}}, "evp_bytestokey": {"version": "1.0.3", "resolved": "http://xnpm.repo.xunlei.cn/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "integrity": "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=", "dev": true, "requires": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "execa": {"version": "0.10.0", "resolved": "http://xnpm.repo.xunlei.cn/execa/-/execa-0.10.0.tgz", "integrity": "sha1-/0Vqj1P5D47MxxqW0Rvfx/CCy1A=", "dev": true, "requires": {"cross-spawn": "^6.0.0", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "expand-brackets": {"version": "2.1.4", "resolved": "http://xnpm.repo.xunlei.cn/expand-brackets/-/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "requires": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "express": {"version": "4.16.4", "resolved": "http://xnpm.repo.xunlei.cn/express/-/express-4.16.4.tgz", "integrity": "sha1-/d72GSYQniTFFeqX/S8b2/Yt8S4=", "dev": true, "requires": {"accepts": "~1.3.5", "array-flatten": "1.1.1", "body-parser": "1.18.3", "content-disposition": "0.5.2", "content-type": "~1.0.4", "cookie": "0.3.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.1.1", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.2", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.4", "qs": "6.5.2", "range-parser": "~1.2.0", "safe-buffer": "5.1.2", "send": "0.16.2", "serve-static": "1.13.2", "setprototypeof": "1.1.0", "statuses": "~1.4.0", "type-is": "~1.6.16", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "dependencies": {"array-flatten": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "dev": true}, "debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "extend-shallow": {"version": "3.0.2", "resolved": "http://xnpm.repo.xunlei.cn/extend-shallow/-/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "dev": true, "requires": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "extglob": {"version": "2.0.4", "resolved": "http://xnpm.repo.xunlei.cn/extglob/-/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "dev": true, "requires": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "fast-json-stable-stringify": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I=", "dev": true}, "faye-websocket": {"version": "0.10.0", "resolved": "http://xnpm.repo.xunlei.cn/faye-websocket/-/faye-websocket-0.10.0.tgz", "integrity": "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=", "dev": true, "requires": {"websocket-driver": ">=0.5.1"}}, "fd-slicer": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/fd-slicer/-/fd-slicer-1.1.0.tgz", "integrity": "sha1-JcfInLH5B3+IkbvmHY85Dq4lbx4=", "requires": {"pend": "~1.2.0"}}, "fill-range": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/fill-range/-/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "finalhandler": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/finalhandler/-/finalhandler-1.1.1.tgz", "integrity": "sha1-7r9O2EAHnIP0JJA4ydcDAIMBsQU=", "dev": true, "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.2", "statuses": "~1.4.0", "unpipe": "~1.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "find-cache-dir": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/find-cache-dir/-/find-cache-dir-1.0.0.tgz", "integrity": "sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=", "dev": true, "requires": {"commondir": "^1.0.1", "make-dir": "^1.0.0", "pkg-dir": "^2.0.0"}}, "flatted": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/flatted/-/flatted-2.0.0.tgz", "integrity": "sha1-VRIrZTbqSWtLRIk+4mCBQdENmRY="}, "flush-write-stream": {"version": "1.0.3", "resolved": "http://xnpm.repo.xunlei.cn/flush-write-stream/-/flush-write-stream-1.0.3.tgz", "integrity": "sha1-xdWG7zivYJdlC0m8QbVfq7GfNb0=", "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "flushwritable": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/flushwritable/-/flushwritable-1.0.0.tgz", "integrity": "sha1-PjKNj95BKtR+c44751C00pAENJg="}, "follow-redirects": {"version": "1.5.9", "resolved": "http://xnpm.repo.xunlei.cn/follow-redirects/-/follow-redirects-1.5.9.tgz", "integrity": "sha1-ye2ddIuBSjlTVxblMbkZaoRdicY=", "dev": true, "requires": {"debug": "=3.1.0"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "for-in": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/for-in/-/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true}, "forwarded": {"version": "0.1.2", "resolved": "http://xnpm.repo.xunlei.cn/forwarded/-/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=", "dev": true}, "fragment-cache": {"version": "0.2.1", "resolved": "http://xnpm.repo.xunlei.cn/fragment-cache/-/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "requires": {"map-cache": "^0.2.2"}}, "fresh": {"version": "0.5.2", "resolved": "http://xnpm.repo.xunlei.cn/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "dev": true}, "from2": {"version": "2.3.0", "resolved": "http://xnpm.repo.xunlei.cn/from2/-/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "dev": true, "requires": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "fs-constants": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/fs-constants/-/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0="}, "fs-extra": {"version": "7.0.1", "resolved": "http://xnpm.repo.xunlei.cn/fs-extra/-/fs-extra-7.0.1.tgz", "integrity": "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=", "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs-write-stream-atomic": {"version": "1.0.10", "resolved": "http://xnpm.repo.xunlei.cn/fs-write-stream-atomic/-/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "fsevents": {"version": "1.2.13", "resolved": "http://xnpm.repo.xunlei.cn/fsevents/-/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "dev": true, "optional": true}, "get-caller-file": {"version": "1.0.3", "resolved": "http://xnpm.repo.xunlei.cn/get-caller-file/-/get-caller-file-1.0.3.tgz", "integrity": "sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o=", "dev": true}, "get-ready": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/get-ready/-/get-ready-1.0.0.tgz", "integrity": "sha1-+RgX8emt7P6hOlYq38jeiDqzR4I="}, "get-stream": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/get-stream/-/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "dev": true}, "get-value": {"version": "2.0.6", "resolved": "http://xnpm.repo.xunlei.cn/get-value/-/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "dev": true}, "glob": {"version": "7.1.3", "resolved": "http://xnpm.repo.xunlei.cn/glob/-/glob-7.1.3.tgz", "integrity": "sha1-OWCDLT8VdBCDQtr9OmezMsCWnfE=", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "3.1.0", "resolved": "http://xnpm.repo.xunlei.cn/glob-parent/-/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "requires": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}, "dependencies": {"is-glob": {"version": "3.1.0", "resolved": "http://xnpm.repo.xunlei.cn/is-glob/-/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "requires": {"is-extglob": "^2.1.0"}}}}, "global-modules-path": {"version": "2.3.0", "resolved": "http://xnpm.repo.xunlei.cn/global-modules-path/-/global-modules-path-2.3.0.tgz", "integrity": "sha1-sOK6xr6sOXRffbXFnSajaguU99w=", "dev": true}, "globby": {"version": "6.1.0", "resolved": "http://xnpm.repo.xunlei.cn/globby/-/globby-6.1.0.tgz", "integrity": "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=", "dev": true, "requires": {"array-union": "^1.0.1", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "graceful-fs": {"version": "4.1.11", "resolved": "http://xnpm.repo.xunlei.cn/graceful-fs/-/graceful-fs-4.1.11.tgz", "integrity": "sha1-Dovf5NHduIVNZOBOp8AOKgJuVlg="}, "handle-thing": {"version": "1.2.5", "resolved": "http://xnpm.repo.xunlei.cn/handle-thing/-/handle-thing-1.2.5.tgz", "integrity": "sha1-/Xqtcmvxpf0W38KbL3pmAdJxOcQ=", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true}, "has-value": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/has-value/-/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "dev": true, "requires": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}}, "has-values": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/has-values/-/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "dev": true, "requires": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "dependencies": {"kind-of": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "hash-base": {"version": "3.0.4", "resolved": "http://xnpm.repo.xunlei.cn/hash-base/-/hash-base-3.0.4.tgz", "integrity": "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "hash.js": {"version": "1.1.5", "resolved": "http://xnpm.repo.xunlei.cn/hash.js/-/hash.js-1.1.5.tgz", "integrity": "sha1-44q0uF37HgxA/pJlwOm1SFTCOBI=", "dev": true, "requires": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "hmac-drbg": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/hmac-drbg/-/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "requires": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "hpack.js": {"version": "2.1.6", "resolved": "http://xnpm.repo.xunlei.cn/hpack.js/-/hpack.js-2.1.6.tgz", "integrity": "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=", "dev": true, "requires": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "html-entities": {"version": "1.2.1", "resolved": "http://xnpm.repo.xunlei.cn/html-entities/-/html-entities-1.2.1.tgz", "integrity": "sha1-DfKTUfByEWNRXfueVUPl9u7VFi8=", "dev": true}, "http-deceiver": {"version": "1.2.7", "resolved": "http://xnpm.repo.xunlei.cn/http-deceiver/-/http-deceiver-1.2.7.tgz", "integrity": "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=", "dev": true}, "http-errors": {"version": "1.6.3", "resolved": "http://xnpm.repo.xunlei.cn/http-errors/-/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "dev": true, "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "http-parser-js": {"version": "0.5.0", "resolved": "http://xnpm.repo.xunlei.cn/http-parser-js/-/http-parser-js-0.5.0.tgz", "integrity": "sha1-1l7b7ehDSdDcMDIIFaFdOcw8u9g=", "dev": true}, "http-proxy": {"version": "1.17.0", "resolved": "http://xnpm.repo.xunlei.cn/http-proxy/-/http-proxy-1.17.0.tgz", "integrity": "sha1-etOElGWPhGBeL220Q230EPTlvpo=", "dev": true, "requires": {"eventemitter3": "^3.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-proxy-middleware": {"version": "0.18.0", "resolved": "http://xnpm.repo.xunlei.cn/http-proxy-middleware/-/http-proxy-middleware-0.18.0.tgz", "integrity": "sha1-CYfmu1pWBuWmkWjY+WeofxXdiqs=", "dev": true, "requires": {"http-proxy": "^1.16.2", "is-glob": "^4.0.0", "lodash": "^4.17.5", "micromatch": "^3.1.9"}}, "https-browserify": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/https-browserify/-/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "dev": true}, "iconv-lite": {"version": "0.4.23", "resolved": "http://xnpm.repo.xunlei.cn/iconv-lite/-/iconv-lite-0.4.23.tgz", "integrity": "sha1-KXhx9jvlB63Pv8pxXQzQ7thOmmM=", "dev": true, "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ieee754": {"version": "1.1.12", "resolved": "http://xnpm.repo.xunlei.cn/ieee754/-/ieee754-1.1.12.tgz", "integrity": "sha1-UL8k5bnIu5ivSWTJQc2wkY2ntgs=", "dev": true}, "iferr": {"version": "0.1.5", "resolved": "http://xnpm.repo.xunlei.cn/iferr/-/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "dev": true}, "ignore": {"version": "3.3.10", "resolved": "http://xnpm.repo.xunlei.cn/ignore/-/ignore-3.3.10.tgz", "integrity": "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=", "dev": true}, "import-local": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/import-local/-/import-local-2.0.0.tgz", "integrity": "sha1-VQcL44pZk88Y72236WH1vuXFoJ0=", "dev": true, "requires": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "dependencies": {"find-up": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/find-up/-/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-limit/-/p-limit-2.0.0.tgz", "integrity": "sha1-5iTtVO6MRgp3izyfNnBJb/ileuw=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-try/-/p-try-2.0.0.tgz", "integrity": "sha1-hQgLuHxkaI+keZb+j3376CEXYLE=", "dev": true}, "pkg-dir": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/pkg-dir/-/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "dev": true, "requires": {"find-up": "^3.0.0"}}}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://xnpm.repo.xunlei.cn/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "indexof": {"version": "0.0.1", "resolved": "http://xnpm.repo.xunlei.cn/indexof/-/indexof-0.0.1.tgz", "integrity": "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10=", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "http://xnpm.repo.xunlei.cn/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.3", "resolved": "http://xnpm.repo.xunlei.cn/inherits/-/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "internal-ip": {"version": "3.0.1", "resolved": "http://xnpm.repo.xunlei.cn/internal-ip/-/internal-ip-3.0.1.tgz", "integrity": "sha1-31yZh24dLrLqLXT1IOP2aaAOzic=", "dev": true, "requires": {"default-gateway": "^2.6.0", "ipaddr.js": "^1.5.2"}}, "interpret": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/interpret/-/interpret-1.1.0.tgz", "integrity": "sha1-ftGxQQxqDg94z5XTuEQMY/eLhhQ=", "dev": true}, "invert-kv": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/invert-kv/-/invert-kv-2.0.0.tgz", "integrity": "sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI=", "dev": true}, "ip": {"version": "1.1.5", "resolved": "http://xnpm.repo.xunlei.cn/ip/-/ip-1.1.5.tgz", "integrity": "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=", "dev": true}, "ip-regex": {"version": "2.1.0", "resolved": "http://xnpm.repo.xunlei.cn/ip-regex/-/ip-regex-2.1.0.tgz", "integrity": "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=", "dev": true}, "ipaddr.js": {"version": "1.8.0", "resolved": "http://xnpm.repo.xunlei.cn/ipaddr.js/-/ipaddr.js-1.8.0.tgz", "integrity": "sha1-6qM9bd16zo9/b+DJygRA5wZzix4=", "dev": true}, "is-accessor-descriptor": {"version": "0.1.6", "resolved": "http://xnpm.repo.xunlei.cn/is-accessor-descriptor/-/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-binary-path": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/is-binary-path/-/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "requires": {"binary-extensions": "^1.0.0"}}, "is-buffer": {"version": "1.1.6", "resolved": "http://xnpm.repo.xunlei.cn/is-buffer/-/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true}, "is-data-descriptor": {"version": "0.1.4", "resolved": "http://xnpm.repo.xunlei.cn/is-data-descriptor/-/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-descriptor": {"version": "0.1.6", "resolved": "http://xnpm.repo.xunlei.cn/is-descriptor/-/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "requires": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "dependencies": {"kind-of": {"version": "5.1.0", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true}}}, "is-extendable": {"version": "0.1.1", "resolved": "http://xnpm.repo.xunlei.cn/is-extendable/-/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-fullwidth-code-point/-/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "requires": {"number-is-nan": "^1.0.0"}}, "is-glob": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-glob/-/is-glob-4.0.0.tgz", "integrity": "sha1-lSHHaEXMJhCoUgPd8ICpWML/q8A=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-number/-/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "is-path-cwd": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-path-cwd/-/is-path-cwd-1.0.0.tgz", "integrity": "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=", "dev": true}, "is-path-in-cwd": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/is-path-in-cwd/-/is-path-in-cwd-1.0.1.tgz", "integrity": "sha1-WsSLNF72dTOb1sekipEhELJBz1I=", "dev": true, "requires": {"is-path-inside": "^1.0.0"}}, "is-path-inside": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/is-path-inside/-/is-path-inside-1.0.1.tgz", "integrity": "sha1-jvW33lBDej/cprToZe96pVy0gDY=", "dev": true, "requires": {"path-is-inside": "^1.0.1"}}, "is-plain-object": {"version": "2.0.4", "resolved": "http://xnpm.repo.xunlei.cn/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "is-stream": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true}, "is-windows": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/is-windows/-/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "dev": true}, "is-wsl": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/is-wsl/-/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "dev": true}, "isarray": {"version": "0.0.1", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=", "dev": true}, "isexe": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "isobject": {"version": "3.0.1", "resolved": "http://xnpm.repo.xunlei.cn/isobject/-/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true}, "json-parse-better-errors": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "integrity": "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=", "dev": true}, "json3": {"version": "3.3.2", "resolved": "http://xnpm.repo.xunlei.cn/json3/-/json3-3.3.2.tgz", "integrity": "sha1-PAQ0dD35Pi9cQq7nsZvLSDV19OE=", "dev": true}, "json5": {"version": "0.5.1", "resolved": "http://xnpm.repo.xunlei.cn/json5/-/json5-0.5.1.tgz", "integrity": "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=", "dev": true}, "jsonfile": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/jsonfile/-/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "requires": {"graceful-fs": "^4.1.6"}}, "killable": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/killable/-/killable-1.0.1.tgz", "integrity": "sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=", "dev": true}, "kind-of": {"version": "6.0.2", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-6.0.2.tgz", "integrity": "sha1-ARRrNqYhjmTljzqNZt5df8b20FE=", "dev": true}, "lcid": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/lcid/-/lcid-2.0.0.tgz", "integrity": "sha1-bvXS32DlL4LrIopMNz6NHzlyU88=", "dev": true, "requires": {"invert-kv": "^2.0.0"}}, "loader-runner": {"version": "2.3.1", "resolved": "http://xnpm.repo.xunlei.cn/loader-runner/-/loader-runner-2.3.1.tgz", "integrity": "sha1-Am8S/nwxFZkolqwCugIrqSlxuXk=", "dev": true}, "loader-utils": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/loader-utils/-/loader-utils-1.1.0.tgz", "integrity": "sha1-yYrvSIvM7aL/teLeZG1qdUQp9c0=", "dev": true, "requires": {"big.js": "^3.1.3", "emojis-list": "^2.0.0", "json5": "^0.5.0"}}, "locate-path": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/locate-path/-/locate-path-2.0.0.tgz", "integrity": "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=", "dev": true, "requires": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}}, "lodash": {"version": "4.17.11", "resolved": "http://xnpm.repo.xunlei.cn/lodash/-/lodash-4.17.11.tgz", "integrity": "sha1-s56mIp72B+zYniyN8SU2iRysm40="}, "lodash.debounce": {"version": "4.0.8", "resolved": "http://xnpm.repo.xunlei.cn/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "integrity": "sha1-gteb/zCmfEAF/9XiUVMArZyk168=", "dev": true}, "log4js": {"version": "4.3.1", "resolved": "http://xnpm.repo.xunlei.cn/log4js/-/log4js-4.3.1.tgz", "integrity": "sha1-Amy2+zzVucRoKpZHjDVsl7SXaG4=", "requires": {"date-format": "^2.0.0", "debug": "^4.1.1", "flatted": "^2.0.0", "rfdc": "^1.1.2", "streamroller": "^1.0.5"}, "dependencies": {"debug": {"version": "4.1.1", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-4.1.1.tgz", "integrity": "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=", "requires": {"ms": "^2.1.1"}}}}, "loglevel": {"version": "1.6.1", "resolved": "http://xnpm.repo.xunlei.cn/loglevel/-/loglevel-1.6.1.tgz", "integrity": "sha1-4PyVEztu8nbNyIh82vJKpvFW+Po=", "dev": true}, "lru-cache": {"version": "4.1.3", "resolved": "http://xnpm.repo.xunlei.cn/lru-cache/-/lru-cache-4.1.3.tgz", "integrity": "sha1-oRdc80lt/IQ2wVbDNLSVWZK85pw=", "dev": true, "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "make-dir": {"version": "1.3.0", "resolved": "http://xnpm.repo.xunlei.cn/make-dir/-/make-dir-1.3.0.tgz", "integrity": "sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=", "dev": true, "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "map-age-cleaner": {"version": "0.1.2", "resolved": "http://xnpm.repo.xunlei.cn/map-age-cleaner/-/map-age-cleaner-0.1.2.tgz", "integrity": "sha1-CY+xVTj9Pb5GHxJ0WwyoVo1OP3Q=", "dev": true, "requires": {"p-defer": "^1.0.0"}}, "map-cache": {"version": "0.2.2", "resolved": "http://xnpm.repo.xunlei.cn/map-cache/-/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true}, "map-visit": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/map-visit/-/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "dev": true, "requires": {"object-visit": "^1.0.0"}}, "md5.js": {"version": "1.3.5", "resolved": "http://xnpm.repo.xunlei.cn/md5.js/-/md5.js-1.3.5.tgz", "integrity": "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "media-typer": {"version": "0.3.0", "resolved": "http://xnpm.repo.xunlei.cn/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "dev": true}, "mem": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/mem/-/mem-4.0.0.tgz", "integrity": "sha1-ZDdpDZRxZ49syDZZwAy6/Nawza8=", "dev": true, "requires": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^1.0.0", "p-is-promise": "^1.1.0"}}, "memory-fs": {"version": "0.4.1", "resolved": "http://xnpm.repo.xunlei.cn/memory-fs/-/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "dev": true, "requires": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "merge-descriptors": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=", "dev": true}, "methods": {"version": "1.1.2", "resolved": "http://xnpm.repo.xunlei.cn/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "dev": true}, "micromatch": {"version": "3.1.10", "resolved": "http://xnpm.repo.xunlei.cn/micromatch/-/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}}, "miller-rabin": {"version": "4.0.1", "resolved": "http://xnpm.repo.xunlei.cn/miller-rabin/-/miller-rabin-4.0.1.tgz", "integrity": "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=", "dev": true, "requires": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}}, "mime": {"version": "1.4.1", "resolved": "http://xnpm.repo.xunlei.cn/mime/-/mime-1.4.1.tgz", "integrity": "sha1-Eh+evEnjdm8xGnbh+hyAA8SwOqY=", "dev": true}, "mime-db": {"version": "1.37.0", "resolved": "http://xnpm.repo.xunlei.cn/mime-db/-/mime-db-1.37.0.tgz", "integrity": "sha1-C2oM5v2+lXbiXx8tL96IMNwK0Ng=", "dev": true}, "mime-types": {"version": "2.1.21", "resolved": "http://xnpm.repo.xunlei.cn/mime-types/-/mime-types-2.1.21.tgz", "integrity": "sha1-K<PERSON>laoey3cHQv5q5+WPkYHHRLP5Y=", "dev": true, "requires": {"mime-db": "~1.37.0"}}, "mimic-fn": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/mimic-fn/-/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=", "dev": true}, "minimalistic-assert": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=", "dev": true}, "minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/minimalistic-crypto-utils/-/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true}, "minimatch": {"version": "3.0.4", "resolved": "http://xnpm.repo.xunlei.cn/minimatch/-/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "mississippi": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/mississippi/-/mississippi-2.0.0.tgz", "integrity": "sha1-NEKlCPr8KFAEhv7qmUCWduTuWm8=", "dev": true, "requires": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^2.0.1", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}, "through2": {"version": "2.0.3", "resolved": "http://xnpm.repo.xunlei.cn/through2/-/through2-2.0.3.tgz", "integrity": "sha1-AARWmzfHx0ujnEPzzteNGtlBQL4=", "dev": true, "requires": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}}, "xtend": {"version": "4.0.1", "resolved": "http://xnpm.repo.xunlei.cn/xtend/-/xtend-4.0.1.tgz", "integrity": "sha1-pcbVMr5lbiPbgg77lDofBJmNY68=", "dev": true}}}, "mixin-deep": {"version": "1.3.1", "resolved": "http://xnpm.repo.xunlei.cn/mixin-deep/-/mixin-deep-1.3.1.tgz", "integrity": "sha1-pJ5yaNzhoNlpjkUybFYm3zVD0P4=", "dev": true, "requires": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "dependencies": {"is-extendable": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/is-extendable/-/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "requires": {"is-plain-object": "^2.0.4"}}}}, "mkdirp": {"version": "0.5.1", "resolved": "http://xnpm.repo.xunlei.cn/mkdirp/-/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "requires": {"minimist": "0.0.8"}, "dependencies": {"minimist": {"version": "0.0.8", "resolved": "http://xnpm.repo.xunlei.cn/minimist/-/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0="}}}, "move-concurrently": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/move-concurrently/-/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "dev": true, "requires": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "ms": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="}, "multicast-dns": {"version": "6.2.3", "resolved": "http://xnpm.repo.xunlei.cn/multicast-dns/-/multicast-dns-6.2.3.tgz", "integrity": "sha1-oOx72QVcQoL3kMPIL04o2zsxsik=", "dev": true, "requires": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}}, "multicast-dns-service-types": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/multicast-dns-service-types/-/multicast-dns-service-types-1.1.0.tgz", "integrity": "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=", "dev": true}, "nanomatch": {"version": "1.2.13", "resolved": "http://xnpm.repo.xunlei.cn/nanomatch/-/nanomatch-1.2.13.tgz", "integrity": "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=", "dev": true, "requires": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}}, "negotiator": {"version": "0.6.1", "resolved": "http://xnpm.repo.xunlei.cn/negotiator/-/negotiator-0.6.1.tgz", "integrity": "sha1-KzJxhOiZIQEXeyhWP7XnECrNDKk=", "dev": true}, "neo-async": {"version": "2.6.0", "resolved": "http://xnpm.repo.xunlei.cn/neo-async/-/neo-async-2.6.0.tgz", "integrity": "sha1-udFeTXHGdikIZUtRg+04t1M0CDU=", "dev": true}, "nice-try": {"version": "1.0.5", "resolved": "http://xnpm.repo.xunlei.cn/nice-try/-/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true}, "node-forge": {"version": "0.7.5", "resolved": "http://xnpm.repo.xunlei.cn/node-forge/-/node-forge-0.7.5.tgz", "integrity": "sha1-bBUsNFzhHFL0ZcKr2VfoY5zWdN8=", "dev": true}, "node-libs-browser": {"version": "2.1.0", "resolved": "http://xnpm.repo.xunlei.cn/node-libs-browser/-/node-libs-browser-2.1.0.tgz", "integrity": "sha1-X5QmPUBPbkR2fXJpAf/wVHjWAN8=", "dev": true, "requires": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^1.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.0", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.10.3", "vm-browserify": "0.0.4"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "node-loader": {"version": "0.6.0", "resolved": "http://xnpm.repo.xunlei.cn/node-loader/-/node-loader-0.6.0.tgz", "integrity": "sha1-x5fvUQle1YWZArFX9jhPY2HgWug=", "dev": true}, "normalize-path": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "^1.0.1"}}, "npm-run-path": {"version": "2.0.2", "resolved": "http://xnpm.repo.xunlei.cn/npm-run-path/-/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dev": true, "requires": {"path-key": "^2.0.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/number-is-nan/-/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "dev": true}, "object-assign": {"version": "4.1.1", "resolved": "http://xnpm.repo.xunlei.cn/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true}, "object-copy": {"version": "0.1.0", "resolved": "http://xnpm.repo.xunlei.cn/object-copy/-/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "dev": true, "requires": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "kind-of": {"version": "3.2.2", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "object-visit": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/object-visit/-/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "dev": true, "requires": {"isobject": "^3.0.0"}}, "object.pick": {"version": "1.3.0", "resolved": "http://xnpm.repo.xunlei.cn/object.pick/-/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "requires": {"isobject": "^3.0.1"}}, "obuf": {"version": "1.1.2", "resolved": "http://xnpm.repo.xunlei.cn/obuf/-/obuf-1.1.2.tgz", "integrity": "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=", "dev": true}, "on-finished": {"version": "2.3.0", "resolved": "http://xnpm.repo.xunlei.cn/on-finished/-/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "dev": true, "requires": {"ee-first": "1.1.1"}}, "on-headers": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/on-headers/-/on-headers-1.0.1.tgz", "integrity": "sha1-ko9dD0cNSTQmUepnlLCFfBAGk/c=", "dev": true}, "once": {"version": "1.4.0", "resolved": "http://xnpm.repo.xunlei.cn/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "opn": {"version": "5.4.0", "resolved": "http://xnpm.repo.xunlei.cn/opn/-/opn-5.4.0.tgz", "integrity": "sha1-y1Reeqt4VivrEao7+rxwQuF2EDU=", "dev": true, "requires": {"is-wsl": "^1.1.0"}}, "original": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/original/-/original-1.0.2.tgz", "integrity": "sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=", "dev": true, "requires": {"url-parse": "^1.4.3"}}, "os-browserify": {"version": "0.3.0", "resolved": "http://xnpm.repo.xunlei.cn/os-browserify/-/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "dev": true}, "os-locale": {"version": "3.0.1", "resolved": "http://xnpm.repo.xunlei.cn/os-locale/-/os-locale-3.0.1.tgz", "integrity": "sha1-OwFPvwHYf2Ch5TSNgP6HDcgsRiA=", "dev": true, "requires": {"execa": "^0.10.0", "lcid": "^2.0.0", "mem": "^4.0.0"}}, "p-defer": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-defer/-/p-defer-1.0.0.tgz", "integrity": "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=", "dev": true}, "p-finally": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-finally/-/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "dev": true}, "p-is-promise": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/p-is-promise/-/p-is-promise-1.1.0.tgz", "integrity": "sha1-nJRWmJ6fZYgBewQ01WCXZ1w9oF4=", "dev": true}, "p-limit": {"version": "1.3.0", "resolved": "http://xnpm.repo.xunlei.cn/p-limit/-/p-limit-1.3.0.tgz", "integrity": "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=", "dev": true, "requires": {"p-try": "^1.0.0"}}, "p-locate": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-locate/-/p-locate-2.0.0.tgz", "integrity": "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=", "dev": true, "requires": {"p-limit": "^1.1.0"}}, "p-map": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/p-map/-/p-map-1.2.0.tgz", "integrity": "sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s=", "dev": true}, "p-try": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-try/-/p-try-1.0.0.tgz", "integrity": "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=", "dev": true}, "pako": {"version": "1.0.6", "resolved": "http://xnpm.repo.xunlei.cn/pako/-/pako-1.0.6.tgz", "integrity": "sha1-AQEhG6pwxLykoPY/Igbpe3368lg=", "dev": true}, "parallel-transform": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/parallel-transform/-/parallel-transform-1.1.0.tgz", "integrity": "sha1-1BDwZbBdojCB/NEPKIVMKb2jOwY=", "dev": true, "requires": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "parse-asn1": {"version": "5.1.1", "resolved": "http://xnpm.repo.xunlei.cn/parse-asn1/-/parse-asn1-5.1.1.tgz", "integrity": "sha1-9r8pOBgzK9DatU77Fgh3JHRebKg=", "dev": true, "requires": {"asn1.js": "^4.0.0", "browserify-aes": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3"}}, "parseurl": {"version": "1.3.2", "resolved": "http://xnpm.repo.xunlei.cn/parseurl/-/parseurl-1.3.2.tgz", "integrity": "sha1-/CidTtiZMRlGDBViUyYs3I3mW/M=", "dev": true}, "pascalcase": {"version": "0.1.1", "resolved": "http://xnpm.repo.xunlei.cn/pascalcase/-/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "dev": true}, "path-browserify": {"version": "0.0.0", "resolved": "http://xnpm.repo.xunlei.cn/path-browserify/-/path-browserify-0.0.0.tgz", "integrity": "sha1-oLhwcpquIUAFt9UDLsLLuw+0RRo=", "dev": true}, "path-dirname": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/path-dirname/-/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "dev": true}, "path-exists": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/path-exists/-/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true}, "path-is-inside": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/path-is-inside/-/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "path-key": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/path-key/-/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true}, "path-to-regexp": {"version": "0.1.7", "resolved": "http://xnpm.repo.xunlei.cn/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=", "dev": true}, "path-type": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/path-type/-/path-type-3.0.0.tgz", "integrity": "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=", "dev": true, "requires": {"pify": "^3.0.0"}, "dependencies": {"pify": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/pify/-/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true}}}, "pbkdf2": {"version": "3.0.17", "resolved": "http://xnpm.repo.xunlei.cn/pbkdf2/-/pbkdf2-3.0.17.tgz", "integrity": "sha1-l2wgZTBhexTrsyEUI597CTNuk6Y=", "dev": true, "requires": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "pend": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/pend/-/pend-1.2.0.tgz", "integrity": "sha1-elfrVQpng/kRUzH89GY9XI4AelA="}, "pify": {"version": "2.3.0", "resolved": "http://xnpm.repo.xunlei.cn/pify/-/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}, "pinkie": {"version": "2.0.4", "resolved": "http://xnpm.repo.xunlei.cn/pinkie/-/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true}, "pinkie-promise": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/pinkie-promise/-/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "requires": {"pinkie": "^2.0.0"}}, "pkg-dir": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/pkg-dir/-/pkg-dir-2.0.0.tgz", "integrity": "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=", "dev": true, "requires": {"find-up": "^2.1.0"}, "dependencies": {"find-up": {"version": "2.1.0", "resolved": "http://xnpm.repo.xunlei.cn/find-up/-/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "requires": {"locate-path": "^2.0.0"}}}}, "portfinder": {"version": "1.0.19", "resolved": "http://xnpm.repo.xunlei.cn/portfinder/-/portfinder-1.0.19.tgz", "integrity": "sha1-B+h5FKVSQtzaW4M9QvAY1odbWV8=", "dev": true, "requires": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "dependencies": {"async": {"version": "1.5.2", "resolved": "http://xnpm.repo.xunlei.cn/async/-/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=", "dev": true}, "debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "posix-character-classes": {"version": "0.1.1", "resolved": "http://xnpm.repo.xunlei.cn/posix-character-classes/-/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true}, "process": {"version": "0.11.10", "resolved": "http://xnpm.repo.xunlei.cn/process/-/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "dev": true}, "process-nextick-args": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/process-nextick-args/-/process-nextick-args-2.0.0.tgz", "integrity": "sha1-o31zL0JxtKsa0HDTVQjoKQeI/6o="}, "promise-inflight": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/promise-inflight/-/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true}, "proxy-addr": {"version": "2.0.4", "resolved": "http://xnpm.repo.xunlei.cn/proxy-addr/-/proxy-addr-2.0.4.tgz", "integrity": "sha1-7PxzO/Iv+Mb0B/onUye5q2fki5M=", "dev": true, "requires": {"forwarded": "~0.1.2", "ipaddr.js": "1.8.0"}}, "prr": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/prr/-/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "dev": true}, "pseudomap": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/pseudomap/-/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM=", "dev": true}, "public-encrypt": {"version": "4.0.3", "resolved": "http://xnpm.repo.xunlei.cn/public-encrypt/-/public-encrypt-4.0.3.tgz", "integrity": "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=", "dev": true, "requires": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}}, "pump": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/pump/-/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "pumpify": {"version": "1.5.1", "resolved": "http://xnpm.repo.xunlei.cn/pumpify/-/pumpify-1.5.1.tgz", "integrity": "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=", "dev": true, "requires": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "punycode": {"version": "1.4.1", "resolved": "http://xnpm.repo.xunlei.cn/punycode/-/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}, "qs": {"version": "6.5.2", "resolved": "http://xnpm.repo.xunlei.cn/qs/-/qs-6.5.2.tgz", "integrity": "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=", "dev": true}, "querystring": {"version": "0.2.0", "resolved": "http://xnpm.repo.xunlei.cn/querystring/-/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=", "dev": true}, "querystring-es3": {"version": "0.2.1", "resolved": "http://xnpm.repo.xunlei.cn/querystring-es3/-/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=", "dev": true}, "querystringify": {"version": "2.1.0", "resolved": "http://xnpm.repo.xunlei.cn/querystringify/-/querystringify-2.1.0.tgz", "integrity": "sha1-fe2N+/eHncxg0KZErGdUsoOtF+8=", "dev": true}, "randombytes": {"version": "2.0.6", "resolved": "http://xnpm.repo.xunlei.cn/randombytes/-/randombytes-2.0.6.tgz", "integrity": "sha1-0wLFIpSFiISKjTAMkytEwkIx2oA=", "dev": true, "requires": {"safe-buffer": "^5.1.0"}}, "randomfill": {"version": "1.0.4", "resolved": "http://xnpm.repo.xunlei.cn/randomfill/-/randomfill-1.0.4.tgz", "integrity": "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=", "dev": true, "requires": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "range-parser": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/range-parser/-/range-parser-1.2.0.tgz", "integrity": "sha1-9JvmtIeJTdxA3MlKMi9hEJLgDV4=", "dev": true}, "raw-body": {"version": "2.3.3", "resolved": "http://xnpm.repo.xunlei.cn/raw-body/-/raw-body-2.3.3.tgz", "integrity": "sha1-GzJOzmtXBuFThVvBFIxlu39uoMM=", "dev": true, "requires": {"bytes": "3.0.0", "http-errors": "1.6.3", "iconv-lite": "0.4.23", "unpipe": "1.0.0"}}, "readable-stream": {"version": "1.1.14", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "readdirp": {"version": "2.2.1", "resolved": "http://xnpm.repo.xunlei.cn/readdirp/-/readdirp-2.2.1.tgz", "integrity": "sha1-DodiKjMlqjPokihcr4tOhGUppSU=", "dev": true, "requires": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "regex-not": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/regex-not/-/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "dev": true, "requires": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "repeat-element": {"version": "1.1.3", "resolved": "http://xnpm.repo.xunlei.cn/repeat-element/-/repeat-element-1.1.3.tgz", "integrity": "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=", "dev": true}, "repeat-string": {"version": "1.6.1", "resolved": "http://xnpm.repo.xunlei.cn/repeat-string/-/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true}, "require-directory": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true}, "require-main-filename": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/require-main-filename/-/require-main-filename-1.0.1.tgz", "integrity": "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=", "dev": true}, "requires-port": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "dev": true}, "resolve-cwd": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/resolve-cwd/-/resolve-cwd-2.0.0.tgz", "integrity": "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=", "dev": true, "requires": {"resolve-from": "^3.0.0"}}, "resolve-from": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/resolve-from/-/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true}, "resolve-url": {"version": "0.2.1", "resolved": "http://xnpm.repo.xunlei.cn/resolve-url/-/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "dev": true}, "ret": {"version": "0.1.15", "resolved": "http://xnpm.repo.xunlei.cn/ret/-/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=", "dev": true}, "rfdc": {"version": "1.1.4", "resolved": "http://xnpm.repo.xunlei.cn/rfdc/-/rfdc-1.1.4.tgz", "integrity": "sha1-unLME2egzNnPgahws7WL060H+MI="}, "rimraf": {"version": "2.6.2", "resolved": "http://xnpm.repo.xunlei.cn/rimraf/-/rimraf-2.6.2.tgz", "integrity": "sha1-LtgVDSShbqhlHm1u8PR8QVjOejY=", "dev": true, "requires": {"glob": "^7.0.5"}}, "ripemd160": {"version": "2.0.2", "resolved": "http://xnpm.repo.xunlei.cn/ripemd160/-/ripemd160-2.0.2.tgz", "integrity": "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=", "dev": true, "requires": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "run-queue": {"version": "1.0.3", "resolved": "http://xnpm.repo.xunlei.cn/run-queue/-/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "dev": true, "requires": {"aproba": "^1.1.1"}}, "safe-buffer": {"version": "5.1.2", "resolved": "http://xnpm.repo.xunlei.cn/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "safe-regex": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/safe-regex/-/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "requires": {"ret": "~0.1.10"}}, "safer-buffer": {"version": "2.1.2", "resolved": "http://xnpm.repo.xunlei.cn/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "schema-utils": {"version": "0.4.7", "resolved": "http://xnpm.repo.xunlei.cn/schema-utils/-/schema-utils-0.4.7.tgz", "integrity": "sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "dependencies": {"ajv": {"version": "6.5.4", "resolved": "http://xnpm.repo.xunlei.cn/ajv/-/ajv-6.5.4.tgz", "integrity": "sha1-JH1SdBENtlNwa1UPzCt5fKKM/Fk=", "dev": true, "requires": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "fast-deep-equal": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://xnpm.repo.xunlei.cn/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true}}}, "select-hose": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/select-hose/-/select-hose-2.0.0.tgz", "integrity": "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=", "dev": true}, "selfsigned": {"version": "1.10.4", "resolved": "http://xnpm.repo.xunlei.cn/selfsigned/-/selfsigned-1.10.4.tgz", "integrity": "sha1-zdfsz8pO12NdR6CL8tXTB0CS4s0=", "dev": true, "requires": {"node-forge": "0.7.5"}}, "semver": {"version": "5.6.0", "resolved": "http://xnpm.repo.xunlei.cn/semver/-/semver-5.6.0.tgz", "integrity": "sha1-fnQlb7qknHWqfHogXMInmcrIAAQ=", "dev": true}, "send": {"version": "0.16.2", "resolved": "http://xnpm.repo.xunlei.cn/send/-/send-0.16.2.tgz", "integrity": "sha1-bsyh4PjBVtFBWXVZhI32RzCmu8E=", "dev": true, "requires": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.6.2", "mime": "1.4.1", "ms": "2.0.0", "on-finished": "~2.3.0", "range-parser": "~1.2.0", "statuses": "~1.4.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "serialize-javascript": {"version": "1.5.0", "resolved": "http://xnpm.repo.xunlei.cn/serialize-javascript/-/serialize-javascript-1.5.0.tgz", "integrity": "sha1-GqM2FiyIqJDdrVOEuuvJOmVRYf4=", "dev": true}, "serve-index": {"version": "1.9.1", "resolved": "http://xnpm.repo.xunlei.cn/serve-index/-/serve-index-1.9.1.tgz", "integrity": "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=", "dev": true, "requires": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "serve-static": {"version": "1.13.2", "resolved": "http://xnpm.repo.xunlei.cn/serve-static/-/serve-static-1.13.2.tgz", "integrity": "sha1-CV6Ecv1bRiN9tQzkhqQ/S4bGzsE=", "dev": true, "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.2", "send": "0.16.2"}}, "set-blocking": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/set-blocking/-/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true}, "set-value": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/set-value/-/set-value-2.0.0.tgz", "integrity": "sha1-ca5KiPD+77v1LR6mBPP7MV67YnQ=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}}}, "setimmediate": {"version": "1.0.5", "resolved": "http://xnpm.repo.xunlei.cn/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "dev": true}, "setprototypeof": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/setprototypeof/-/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=", "dev": true}, "sha.js": {"version": "2.4.11", "resolved": "http://xnpm.repo.xunlei.cn/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=", "dev": true, "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "shebang-command": {"version": "1.2.0", "resolved": "http://xnpm.repo.xunlei.cn/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true}, "signal-exit": {"version": "3.0.2", "resolved": "http://xnpm.repo.xunlei.cn/signal-exit/-/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=", "dev": true}, "slash": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/slash/-/slash-1.0.0.tgz", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=", "dev": true}, "snapdragon": {"version": "0.8.2", "resolved": "http://xnpm.repo.xunlei.cn/snapdragon/-/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "dev": true, "requires": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "define-property": {"version": "0.2.5", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}, "extend-shallow": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "snapdragon-node": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/snapdragon-node/-/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "dev": true, "requires": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "dependencies": {"define-property": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "requires": {"is-descriptor": "^1.0.0"}}, "is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-accessor-descriptor/-/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-data-descriptor": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-data-descriptor/-/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "requires": {"kind-of": "^6.0.0"}}, "is-descriptor": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/is-descriptor/-/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "requires": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}}}}, "snapdragon-util": {"version": "3.0.1", "resolved": "http://xnpm.repo.xunlei.cn/snapdragon-util/-/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "dev": true, "requires": {"kind-of": "^3.2.0"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "sockjs": {"version": "0.3.19", "resolved": "http://xnpm.repo.xunlei.cn/sockjs/-/sockjs-0.3.19.tgz", "integrity": "sha1-2Xa76ACve9IK4IWY1YI5NQiZPA0=", "dev": true, "requires": {"faye-websocket": "^0.10.0", "uuid": "^3.0.1"}}, "sockjs-client": {"version": "1.3.0", "resolved": "http://xnpm.repo.xunlei.cn/sockjs-client/-/sockjs-client-1.3.0.tgz", "integrity": "sha1-EvydbLZj2lc509xftuhofalcsXc=", "dev": true, "requires": {"debug": "^3.2.5", "eventsource": "^1.0.7", "faye-websocket": "~0.11.1", "inherits": "^2.0.3", "json3": "^3.3.2", "url-parse": "^1.4.3"}, "dependencies": {"faye-websocket": {"version": "0.11.1", "resolved": "http://xnpm.repo.xunlei.cn/faye-websocket/-/faye-websocket-0.11.1.tgz", "integrity": "sha1-8O/hjE9W5PQK/H4Gxxn9XuYYjzg=", "dev": true, "requires": {"websocket-driver": ">=0.5.1"}}}}, "source-list-map": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/source-list-map/-/source-list-map-2.0.1.tgz", "integrity": "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=", "dev": true}, "source-map": {"version": "0.5.7", "resolved": "http://xnpm.repo.xunlei.cn/source-map/-/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true}, "source-map-loader": {"version": "0.2.4", "resolved": "http://xnpm.repo.xunlei.cn/source-map-loader/-/source-map-loader-0.2.4.tgz", "integrity": "sha1-wYsNxuI79m9nkkN1V8VpoR4HInE=", "dev": true, "requires": {"async": "^2.5.0", "loader-utils": "^1.1.0"}}, "source-map-resolve": {"version": "0.5.2", "resolved": "http://xnpm.repo.xunlei.cn/source-map-resolve/-/source-map-resolve-0.5.2.tgz", "integrity": "sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk=", "dev": true, "requires": {"atob": "^2.1.1", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "source-map-url": {"version": "0.4.0", "resolved": "http://xnpm.repo.xunlei.cn/source-map-url/-/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=", "dev": true}, "spdy": {"version": "3.4.7", "resolved": "http://xnpm.repo.xunlei.cn/spdy/-/spdy-3.4.7.tgz", "integrity": "sha1-Qv9B7OXMD5mjpsKKq7c/XDsDrLw=", "dev": true, "requires": {"debug": "^2.6.8", "handle-thing": "^1.2.5", "http-deceiver": "^1.2.7", "safe-buffer": "^5.0.1", "select-hose": "^2.0.0", "spdy-transport": "^2.0.18"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}}}, "spdy-transport": {"version": "2.1.0", "resolved": "http://xnpm.repo.xunlei.cn/spdy-transport/-/spdy-transport-2.1.0.tgz", "integrity": "sha1-S7sVqv/tC+791WrWHb3Iuj4st6E=", "dev": true, "requires": {"debug": "^2.6.8", "detect-node": "^2.0.3", "hpack.js": "^2.1.6", "obuf": "^1.1.1", "readable-stream": "^2.2.9", "safe-buffer": "^5.0.1", "wbuf": "^1.7.2"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "http://xnpm.repo.xunlei.cn/debug/-/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "requires": {"ms": "2.0.0"}}, "isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "ms": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "split-string": {"version": "3.1.0", "resolved": "http://xnpm.repo.xunlei.cn/split-string/-/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "dev": true, "requires": {"extend-shallow": "^3.0.0"}}, "ssri": {"version": "5.3.0", "resolved": "http://xnpm.repo.xunlei.cn/ssri/-/ssri-5.3.0.tgz", "integrity": "sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY=", "dev": true, "requires": {"safe-buffer": "^5.1.1"}}, "static-extend": {"version": "0.1.2", "resolved": "http://xnpm.repo.xunlei.cn/static-extend/-/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "dev": true, "requires": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "dependencies": {"define-property": {"version": "0.2.5", "resolved": "http://xnpm.repo.xunlei.cn/define-property/-/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "requires": {"is-descriptor": "^0.1.0"}}}}, "statuses": {"version": "1.4.0", "resolved": "http://xnpm.repo.xunlei.cn/statuses/-/statuses-1.4.0.tgz", "integrity": "sha1-u3PURtonlhBu/MG2AaJT1sRr0Ic=", "dev": true}, "stream-browserify": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/stream-browserify/-/stream-browserify-2.0.1.tgz", "integrity": "sha1-ZiZu5fm9uZQKTkUUyvtDu3Hlyds=", "dev": true, "requires": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}}}, "stream-each": {"version": "1.2.3", "resolved": "http://xnpm.repo.xunlei.cn/stream-each/-/stream-each-1.2.3.tgz", "integrity": "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "stream-http": {"version": "2.8.3", "resolved": "http://xnpm.repo.xunlei.cn/stream-http/-/stream-http-2.8.3.tgz", "integrity": "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=", "dev": true, "requires": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "readable-stream": {"version": "2.3.6", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "requires": {"safe-buffer": "~5.1.0"}}, "xtend": {"version": "4.0.1", "resolved": "http://xnpm.repo.xunlei.cn/xtend/-/xtend-4.0.1.tgz", "integrity": "sha1-pcbVMr5lbiPbgg77lDofBJmNY68=", "dev": true}}}, "stream-shift": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/stream-shift/-/stream-shift-1.0.0.tgz", "integrity": "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI=", "dev": true}, "streamifier": {"version": "0.1.1", "resolved": "http://xnpm.repo.xunlei.cn/streamifier/-/streamifier-0.1.1.tgz", "integrity": "sha1-l+mNj6TRBdYqJpHR3AfoINuN/E8="}, "streamroller": {"version": "1.0.5", "resolved": "http://xnpm.repo.xunlei.cn/streamroller/-/streamroller-1.0.5.tgz", "integrity": "sha1-cWYMILBrGnsgTUYIVzGtE8EKVi0=", "requires": {"async": "^2.6.2", "date-format": "^2.0.0", "debug": "^3.2.6", "fs-extra": "^7.0.1", "lodash": "^4.17.11"}, "dependencies": {"async": {"version": "2.6.2", "resolved": "http://xnpm.repo.xunlei.cn/async/-/async-2.6.2.tgz", "integrity": "sha1-GDMOp+bjE4h/XS8qkEusb+TdU4E=", "requires": {"lodash": "^4.17.11"}}}}, "string-width": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/string-width/-/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "string_decoder": {"version": "0.10.31", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=", "dev": true}, "strip-ansi": {"version": "3.0.1", "resolved": "http://xnpm.repo.xunlei.cn/strip-ansi/-/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "requires": {"ansi-regex": "^2.0.0"}}, "strip-eof": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/strip-eof/-/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "http://xnpm.repo.xunlei.cn/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "requires": {"has-flag": "^3.0.0"}}, "tapable": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/tapable/-/tapable-1.1.0.tgz", "integrity": "sha1-DQdqFy49m6CI/SJysmaPuNGUt4w=", "dev": true}, "tar-stream": {"version": "1.6.2", "resolved": "http://xnpm.repo.xunlei.cn/tar-stream/-/tar-stream-1.6.2.tgz", "integrity": "sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU=", "requires": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "readable-stream": {"version": "2.3.7", "resolved": "http://xnpm.repo.xunlei.cn/readable-stream/-/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "string_decoder": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}}}, "thunky": {"version": "1.0.3", "resolved": "http://xnpm.repo.xunlei.cn/thunky/-/thunky-1.0.3.tgz", "integrity": "sha1-9d9zJFNAewkZHa5z4qjMc/OBqCY=", "dev": true}, "timers-browserify": {"version": "2.0.10", "resolved": "http://xnpm.repo.xunlei.cn/timers-browserify/-/timers-browserify-2.0.10.tgz", "integrity": "sha1-HSjj0qrfHVpZlsTp+VYBzQU0gK4=", "dev": true, "requires": {"setimmediate": "^1.0.4"}}, "to-arraybuffer": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/to-arraybuffer/-/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "dev": true}, "to-buffer": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/to-buffer/-/to-buffer-1.1.1.tgz", "integrity": "sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA="}, "to-object-path": {"version": "0.3.0", "resolved": "http://xnpm.repo.xunlei.cn/to-object-path/-/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "dev": true, "requires": {"kind-of": "^3.0.2"}, "dependencies": {"kind-of": {"version": "3.2.2", "resolved": "http://xnpm.repo.xunlei.cn/kind-of/-/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "requires": {"is-buffer": "^1.1.5"}}}}, "to-regex": {"version": "3.0.2", "resolved": "http://xnpm.repo.xunlei.cn/to-regex/-/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "dev": true, "requires": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}}, "to-regex-range": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/to-regex-range/-/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "requires": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}}, "ts-loader": {"version": "4.5.0", "resolved": "http://xnpm.repo.xunlei.cn/ts-loader/-/ts-loader-4.5.0.tgz", "integrity": "sha1-oc5wstx5mUH7IZdgXw1nh0CXhZs=", "dev": true, "requires": {"chalk": "^2.3.0", "enhanced-resolve": "^4.0.0", "loader-utils": "^1.0.2", "micromatch": "^3.1.4", "semver": "^5.0.1"}}, "tslib": {"version": "1.9.3", "resolved": "http://xnpm.repo.xunlei.cn/tslib/-/tslib-1.9.3.tgz", "integrity": "sha1-1+TdeSRdhUKMTX5IIqeZF5VMooY=", "dev": true}, "tty-browserify": {"version": "0.0.0", "resolved": "http://xnpm.repo.xunlei.cn/tty-browserify/-/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "dev": true}, "type-is": {"version": "1.6.16", "resolved": "http://xnpm.repo.xunlei.cn/type-is/-/type-is-1.6.16.tgz", "integrity": "sha1-+JzjQVQcZysl7nrjxz3uOyvlAZQ=", "dev": true, "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.18"}}, "typedarray": {"version": "0.0.6", "resolved": "http://xnpm.repo.xunlei.cn/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "typescript": {"version": "4.5.4", "resolved": "http://xnpm.repo.xunlei.cn/typescript/-/typescript-4.5.4.tgz", "integrity": "sha512-VgYs2A2QIRuGphtzFV7aQJduJ2gyfTljngLzjpfW9FoYZF6xuw1W0vW9ghCKLfcWrCFxK81CSGRAvS1pn4fIUg==", "dev": true}, "uglify-es": {"version": "3.3.9", "resolved": "http://xnpm.repo.xunlei.cn/uglify-es/-/uglify-es-3.3.9.tgz", "integrity": "sha1-DBxPBwC+2NvBJM2zBNJZLKID5nc=", "dev": true, "requires": {"commander": "~2.13.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://xnpm.repo.xunlei.cn/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "uglifyjs-webpack-plugin": {"version": "1.3.0", "resolved": "http://xnpm.repo.xunlei.cn/uglifyjs-webpack-plugin/-/uglifyjs-webpack-plugin-1.3.0.tgz", "integrity": "sha1-dfVIFghYFjoIZD4IbV/v4YpdZ94=", "dev": true, "requires": {"cacache": "^10.0.4", "find-cache-dir": "^1.0.0", "schema-utils": "^0.4.5", "serialize-javascript": "^1.4.0", "source-map": "^0.6.1", "uglify-es": "^3.3.4", "webpack-sources": "^1.1.0", "worker-farm": "^1.5.2"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://xnpm.repo.xunlei.cn/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "union-value": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/union-value/-/union-value-1.0.0.tgz", "integrity": "sha1-XHHDTLW61dzr4+oM0IIHulqhrqQ=", "dev": true, "requires": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}, "dependencies": {"extend-shallow": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/extend-shallow/-/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "requires": {"is-extendable": "^0.1.0"}}, "set-value": {"version": "0.4.3", "resolved": "http://xnpm.repo.xunlei.cn/set-value/-/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "dev": true, "requires": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}}}}, "unique-filename": {"version": "1.1.1", "resolved": "http://xnpm.repo.xunlei.cn/unique-filename/-/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "dev": true, "requires": {"unique-slug": "^2.0.0"}}, "unique-slug": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/unique-slug/-/unique-slug-2.0.1.tgz", "integrity": "sha1-Xp7cbRzo+yZNsYpQfvm9hURFHKY=", "dev": true, "requires": {"imurmurhash": "^0.1.4"}}, "universalify": {"version": "0.1.2", "resolved": "http://xnpm.repo.xunlei.cn/universalify/-/universalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="}, "unpipe": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "dev": true}, "unset-value": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/unset-value/-/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "dev": true, "requires": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "dependencies": {"has-value": {"version": "0.3.1", "resolved": "http://xnpm.repo.xunlei.cn/has-value/-/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "dev": true, "requires": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "dependencies": {"isobject": {"version": "2.1.0", "resolved": "http://xnpm.repo.xunlei.cn/isobject/-/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "requires": {"isarray": "1.0.0"}}}}, "has-values": {"version": "0.1.4", "resolved": "http://xnpm.repo.xunlei.cn/has-values/-/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "dev": true}, "isarray": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/isarray/-/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}}}, "upath": {"version": "1.1.0", "resolved": "http://xnpm.repo.xunlei.cn/upath/-/upath-1.1.0.tgz", "integrity": "sha1-NSVll+RqWB20eT0M5H+prr/J+r0=", "dev": true}, "uri-js": {"version": "4.2.2", "resolved": "http://xnpm.repo.xunlei.cn/uri-js/-/uri-js-4.2.2.tgz", "integrity": "sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=", "dev": true, "requires": {"punycode": "^2.1.0"}, "dependencies": {"punycode": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/punycode/-/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew=", "dev": true}}}, "urix": {"version": "0.1.0", "resolved": "http://xnpm.repo.xunlei.cn/urix/-/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "dev": true}, "url": {"version": "0.11.0", "resolved": "http://xnpm.repo.xunlei.cn/url/-/url-0.11.0.tgz", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "dev": true, "requires": {"punycode": "1.3.2", "querystring": "0.2.0"}, "dependencies": {"punycode": {"version": "1.3.2", "resolved": "http://xnpm.repo.xunlei.cn/punycode/-/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=", "dev": true}}}, "url-parse": {"version": "1.4.3", "resolved": "http://xnpm.repo.xunlei.cn/url-parse/-/url-parse-1.4.3.tgz", "integrity": "sha1-v67kVciJAjIZ11fgRfpqaE7DbBU=", "dev": true, "requires": {"querystringify": "^2.0.0", "requires-port": "^1.0.0"}}, "use": {"version": "3.1.1", "resolved": "http://xnpm.repo.xunlei.cn/use/-/use-3.1.1.tgz", "integrity": "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=", "dev": true}, "util": {"version": "0.10.4", "resolved": "http://xnpm.repo.xunlei.cn/util/-/util-0.10.4.tgz", "integrity": "sha1-OqASW/5mikZy3liFfTrOJ+y3aQE=", "dev": true, "requires": {"inherits": "2.0.3"}}, "util-deprecate": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "utils-merge": {"version": "1.0.1", "resolved": "http://xnpm.repo.xunlei.cn/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "dev": true}, "uuid": {"version": "3.3.2", "resolved": "http://xnpm.repo.xunlei.cn/uuid/-/uuid-3.3.2.tgz", "integrity": "sha1-G0r0lV6zB3xQHCOHL8ZROBFYcTE=", "dev": true}, "v8-compile-cache": {"version": "2.0.2", "resolved": "http://xnpm.repo.xunlei.cn/v8-compile-cache/-/v8-compile-cache-2.0.2.tgz", "integrity": "sha1-pCiyi7JnkHNMT8i8n6EG/M6/amw=", "dev": true}, "vary": {"version": "1.1.2", "resolved": "http://xnpm.repo.xunlei.cn/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "dev": true}, "vm-browserify": {"version": "0.0.4", "resolved": "http://xnpm.repo.xunlei.cn/vm-browserify/-/vm-browserify-0.0.4.tgz", "integrity": "sha1-XX6kW7755Kb/ZflUOOCofDV9WnM=", "dev": true, "requires": {"indexof": "0.0.1"}}, "watchpack": {"version": "1.6.0", "resolved": "http://xnpm.repo.xunlei.cn/watchpack/-/watchpack-1.6.0.tgz", "integrity": "sha1-S8EsLr6KonenHx0/FNaFx7RGzQA=", "dev": true, "requires": {"chokidar": "^2.0.2", "graceful-fs": "^4.1.2", "neo-async": "^2.5.0"}}, "wbuf": {"version": "1.7.3", "resolved": "http://xnpm.repo.xunlei.cn/wbuf/-/wbuf-1.7.3.tgz", "integrity": "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=", "dev": true, "requires": {"minimalistic-assert": "^1.0.0"}}, "webpack": {"version": "4.23.1", "resolved": "http://xnpm.repo.xunlei.cn/webpack/-/webpack-4.23.1.tgz", "integrity": "sha1-23RnsRZ3GuAgxYvf4qCCJ4W7gjk=", "dev": true, "requires": {"@webassemblyjs/ast": "1.7.10", "@webassemblyjs/helper-module-context": "1.7.10", "@webassemblyjs/wasm-edit": "1.7.10", "@webassemblyjs/wasm-parser": "1.7.10", "acorn": "^5.6.2", "acorn-dynamic-import": "^3.0.0", "ajv": "^6.1.0", "ajv-keywords": "^3.1.0", "chrome-trace-event": "^1.0.0", "enhanced-resolve": "^4.1.0", "eslint-scope": "^4.0.0", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.3.0", "loader-utils": "^1.1.0", "memory-fs": "~0.4.1", "micromatch": "^3.1.8", "mkdirp": "~0.5.0", "neo-async": "^2.5.0", "node-libs-browser": "^2.0.0", "schema-utils": "^0.4.4", "tapable": "^1.1.0", "uglifyjs-webpack-plugin": "^1.2.4", "watchpack": "^1.5.0", "webpack-sources": "^1.3.0"}, "dependencies": {"ajv": {"version": "6.5.4", "resolved": "http://xnpm.repo.xunlei.cn/ajv/-/ajv-6.5.4.tgz", "integrity": "sha1-JH1SdBENtlNwa1UPzCt5fKKM/Fk=", "dev": true, "requires": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "fast-deep-equal": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://xnpm.repo.xunlei.cn/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true}}}, "webpack-cli": {"version": "3.1.2", "resolved": "http://xnpm.repo.xunlei.cn/webpack-cli/-/webpack-cli-3.1.2.tgz", "integrity": "sha1-F9fgG3f4n4hKK7+dtUXw9qZI50Y=", "dev": true, "requires": {"chalk": "^2.4.1", "cross-spawn": "^6.0.5", "enhanced-resolve": "^4.1.0", "global-modules-path": "^2.3.0", "import-local": "^2.0.0", "interpret": "^1.1.0", "loader-utils": "^1.1.0", "supports-color": "^5.5.0", "v8-compile-cache": "^2.0.2", "yargs": "^12.0.2"}}, "webpack-dev-middleware": {"version": "3.4.0", "resolved": "http://xnpm.repo.xunlei.cn/webpack-dev-middleware/-/webpack-dev-middleware-3.4.0.tgz", "integrity": "sha1-ETL+zJAm/ZDw7O2sXL/3XR+0WJA=", "dev": true, "requires": {"memory-fs": "~0.4.1", "mime": "^2.3.1", "range-parser": "^1.0.3", "webpack-log": "^2.0.0"}, "dependencies": {"mime": {"version": "2.3.1", "resolved": "http://xnpm.repo.xunlei.cn/mime/-/mime-2.3.1.tgz", "integrity": "sha1-sWIcVNY7l8R9PP5/chX31kUXw2k=", "dev": true}}}, "webpack-dev-server": {"version": "3.1.10", "resolved": "http://xnpm.repo.xunlei.cn/webpack-dev-server/-/webpack-dev-server-3.1.10.tgz", "integrity": "sha1-UHQRvucn7o0v3/3GIbZqZKs96is=", "dev": true, "requires": {"ansi-html": "0.0.7", "bonjour": "^3.5.0", "chokidar": "^2.0.0", "compression": "^1.5.2", "connect-history-api-fallback": "^1.3.0", "debug": "^3.1.0", "del": "^3.0.0", "express": "^4.16.2", "html-entities": "^1.2.0", "http-proxy-middleware": "~0.18.0", "import-local": "^2.0.0", "internal-ip": "^3.0.1", "ip": "^1.1.5", "killable": "^1.0.0", "loglevel": "^1.4.1", "opn": "^5.1.0", "portfinder": "^1.0.9", "schema-utils": "^1.0.0", "selfsigned": "^1.9.1", "serve-index": "^1.7.2", "sockjs": "0.3.19", "sockjs-client": "1.3.0", "spdy": "^3.4.1", "strip-ansi": "^3.0.0", "supports-color": "^5.1.0", "webpack-dev-middleware": "3.4.0", "webpack-log": "^2.0.0", "yargs": "12.0.2"}, "dependencies": {"ajv": {"version": "6.5.4", "resolved": "http://xnpm.repo.xunlei.cn/ajv/-/ajv-6.5.4.tgz", "integrity": "sha1-JH1SdBENtlNwa1UPzCt5fKKM/Fk=", "dev": true, "requires": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "fast-deep-equal": {"version": "2.0.1", "resolved": "http://xnpm.repo.xunlei.cn/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "dev": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://xnpm.repo.xunlei.cn/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true}, "schema-utils": {"version": "1.0.0", "resolved": "http://xnpm.repo.xunlei.cn/schema-utils/-/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "requires": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}}}}, "webpack-log": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/webpack-log/-/webpack-log-2.0.0.tgz", "integrity": "sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=", "dev": true, "requires": {"ansi-colors": "^3.0.0", "uuid": "^3.3.2"}}, "webpack-sources": {"version": "1.3.0", "resolved": "http://xnpm.repo.xunlei.cn/webpack-sources/-/webpack-sources-1.3.0.tgz", "integrity": "sha1-KijcufH0X+lg2PFJMlK17mUw+oU=", "dev": true, "requires": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}, "dependencies": {"source-map": {"version": "0.6.1", "resolved": "http://xnpm.repo.xunlei.cn/source-map/-/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}}}, "websocket-driver": {"version": "0.7.0", "resolved": "http://xnpm.repo.xunlei.cn/websocket-driver/-/websocket-driver-0.7.0.tgz", "integrity": "sha1-DK+dLXVdk67gSdS90NP+LMoqJOs=", "dev": true, "requires": {"http-parser-js": ">=0.4.0", "websocket-extensions": ">=0.1.1"}}, "websocket-extensions": {"version": "0.1.3", "resolved": "http://xnpm.repo.xunlei.cn/websocket-extensions/-/websocket-extensions-0.1.3.tgz", "integrity": "sha1-XS/yKXcAPsaHpLhwc9+7rBRszyk=", "dev": true}, "which": {"version": "1.3.1", "resolved": "http://xnpm.repo.xunlei.cn/which/-/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/which-module/-/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=", "dev": true}, "worker-farm": {"version": "1.6.0", "resolved": "http://xnpm.repo.xunlei.cn/worker-farm/-/worker-farm-1.6.0.tgz", "integrity": "sha1-rsxAWXb6talVJhgIRvDboojzpKA=", "dev": true, "requires": {"errno": "~0.1.7"}}, "wrap-ansi": {"version": "2.1.0", "resolved": "http://xnpm.repo.xunlei.cn/wrap-ansi/-/wrap-ansi-2.1.0.tgz", "integrity": "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=", "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}}, "wrappy": {"version": "1.0.2", "resolved": "http://xnpm.repo.xunlei.cn/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "xregexp": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/xregexp/-/xregexp-4.0.0.tgz", "integrity": "sha1-5pgYneSd0qGMxWh7BeF8jkOUMCA=", "dev": true}, "xtend": {"version": "4.0.2", "resolved": "http://xnpm.repo.xunlei.cn/xtend/-/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="}, "y18n": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/y18n/-/y18n-4.0.0.tgz", "integrity": "sha1-le+U+F7MgdAHwmThkKEg8KPIVms=", "dev": true}, "yallist": {"version": "2.1.2", "resolved": "http://xnpm.repo.xunlei.cn/yallist/-/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "yargs": {"version": "12.0.2", "resolved": "http://xnpm.repo.xunlei.cn/yargs/-/yargs-12.0.2.tgz", "integrity": "sha1-/lgjQ2k5KvM+y+9TgZFx7/D1qtw=", "dev": true, "requires": {"cliui": "^4.0.0", "decamelize": "^2.0.0", "find-up": "^3.0.0", "get-caller-file": "^1.0.1", "os-locale": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1 || ^4.0.0", "yargs-parser": "^10.1.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/ansi-regex/-/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true}, "decamelize": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/decamelize/-/decamelize-2.0.0.tgz", "integrity": "sha1-ZW17vICUxMeI6lPFhAkIycfQY8c=", "dev": true, "requires": {"xregexp": "4.0.0"}}, "find-up": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/find-up/-/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "requires": {"locate-path": "^3.0.0"}}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true}, "locate-path": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/locate-path/-/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "p-limit": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-limit/-/p-limit-2.0.0.tgz", "integrity": "sha1-5iTtVO6MRgp3izyfNnBJb/ileuw=", "dev": true, "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-locate/-/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.0.0", "resolved": "http://xnpm.repo.xunlei.cn/p-try/-/p-try-2.0.0.tgz", "integrity": "sha1-hQgLuHxkaI+keZb+j3376CEXYLE=", "dev": true}, "string-width": {"version": "2.1.1", "resolved": "http://xnpm.repo.xunlei.cn/string-width/-/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dev": true, "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "http://xnpm.repo.xunlei.cn/strip-ansi/-/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "requires": {"ansi-regex": "^3.0.0"}}}}, "yargs-parser": {"version": "10.1.0", "resolved": "http://xnpm.repo.xunlei.cn/yargs-parser/-/yargs-parser-10.1.0.tgz", "integrity": "sha1-cgImW4n36eny5XZeD+c1qQXtuqg=", "dev": true, "requires": {"camelcase": "^4.1.0"}, "dependencies": {"camelcase": {"version": "4.1.0", "resolved": "http://xnpm.repo.xunlei.cn/camelcase/-/camelcase-4.1.0.tgz", "integrity": "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=", "dev": true}}}, "yauzl": {"version": "2.10.0", "resolved": "http://xnpm.repo.xunlei.cn/yauzl/-/yauzl-2.10.0.tgz", "integrity": "sha1-x+sXyT4RLLEIb6bY5R+wZnt5pfk=", "requires": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "yazl": {"version": "2.5.1", "resolved": "http://xnpm.repo.xunlei.cn/yazl/-/yazl-2.5.1.tgz", "integrity": "sha1-o9ZdPdZZpbCTeFDoYJ8i//orXDU=", "requires": {"buffer-crc32": "~0.2.3"}}}}