import { client } from '@xunlei/node-net-ipc/dist/ipc-client'
import { getConfigValue } from '@/common/ipc'

import * as path from 'path'
import requireNodeFile from '@/common/require-node-file'

const thunderHelper: any = requireNodeFile(path.join(__rootDir, '../bin/ThunderHelper.node'))
const thunderPath = path.join(__rootDir, '../../Thunder.exe')
const crypto = require('crypto');
const peer_id = thunderHelper.getPeerID()

import TinyLogger from '@xunlei/tiny-logger';
const logger: TinyLogger = TinyLogger.getLogger('task-center-api')

let aesKey = '-----BEGIN PUBLIC KEY-----\r\n';
aesKey += 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgw8Pvb5JAZJpg+IM0wBh/orNX\r\n';
aesKey += '/GezCuDUZKERCNAuqIgDNNTzrPFeyBKG0vSF0Jtr8QnwEr5EsLAf+KUsuLDgarUq\r\n';
aesKey += 'M1irgxdgkSGlOjQRKYhXZd1e8tdGCWn+5NS0Y4nmMrohiLkR7MRpkwI/pt29RCGI\r\n';
aesKey += 'Fz2z5m6qdigMZLgPlQIDAQAB\r\n';
aesKey += '-----END PUBLIC KEY-----\r\n';

export function cryptoData (jsonData: {} & any): string {
  try {
    let strJson = JSON.stringify(jsonData);
    let text: string = crypto.publicEncrypt({ key: aesKey, padding: crypto.constants.RSA_PKCS1_PADDING }, Buffer.from(strJson, 'utf-8')).toString('hex');
    return text;
  } catch (error) {
    logger.information(error);
    return '';
  }
}

export function getThunderVersionNumber (thunderVersionStr: string) {
  let thunderVersionNumber: number = 0;
  const version: string[] = thunderVersionStr.split('.');
  if (version && version.length === 4) {
    const v1: number = Number(version[0]).valueOf();
    const v2: number = Number(version[1]).valueOf();
    const v3: number = Number(version[2]).valueOf();
    const productType: number = 0x80;
    thunderVersionNumber = productType * Math.pow(2, 24) + v1 * Math.pow(2, 16) + v2 * Math.pow(2, 8) + v3;
  }
  return thunderVersionNumber;
}

export async function doRequest<T> (
  uri: string,
  method: 'get' | 'post',
  body?: any
): Promise<{
  err:{
    error_code: number,
    error?: string,
    error_description?: string
  },
  data?: T
}> {
  const accessToken: any = (await client.callRemoteClientFunction('User', 'UserGetAccessToken').catch());
  const version = thunderHelper.getFileVersion(thunderPath);
  let headers = {
    'app-type': 'pc',
    'platform-version': '',
    'platform-version-sdk': '',
    'product-id': '',
    'user-agent': navigator.userAgent,
    'version-code': '',
    'version-name': '',
    'client-name': 'xl_xdas',
    'client-version': getThunderVersionNumber(version),
    'peer-id': peer_id,
    'x-client-version-code': version,
    'Authorization': accessToken && 'Bearer ' + (accessToken[0])
  };

  try {
    const env = await getConfigValue('ApiTestEnv', 'TaskCenter', 0) as number; // 判断下当前环境0 正式环境，1测试环境

    let url: string = 'https://api-shoulei-ssl.xunlei.com';
    if (env === 1) {
      url = 'http://test.api-shoulei-ssl.xunlei.com';
    }
    url += uri;

    const axios: any = await require('axios');
    let response = await axios({url, method, headers, data: body ? body : '', timeout: 5000});

    logger.information('TaskCenter, url=', url, ',respones=', response);

    if (response !== null) {
      if (response.status !== 200) {
        return {err: response};
      } else {
        return {err: {error_code: 0}, data: response.data};
      }
    } else {
      return {err: {error_code: -1, error: 'response_empty', 'error_description': '请求失败'}}
    }
  } catch (res) {
    if ((res as any).response) {
      return {err: (res as any).response.data};
    }
    return {err: {error_code: -2, error: 'response_exception', 'error_description': '请求异常'}};
  }
}
