<template>
  <div class="xly-favorites">
    <setting
      :showNews="showNews"
      :sitesOn="sitesOn"
      :subscribeShow="subscribeShow"
      :hotwordShow="hotwordShow"
      :nodisturbDays="nodisturbDays"
      @init-setting="onInitSetting"
      @setting-change="onSettingChange">
    </setting>

    <CommonSites v-show="settingInited && sitesOn"></CommonSites>

    <!-- <div class="xly-favorites-wrapper">
      <HotwordImage
        v-if="imageSlots.length > 0"
        :settingInited="settingInited"
        :hotwordShow="hotwordShow"
        :showedIds="hotwordShowedIds"
        :visible="visible"
        :options="imageSlots"
        @slot-showed="onHotwordSlotShowed"
      >
      </HotwordImage>

      <HotwordText
        v-else-if="textSlots.length > 0"
        v-show="settingInited && hotwordShow"
        :hotwordShow="hotwordShow"
        :visible="visible"
        :options="textSlots"
      >
      </HotwordText>

      <FavoritesDynamic
        v-if="showNews"
        ref="dynamic"
        v-show="subscribeShow && settingInited "
        :subscribeShow="subscribeShow"
        :settingInited="settingInited"
        :visible="selectEmpty"
        :isLogined ="isLogined"
        :noCollectOptions="noCollectOptions"
      ></FavoritesDynamic>

      <div ref="lazy" v-load="{ handler: handleLoadMore, distance: `50px` }"></div>
    </div> -->

    <div class="xly-empty" v-show="showBird"><i class="xly-icon-logo"></i></div>
  </div>
</template>

<script lang="ts">
import TinyLogger from '@xunlei/tiny-logger';
import { Vue, Component, Watch } from 'vue-property-decorator';
import CommonSites from './views/common-sites.vue';
// import FavoritesDynamic from './views/favorites-dynamic.vue';
import Setting from './views/setting.vue';
// import HotwordImage from './views/hotword-image.vue';
// import HotwordText from './views/hotword-text.vue'
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
import { ISlotItem } from '@/main-renderer/common/data-define'

const logger: TinyLogger = TinyLogger.getLogger('start-page-root');
let delayTimerId: number = undefined;

@Component({
  components: {
    CommonSites,
    // FavoritesDynamic,
    Setting,
    // HotwordImage,
    // HotwordText,
  }
})
export default class App extends Vue {
  textSlots: ISlotItem[] = [];
  imageSlots: ISlotItem[] = [];

  selectEmpty: boolean = true;
  settingInited: boolean = false;
  sitesOn: boolean = false;
  subscribeShow: boolean = false;
  hotwordShow: boolean = false;
  showBird: boolean = true;
  isLogined: boolean = null;

  hotwordShowedIds: string[] = [];

  get nodisturbDays(): number {
    return (this.$parent as any)?.searchNavs?.notshow_days || 30;
  }

  get showNews(): boolean {
    return (this.$parent as any)?.searchNavs.news_show ?? true;
  }

  get noCollectOptions(): {
    text: string;
    url: string;
  } {
    return (this.$parent as any)?.searchNavs?.nocollect_default;
  }

  get visible(): boolean {
    if (this.textSlots?.length === 0 && this.imageSlots?.length === 0) {
      return false;
    }

    return this.selectEmpty;
  }

  get allHide(): boolean {
    return !this.sitesOn && !this.subscribeShow && !this.hotwordShow;
  }

  @Watch('allHide')
  onShowEmptyPlaceholder(val: boolean): void {
    logger.information('allHide', val);
    if (val) {
      this.delayShowEmpty();
    } else {
      if (delayTimerId !== undefined) {
        clearTimeout(delayTimerId);
        delayTimerId = undefined;
      }
      this.showBird = false;
    }
  }

  // handleLoadMore(): void {
  //   const lazyRoot = (this.$refs?.['lazy'] as Element).parentElement;
  //   (this.$refs?.['dynamic'] as any)?.loadMore(lazyRoot);
  // }

  // 在收起动画结束后再显示占位符
  delayShowEmpty(): void {
    if (delayTimerId !== undefined) {
      clearTimeout(delayTimerId);
      delayTimerId = undefined;
    }

    delayTimerId = setTimeout(() => {
      delayTimerId = undefined;
      if (this.allHide) {
        this.showBird = true;
      }
    }, 500) as any;
  }

  onHotwordSlotShowed(id: string): void {
    if (!this.hotwordShowedIds.includes(id)) {
      this.hotwordShowedIds.push(id);
    }
  }

  onInitSetting(sitesOn: boolean, subscribeShow: boolean, hotwordShow: boolean): void {
    this.settingInited = true;
    this.sitesOn = sitesOn;
    // this.subscribeShow = subscribeShow;
    // this.hotwordShow = hotwordShow;

    const extData: string = `usual_url_setting_state=${sitesOn ? 'show' : 'not_show'},collect_url_update_setting_state=${subscribeShow ? 'show' : 'not_show'}`;
    clientModule.callServerFunction('TrackEvent', 'core_event', 'new_start_page_show', '', 0, 0, 0, 0, extData);
  }

  onSettingChange(type: 'subscribe' | 'sites' | 'hotword', show: boolean) {
    if (type === 'sites') {
      this.sitesOn = show;
    }
    // else if (type === 'subscribe') {
    //   this.subscribeShow = show;
    // } else if (type === 'hotword') {
    //   this.hotwordShow = show;
    // }
    // const extData: string = `accurate_search=${this.sitesOn ? 'show' : 'not_show'},content_recommend=${this.subscribeShow ? 'show' : 'not_show'}`;
    // clientModule.callServerFunction('TrackEvent', 'download_detail', 'dltab_search_content_recommend_status', '', 0, 0, 0, 0, extData);
  }

  getHotwordMode(allSlots: ISlotItem[]): void {
    const tempTextSlots: ISlotItem[] = [];
    const tempImageSlots: ISlotItem[] = [];
    do {
      if (allSlots?.length === 0 ){
        break;
      }

      for (let i: number = 0; i < allSlots.length; i++) {
        const slot = allSlots[i];

        if (slot.getSucceed() && slot.getSubSlots()?.length > 0) {
          if (slot.id() === 'xl_pc_collect_recommend') {
            tempTextSlots.push(slot);
          } else {
            tempImageSlots.push(slot);
          }
        }
      }
    } while (0);

    this.textSlots = tempTextSlots;
    this.imageSlots = tempImageSlots;
  }

  async getSlots(): Promise<void> {
    function deepCopy(obj: any): any {
      const str: string = JSON.stringify(obj);
      let result: any = null;
      try {
        result = JSON.parse(str);
      } catch (error) {
        logger.warning(error);
      }
      return result;
    }

    function getPrintable(input: any[]): any {
      if (!input) {
        return '';
      }
      if (input instanceof Array) {
        //
      } else {
        return input;
      }
      let results: any[] = [];
      for (let item of input) {
        let result: any = {};
        let propertys: string[] = Object.getOwnPropertyNames(item);
        propertys.forEach((property: string) => {
          switch (typeof item[property]) {
            case 'function':
            case 'symbol':
            case 'undefined':
              break;
            default:
              result[property] = deepCopy(item[property]);
              break;
          }
        });
        results.push(result);
      }
      return results;
    }

    const slotIds: string[] = [
      'xl_pc_collect_recommend',
      'xl_pc_startpage_middle_banner_1',
      'xl_pc_startpage_middle_banner_2',
      'xl_pc_startpage_middle_banner_3',
      'xl_pc_startpage_middle_banner_4',
      'xl_pc_startpage_middle_banner_5'
    ];

    let slots: ISlotItem[] = await clientModule.callServerFunction('FSFetchSlotArrayEx', slotIds);
    if (slots) {
      slots = slots.filter((item: ISlotItem) => {
        return item.getSucceed();
      });
    }

    // 重置已经显示的slot id列表为空
    this.hotwordShowedIds = [];
    logger.information('get slots', getPrintable(slots));
    if (slots && slots.length >= 0) {
      this.getHotwordMode(slots);
    }
  }

  async created(): Promise<void> {
    clientModule.attachServerEvent('OnSelectTaskChange', (context: any, categoryViewId: string, val: number[]) => {
      if ((val?.length ?? 0) === 0) {
        this.selectEmpty = true;
      } else {
        this.selectEmpty = false;
      }
    });

    this.isLogined = await clientModule.callServerFunction('IsLogined'); // 等待登录初始化完毕
    clientModule.attachServerEvent('OnLoginSuc', async (context: any, ...args: any[]) => {
      this.isLogined = true;
      this.getSlots().catch();
    });
    clientModule.attachServerEvent('OnLogout', async (context: any, ...args: any[]) => {
      this.isLogined = false;
      this.getSlots().catch();
    });
    clientModule.attachServerEvent('OnLoginUpdate', async (context: any, type: string, data: string) => {
      if (type === 'vipinfo' || type === 'userinfo') {
        this.getSlots().catch();
      }
    });

    this.getSlots().catch();
  }
}
</script>

<style src="@/assets/css/download/xly-favorites.css"></style>
<style src="@/assets/css/xly-icon.css"></style>
<style src="@/assets/css/xly-common.css"></style>