const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');

const packageJSON = require('../package.json');
const buildTarget = process.env.BIN_TARGET || 'Debug';
const outDir = path.join(path.resolve(packageJSON.build.outDir), buildTarget, 'TaskCenter', packageJSON.version);

const isBuildProduction = process.env.BUILD_ENV === 'production';
const isDeveloping = process.env.RUN_ENV === 'development';

module.exports = (rendererName) => {
  const rendererSrcPath = `../src/${rendererName}`;

  console.log(path.join(__dirname, '../src/common/tiny-logger.ts'));
  console.log(path.join(__dirname, rendererSrcPath, 'task-center.ts'));

  return {
    entry: {
      index: [
        path.join(__dirname, '../src/common/tiny-logger.ts'),
        path.join(__dirname, rendererSrcPath, 'task-center.ts')
      ]
    }
  };
};
