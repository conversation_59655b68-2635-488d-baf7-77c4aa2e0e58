import axios from 'axios';
const xss: any = require('xss');
const emojiMap: any = require('@/assets/data/emojiMap.json');

/**
 * 获取用户个人中心链接
 * <AUTHOR>
 * @param {string} userId - 用户 id
 * @param {string} entrypage - 个人中心的入口页
 * @param {string} entry - 进入个人中心页的点击位置
 * @return {string} 链接
 */
export function getUserLink(userId: string, entrypage: string = '', entry: string = ''): string {
  return `http://pc.xunlei.com/d/#/users/${userId}?entrypage=${entrypage}&entry=${entry}`;
}

// 七牛的图片CDN
export const QINIU_HOST: string[] = [/* tslint:disable-line */
  'sl.wangpan.7niu.n0808.com',
  'pc.wangpan.xycdn.n0808.com',
  'sl.image.7niu.n0808.com',
  'static-movie.a.88cdn.com'
];

/** 七牛图片缩略、剪裁 */
export function qiniuImageView2(url: string, mode: number = 1, w: number, h: number, q: number = 90): string {
  url = toHttps(url.split('?imageView2')[0]);
  return `${url}?imageView2/${mode}` + (w ? `/w/${w}` : '') + (h ? `/h/${h}` : '') + `/q/${q}/interlace/1`;
}

// 将地址转化成https
export function toHttps(url: string): any {
  const aliyunHttps: string = 'static-movie.a.88cdn.com';
  const shortVideoImageHttps: string = 'shortvideo-image.a.88cdn.com';
  const dnionHttps: string = 'static-xl.a.88cdn.com';

  const originsMap: any = {
    'sl.wangpan.7niu.n0808.com': aliyunHttps,
    'pc.wangpan.xycdn.n0808.com': aliyunHttps,
    'sl.image.7niu.n0808.com': shortVideoImageHttps,
    'static-xl9-ssl.xunlei.com': dnionHttps
  };
  let domain: any;
  try {
    domain = url.match(/http:\/\/(.+\.com)\/.+/)[1];
  } catch (error) {
    return url;
  }

  const replace: any = (): any => {
    url = url.replace(/http:\/\//, 'https://');
    return url.replace(domain, originsMap[domain]);
  };

  if (!originsMap[domain]) {
    // 如果都没有则返回原地址
    return url;
  } else {
    return replace();
  }
}

/**
 * 获取缩略图url
 * @param {string} url
 * @param {number} width
 * @param {number} height
 */
export function getThumbUrl(url: string, width: number, height: number, option: any = { keepRatio: false }): string {
  if (!url) {
    return '';
  }
  const plugins: any = {
    qiniu: {
      hosts: QINIU_HOST,
      handler: (): any => {
        return qiniuImageView2(url, option.keepRatio ? 2 : 1, width, height);
      }
    },
    dnion: {
      hosts: [
        'static-xl9-ssl.xunlei.com'
      ],
      handler: (): any => {
        return `${url}?w=${width}&h=${height}`;
      }
    },
    aliyun: {
      hosts: [
        'static-xl.a.88cdn.com'
      ],
      handler: (): any => {
        // 图片缩放文档
        // https://help.aliyun.com/document_detail/44688.html?spm=a2c4g.11186623.6.1181.38f917f1xjZfdQ
        return `${url}?x-oss-process=image/resize,m_mfit,h_${height},w_${width}`;
      }
    }
  };

  for (const i in plugins) {
    const { hosts, handler }: any = plugins[i];
    if (hosts.some((host: any): any => url.match(host))) {
      return handler();
    }
  }

  // 如果没有匹配，就返回原url
  return url;
}

// 阿里云的图片CDN
export const ALIYUN_HOST = [ /* tslint:disable-line */
  'aliyuncs.com',
  'a.88cdn.com',
  'pic-comment-shoulei-ssl.xunlei.com'
];

export const isALi: any = (url: string): any => ALIYUN_HOST.some((item: any) => url.includes(item));

// 阿里云图片缩放裁剪
export function aliImageView(url: string, mode: number = 1, w: number, h: number, q: number = 90): any {
  if (!isALi(url)) {
    return;
  }
  let modeStr: string = 'm_mfit';
  switch (mode) {
    case 1:
      modeStr = 'm_mfit';
      break;
    case 2:
      modeStr = 'm_lfit';
      break;
  }
  const width: string = w ? ',w_' + w : '';
  const height: string = h ? ',h_' + h : '';
  return `${url}&x-oss-process=image/resize,${modeStr}${height}${width}/quality,q_${q}`;
}

// 获取阿里云图片信息
export async function aliImageInfo(url: string): Promise<any> {
  if (isALi(url)) {
    if (url.indexOf('?') > 0) {
      return axios.get(`${url}&x-oss-process=image/info`).then((res: any) => {
        return res.data;
      });
    } else {
      return axios.get(`${url}?x-oss-process=image/info`).then((res: any) => {
        return res.data;
      });
    }
  } else {
    // 非CDN，加载原图
    return new Promise((resolve: any, reject: any): any => {
      const img: any = new Image();
      img.src = url;
      img.onload = (): any => {
        const imgInfo: any = {};
        imgInfo.ImageWidth = img.width;
        imgInfo.ImageHeight = img.height;
        resolve(imgInfo);
      };
    });
  }
}

/**
 * 图片懒加载
 * @param {string} loadingImg - loading时的图片
 * @param {string} src - 要加载的图片
 * @return {object} 懒加载对象
 */
export function lazyImgObj({ loadingImg = '', src }: any): any {
  return {
    src,
    loading: loadingImg,
    error: loadingImg
  };
}

// 将评论中的表情替换成自定义的样式
export function replaceEmoji(str: string = ''): any {
  if (typeof str !== 'string') return str;

  // xss
  str = xss(str, {
    whiteList: {
      br: []
    }
  });
  // 匹配表情文字，类似：[开心]
  const match: any = str.match(/\[[^[\]]+\]/g);
  match &&
    match.forEach((item: any) => {
      const unicode: any = emojiMap[item];
      if (unicode) {
        str = str.replace(
          item,
          `<span class="face-icon-emoji emoji-${unicode}"></span>`
        );
      }
    });
  return str;
}
