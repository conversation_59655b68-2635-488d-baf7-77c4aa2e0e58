<template>
  <div class="xly-favorites-common">
    <div class="xly-favorites-common__header">
      <h2>常用网站</h2>
    </div>
    <div class="xly-favorites-common__list">
      <ul class="xly-favorites-common__items" ref="scrollHorizontal">
        <li
          class="xly-favorites-common__item"
          :title="item.name || item.url"
          v-for="(item, index) of filterMostCommonlySites"
          :key="item.url"
          @click="onClickCommonSite(index)"
        >
          <div class="xly-favorites-common__icon">
            <img v-if="item.iconUrl" v-show="item.iconLoaded" :src="item.iconUrl" @load="onIconUrlLoad(index, item.iconUrl)" :alt="item.name" />
            <i v-show="!item.iconLoaded" class="xly-icon-net"></i>
          </div>
          <div class="xly-favorites-common__more" v-if="!item.default" @click.stop="onClickMore" @mouseleave="dropStyle=''">
            <i class="xly-icon-more"></i>
            <div class="xly-more-drop" :style="dropStyle">
              <ul>
                <li @click.stop="deleteMostCommonly(index)">删除</li>
              </ul>
            </div>
          </div>
          <p class="xly-favorites-common__text">{{ item.name || item.url }}</p>
        </li>
      </ul>
      <div class="xly-favorites-common__add" v-if="false">
        <div class="xly-favorites-common__icon">
          <i class="xly-icon-add"></i>
        </div>
        <p class="xly-favorites-common__text">添加网站</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch } from 'vue-property-decorator';
import { ISiteItemOptions } from '@/main-renderer/common/data-define';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';

@Component({
  components: {
  },
  inheritAttrs: false
})
export default class CommonSites extends Vue {
  dropStyle: string = '';
  // 常用网址
  mostCommonlySites: ISiteItemOptions[] = [];

  filterMostCommonlySites: ISiteItemOptions[] = [];

  defaultSites: ISiteItemOptions[] = [{
    name: '百度', url: 'https://www.baidu.com/', default: true, iconUrl: 'https://www.baidu.com/favicon.ico'
  },
  {
    name: '迅雷', url: 'https://www.xunlei.com/', default: true, iconUrl: 'https://www.xunlei.com/favicon.ico'
  }];

  @Watch('mostCommonlySites', { immediate: true })
  onmostCommonlySiteschange(): void {
    if (this.mostCommonlySites.length >= 6) {
      this.filterMostCommonlySites = [ ...this.mostCommonlySites ];
    }
    const mostCommonlySites = [...this.mostCommonlySites, ...this.defaultSites];
    this.filterMostCommonlySites = mostCommonlySites.slice(0, 6);
  }

  async mounted(): Promise<void> {
    (this.$refs.scrollHorizontal as Element).addEventListener("wheel", (event) => {
      event.preventDefault();
      (this.$refs.scrollHorizontal as Element).scrollLeft += (event as any).deltaY * 0.08;
    });

    const mostCommonlySites: ISiteItemOptions[] = await clientModule.callServerFunction('GetMostCommonly');
    this.mostCommonlySites = mostCommonlySites.map(
      (val) => {
        if (!val.iconUrl) {
          const url = new URL((val.url.startsWith('http') ? '' : 'http://') + val.url).origin;
          val.iconUrl = url + '/favicon.ico'; // 尝试补一个默认图标
        }
        return { ...val };
      });
  }

  // 图标加载完成后, 显示加载的图标
  onIconUrlLoad(index: number, url: string): void {
    if (this.filterMostCommonlySites[index].iconUrl !== url) {
      index = this.filterMostCommonlySites.findIndex((val: ISiteItemOptions) => val.iconUrl === url);
      if (index === -1) {
        return ; // 因为异步更新了mostCommonlySites, 此时已经不存在
      }
    }
    this.$set(this.filterMostCommonlySites[index], 'iconLoaded', true);
    if (index < this.mostCommonlySites.length) {
      this.mostCommonlySites[index].iconLoaded = true;
    } else if (index - this.mostCommonlySites.length < 2) {
      // baidu或xunlei
      this.defaultSites[index - this.mostCommonlySites.length].iconLoaded = true;
    }
  }

  onClickMore(): void {
    this.dropStyle = 'display: block';
  }

  onClickCommonSite(index: number): void {
    clientModule.callServerFunction('OpenNewTab', this.filterMostCommonlySites[index].url);
  }

  deleteMostCommonly(index: number): void {
    this.dropStyle = '';
    clientModule.callServerFunction('DeleteMostCommonly', this.mostCommonlySites[index].url);
    this.mostCommonlySites.splice(index, 1);
  }
}
</script>
