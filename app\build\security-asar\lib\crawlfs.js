'use strict'

const { promisify } = require('util')

const fs = require('./wrapped-fs')
const glob = promisify(require('glob'))
const crypto = require('./asarCrypto');

// .map文件在非日志版不存在, 所以不加密, 图片和fonts文件不加密
function fileInWhiteList(filename) {
  // if (filename.endsWith('plugin-boot.js')) {
  //   return false;
  // }
  let whiteList = ['.js', '.html', '.css'];// [".js", ".html", ".css", ".json"];
  for (let white of whiteList) {
    if (filename.endsWith(white)) {
      return true;
    }
  }
  return false;
}

// 获取文件属性, 并且加密
// 只有走fs加载的文件才需要加密(第一期只加密renderer.js)
async function determineFileType(filename, aes_conf) {
  let stat = await fs.lstat(filename);
  if (stat.isFile()) {
    if (fileInWhiteList(filename) && aes_conf && aes_conf.key) {
      crypto.encryptFile(filename, filename, aes_conf.key);
      stat = await fs.lstat(filename);
      return { type: 'file', stat: stat, encrypted: true }
    }
    return { type: 'file', stat: stat }
  } else if (stat.isDirectory()) {
    return { type: 'directory', stat }
  } else if (stat.isSymbolicLink()) {
    return { type: 'link', stat }
  }
}

module.exports = async function (dir, options, aes_conf) {
  const metadata = {}
  const crawled = await glob(dir, options)
  const results = await Promise.all(crawled.map(async filename => [filename, await determineFileType(filename)])) // 这里的遍历不进行加密
  const filenames = results.map(([filename, type]) => {
    if (type) {
      metadata[filename] = type
    }
    return filename
  })
  return [filenames, metadata]
}
module.exports.determineFileType = determineFileType
