
import { client } from '@xunlei/node-net-ipc/dist/ipc-client';

/**
 * Captcha 请求
 */
export async function normalizeUserRequest (input: RequestInfo, init?: RequestInit): Promise<unknown> {
  return client.callRemoteClientFunction('User', 'Request', input, init).then((response: unknown[]) => {
    if (response[1]) {
      throw response[1]
    }
    return response[0]
  })
}

export type MessageOptions = {
  message: string
  type: 'success' | 'warning' | 'error'
  position: 'middle' | 'top'
  duration: number
  unique: boolean
  uniqueById: boolean
  id: string
}
export interface MessageType {
  (options: Partial<MessageOptions>): void
}
/**
 * 主渲染进程的 toast
 */
export function showMessageToast (option: Parameters<MessageType>[0]) {
  return client.callServerFunction('ShowMessageToast', { position: 'middle', ...option })
}

export enum MsgPriority { // 消息的优先级
  Community = 0, // 社区tips
  AdvertisementMarket, // 广告运营tips
  AdvertisementFunctional, // 广告功能tips
  PreciseDelivery, // 精准投放
  CenterVip, // 续费触达
  Download // 下载tips
}
export interface ICommonExOptions {
  header: string;
  title: string; // 标题
  text: string; // 内容
  icon?: string; // 内容
  priority?: MsgPriority; // 优先级，数值越大优先级越高
  okText: string;
  checkboxEnabled?: boolean;
  checkboxLabel?: string;
  okVisible: boolean;
  beforeClose?: () => void; // 点击关闭按钮的回调
  onClick?: (from: string, checked: boolean) => void; // 点击标题或者内容的回调
}
export interface IOptions {
  duration?: number; // tips时长，默认10s，单位是 ms
}
/**
 * 右下角推送提醒
 */
export function showPushNotify (message: ICommonExOptions, options: IOptions, name?: string): Promise<void> {
  if (name !== undefined && name !== null) {
    return client.callServerFunction('PushMergeableNotify', message, options, name).catch()
  }
  return client.callServerFunction('PushNotify', message, options).catch()
}

export type ConfigValue = string | number | boolean | string[] | number[]
export function setConfigValue (section: string, key: string, value: ConfigValue) {
  return client.callServerFunction('SetConfigValue', section, key, value) as Promise<void>
}

export function getConfigValue (section: string, key: string, defValue: ConfigValue | null) {
  return client.callServerFunction('GetConfigValue', section, key, defValue) as Promise<ConfigValue | null>
}

export function trackEvent (eventKey: string, attr: string, extData: string) {
  return client.callServerFunction(
    'TrackEvent',
    eventKey,
    attr,
    '',
    0,
    0,
    0,
    0,
    extData
  )
}

export async function getUserInfo () {
  try {
    const userInfo = JSON.parse(await client.callServerFunction('GetUserInfo'))

    return {
      ...userInfo[1],
      ...userInfo[2],
      userId: userInfo[1].userid
    }
  } catch (err) {
    return {}
  }
}