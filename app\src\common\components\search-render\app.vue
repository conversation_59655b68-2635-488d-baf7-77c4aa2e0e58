<template>
      <div class="xly-search-drop" id="search-window" tabindex="0">
        <!-- 搜索框区域 -->
        <div class="xly-search-drop__input">
          <a  @click="$emit('clearSearchInput')" v-show="textData.length > 0">清空</a>
        </div>
        <td-tabs :tabs="tabs" :active-key.sync="activeKey">
          <template slot-scope="{ tab }">
            <!-- <div class="xly-search-tips" v-show="showFavUpdateTips">
            <h4>🎉收藏夹全新升级</h4>
            <p>收藏夹网站信息发生变动时，可以查看动态更新啦~</p>
            <i class="td-icon-close" @click="showFavUpdateTips = false" title="关闭"></i>
          </div> -->
            <component
              ref="tabPanel"
              :is="tab.key"
              :activeKey="activeKey"
              :keyword="keyWord"
              :openFrom="openFrom"
              :select.sync="selectIndex"
              :isInSafeSpace="isInSafeSpace"
              :searchEngineUrl="searchEngineUrl"
              :userid="userid"
              :isLogined="isLogined"
              :clearConfirm="clearConfirm"
              :wholeSearch="wholeSearch"
              @hideDropDownWindow="hideDropDownWindow"
              @resizeSearchWindow="resizeSearchWindow"
              @searchKeyword="searchKeyword"
              @tabChange="onTabChange"
            ></component>
          </template>
          <div slot="title" slot-scope="{ tab }" v-text="tab.title"></div>
        </td-tabs>

        <ClearConfirmCtrl ref="confirm"/>
      </div>
</template>

<script lang="ts">
/**
 * @description 搜索下拉窗口
 */
import { XLStatNS } from '@/common/xlstat';
import TinyLogger from '@xunlei/tiny-logger';
import ClearConfirmCtrl from '@/common/components/clear-verify.vue';
import { Vue, Component, Watch, Prop } from 'vue-property-decorator';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
import TabCommonSearch from '@/common/components/search-components/tab-common-search.vue';
import { ISearchInputChangeOptions, ISearchWindowKeyDownOptions } from '@/common/browser-tabs-define';
import { NavViewID } from '@/common/navs-define';

const logger: TinyLogger = TinyLogger.getLogger('search-drop');

/**
 * 测试配置链接http://*************:3000/#/projects/8bcd9823-14ca-4f65-b774-4d7944a3f48b/configs/8bcd9823-14ca-4f65-b774-4d7944a3f48b
 * 获取链接 http://*************:3001/external/8bcd9823-14ca-4f65-b774-4d7944a3f48b?peer-id=a4f80a298026bc25cf79991a0d07bcfd&version-code=11980
 * 获取方法:
 * client.callServerFunction('GetRemoteGlobalConfigValue', 'home', 'home_act')
 */

@Component({
  components: {
    TabCommonSearch,
    ClearConfirmCtrl,
    TabFav: (): Promise<unknown> => import('@/common/components/search-components/tab-fav.vue'),
    TabDownload: (): Promise<unknown> => import('@/common/components/search-components/task-item-panel.vue'),
    TabPan: (): Promise<unknown> => import('@/common/components/search-components/pan-task-item-panel.vue'),
    TabHistory: (): Promise<unknown> => import('@/common/components/search-components/tab-history.vue'),
  }
})
export default class Search extends Vue {
  @Prop({})
  parentId: number;

  @Prop({default: ''})
  textData: string;

  @Prop()
  currNav: NavViewID;

  @Prop()
  wholeSearch: (text: string) => void;

  tabs: { title: string, key: string }[] = [
    { title: '全网', key: 'TabCommonSearch' },
    { title: '下载', key: 'TabDownload' },
    { title: '云盘', key: 'TabPan' },
    { title: '收藏', key: 'TabFav' },
    { title: '历史', key: 'TabHistory' },
  ];
  activeKey: string = 'TabCommonSearch';

  @Watch('downloadNav')
  onDownloadNavChange() {
    if (this.currNav === NavViewID.Cloud) {
      this.activeKey = 'TabPan';
    } else {
      this.activeKey = 'TabCommonSearch';
    }
  }

  // showFavUpdateTips: boolean = false;
  // favUpdateTipsTimerId: number = undefined;

  @Watch('activeKey')
  onActiveKeyChange(): void {
    logger.information('activeKey: ', this.activeKey);
    this.$emit('tabChange', this.activeKey);
    const tabKeyMap: {[key: string]: string} = {
      TabCommonSearch: 'comment_search',
      TabDownload: 'download',
      TabPan: 'pan',
      TabFav: 'collect_website',
      TabHistory: 'history_website'
    };
    XLStatNS.trackEventEx('download_leftpanel', 'download_right_search_module_tab_click', 'from=' + this.openFrom + ',tab=' + tabKeyMap[this.activeKey] ?? '');
    // 切换tab重置选中态
    this.selectIndex = -1;
    // Tab展示埋点
    if (this.activeKey === 'TabHistory') {
      XLStatNS.trackEventEx('download_leftpanel', 'website_history_tab_show');
    }

    // if (this.activeKey === 'TabFav') {
    //   this.showFavUpdateTips = false;
    // }
  }

  async clearConfirm(title: string): Promise<boolean> {
    return (this.$refs?.['confirm'] as ClearConfirmCtrl)?.clearConfirm(title);
  }

  onTabChange (key: string): void {
    this.activeKey = key;
  }

  eventAddressKeyDown: number = -1;
  eventOnAddressBarInputChanged: number = -1;
  eventHideDropDownWindow: number = -1;

  keyWord: string = '';
  configKeyword: string = '';

  searchWindowX: number = 0;
  searchWindowY: number = 0;
  searchWindowWidth: number = 0;

  // 当前选中的索引序号，每次调整窗口/窗口展示时归0；未全部展开时，需要跳跃
  selectIndex: number = -1;

  searchEngineUrl: string = 'https://www.baidu.com/s?tn=46054985_oem_dg&wd=$word$';

  isLogined: boolean = false;
  userid: string = '';
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  vipInfo: any = {};

  isInSafeSpace: boolean = null; // 是否在保险箱内
  openFrom: string = '';

  @Prop()
  isInMainRenderer: boolean;

  get panPanelText(): string {
    if (this.isInSafeSpace) {
      return '超级保险箱';
    }
    return '云盘';
  }

  getSearchUrl(word: string): string {
    return this.searchEngineUrl.replace('$word$', encodeURIComponent(word));
  }

  // 搜索
  searchKeyword(): void {
    const url = this.getSearchUrl(this.keyWord);
    clientModule.callServerFunction('OpenNewTab', url);
    clientModule.callServerFunction('AddSearchKeyword', { keyword: this.keyWord, url: url }).catch();
  }

  // 将taskname转换成带span样式的名称
  // formatedTitle(name: string): string {
  //   let result: string = name;
  //   const keys: string[] = this.keyWord.trim().split(' ');
  //   keys.forEach((key: string) => {
  //     if (key.length === 0) {
  //       return ;
  //     }
  //     key = key.replace(new RegExp('[\\(|\\)|\\[|\\]]', 'ig'), (key: string) => {
  //       return '\\' + key;
  //     });
  //     result = result.replace(new RegExp(key, 'ig'), (key: string) => { return '<span class="keyword">' + key + '</span>'; });
  //   });
  //   return result;
  // }

  hideDropDownWindow(force?: boolean): void {
    clientModule.detachServerEvent('AddressKeyDown', this.eventAddressKeyDown);
    clientModule.detachServerEvent('OnAddressBarInputChanged', this.eventOnAddressBarInputChanged);
    clientModule.detachServerEvent('OnHideDropDownWindow', this.eventHideDropDownWindow);
    window.removeEventListener('keydown', this.handleKeydownHotkey);
    clientModule.broadcastEvent('SelectAddressDropItem', this.keyWord, false, undefined);
    this.$emit('hideDropDownWindow', force);
  }

  resizeSearchWindow(reason?: string): void {
    // 在已强制关闭时不需要进行调整
    logger.information('resize reason <', reason, '>');

    this.$emit('resizeSearchWindow');
  }

  async handleSearch(key: string): Promise<void> {
    this.keyWord = key;
  }

  // 地址栏/搜索框收到按键事件, 传给此方法处理, 上下箭头时展示选中态, enter键时进行访问
  // selectIndex = -1表示未选中
  handleKeydownSelect(key: string, value: string): void {
    do {
      if (key === 'Enter') { // enter键
        if (this.keyWord === '' || (this.activeKey !== 'TabPan' && this.selectIndex === -1)) { // 直接访问
          clientModule.broadcastEvent('OnKeydownEnterNavigate', this.parentId);
          this.hideDropDownWindow();
          break;
        }
        // 各组件单独处理选中访问
        (this.$refs.tabPanel as any)?.handleKeydownEnter?.();
        break;
      }
      if (this.keyWord === '' && this.activeKey !== 'TabFav') {
        break;
      }
      if (key === 'ArrowDown') {
        // key: ArrowDown 只管往下, 然后允许组件重设selectIndex值
        this.selectIndex += 1;
        break;
      }
      if (key === 'ArrowUp') {
        // key: ArrowUp
        if (this.selectIndex < 0) {
          logger.information('key up reach top');
          break;
        }
        this.selectIndex -= 1;
        break;
      }
    } while (0);
  }

  handleKeydownHotkey(e: KeyboardEvent): void {
    do {
      logger.information('key down', e);
      this.handleKeydownSelect(e.key, undefined);
    } while (0);
  }

  beforeDestroy(): void {
    this.hideDropDownWindow(false);

    // if (this.favUpdateTipsTimerId !== undefined) {
    //   clearTimeout(this.favUpdateTipsTimerId);
    //   this.favUpdateTipsTimerId = undefined;
    // }
  }

  async mounted(): Promise<void> {
    if (this.currNav === NavViewID.Cloud) {
      this.activeKey = 'TabPan';
    } else {
      this.activeKey = 'TabCommonSearch';
    }

    window.addEventListener('keydown', this.handleKeydownHotkey);
    if (!this.isInMainRenderer) {
      this.eventAddressKeyDown = clientModule.attachServerEvent('AddressKeyDown', (context: unknown, param: ISearchWindowKeyDownOptions) => {
        if (this.parentId === param.parentId) {
          this.handleKeydownSelect(param.key, param.value);
        }
      });
    }

    // await clientModule.callServerFunction('WaitForConfigInitFinish');
    // const value: boolean = await clientModule.callServerFunction('GetConfigValue', 'ConfigFavorites', 'ShowFirstTips', true);
    // if (value) {
    //   // 避免闪现，在展示150ms之后，展示tips
    //   this.favUpdateTipsTimerId = setTimeout(async () => {
    //     this.favUpdateTipsTimerId = undefined;
    //     this.showFavUpdateTips = true;
    //     clientModule.callServerFunction('TrackEvent', 'download_leftpanel', 'new_collect_inform_pop_show');
    //     await clientModule.callServerFunction('SetConfigValue', 'ConfigFavorites', 'ShowFirstTips', false);
    //   }, 150) as any;
    // }
  }

  async created(): Promise<void> {
    // 隐藏垂直滚动条，避免改变窗口大小过程中闪现滚动条
    document.body.style.overflowY = 'hidden';
    [this.searchEngineUrl, this.openFrom] = await Promise.all([clientModule.callServerFunction(
      'GetRemoteGlobalConfigValue',
      'search',
      'search_engine_url',
      'https://www.baidu.com/s?tn=46054985_oem_dg&wd=$word$'
    ),
    clientModule.callServerFunction('GetSearchWindowOpenFrom')
    ]);
    this.handleSearch(this.textData);
    this.eventOnAddressBarInputChanged = clientModule.attachServerEvent('OnAddressBarInputChanged', (context: unknown, param: ISearchInputChangeOptions): void => {
      // keyWord: string, left: number, top: number, width: number, height: number
      do {
        if (this.parentId !== param.parentId) {
          break;
        }

        this.openFrom = 'clienttop_address';
        this.searchWindowX = param.left;
        this.searchWindowY = param.top;
        this.searchWindowWidth = param.width;
        this.handleSearch(param.value);
      } while (0);
    });

    this.eventHideDropDownWindow = clientModule.attachServerEvent('OnHideDropDownWindow', this.hideDropDownWindow);
    const currentNav: string = await clientModule.callServerFunction('GetCurrentNav');
    if (currentNav === 'pan-plugin-view') {
      const inSafeSpace: boolean[] = (await clientModule.callRemoteClientFunction('ThunderPanPluginWebview', 'IpcInSafeSpace'));
      logger.information('is In Safe Space: ', inSafeSpace);
      this.isInSafeSpace = inSafeSpace[0];
    } else {
      this.isInSafeSpace = false;
    }
    this.resizeSearchWindow('created'); // 避免闪动, 在渲染完成后再通知后台进程显示窗口

    this.isLogined = await clientModule.callServerFunction('IsLogined');
    this.userid = await clientModule.callServerFunction('GetUserID');
    this.vipInfo = await clientModule.callServerFunction('GetVipInfo');

    // 临时打开控制台
    // let searchWindow: any = await asyncRemoteCall.getCurrentWindow();
    // let webContents: any = searchWindow.webContents;
    // webContents.openDevTools();
  }
  // 新增方法end
}
</script>

<style src="../../../assets/css/xly-icon.css"></style>
<style src="../../../assets/css/xly-icon-type.css"></style>
<style src="../../../assets/css/xly-common.css"></style>
<style src="../../../assets/css/xly-var.css"></style>
<style src="../../../assets/css/xly-search-drop.css"></style>
