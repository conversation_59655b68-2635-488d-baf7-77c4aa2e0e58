<template>
  <div class="xly-sign-in-panel">
    <div class="xly-sign-in-panel__header">
      <span @click="$emit('back')">
        <i class="xly-icon-arrow-left"></i>
        返回
      </span>
    </div>
    <div class="xly-sign-in-panel__body">
      <div class="xly-sign-in__gift"></div>
      <h4>累计连续签到成功，获得超会天卡</h4>
      <p class="is-warn">7天内有效</p>

      <div class="xly-sign-in-panel__button">
        <td-button @click="handleUse">使用</td-button>
        <td-button secondary @click="$emit('openAwardList')">查看奖品</td-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
import { showMessageToast } from '@/common/ipc'
import { SIGN_IN_AWARD } from '@/common/reward-map'
import * as SignInApi from '@/api/sign-in'

@Component
export default class FinalAward extends Vue {
  @Prop() finalAwardList!: any[]

  isRequest: boolean = false

  async handleUse () {
    if (this.isRequest) return
    this.isRequest = true
    // 获取奖品列表
    const resp = await SignInApi.getAwardList()

    if (!resp.data) {
      showMessageToast({ message: '操作失败，请稍后重试', type: 'error' })
      this.isRequest = false
      return
    }
    // 当前奖品列表第一个为刚获取的奖品，40，41 代表超会天卡
    const award = resp.data.data.filter(award => [40, 41].includes(award.asset_id))[0]

    if (!award) {
      showMessageToast({ message: '操作失败，请稍后重试', type: 'error' })
      this.isRequest = false
      return
    }

    const res = await SignInApi.useAward({
      id: award.id,
      game_id: award.game_id,
      cost_asset_id: SIGN_IN_AWARD[award.asset_id].cost_asset_id,
      get_asset_id: SIGN_IN_AWARD[award.asset_id].get_asset_id,
    })

    if (res.data) {
      showMessageToast({ message: '已成功兑换', type: 'success' })
      this.$emit('back')
    } else {
      showMessageToast({ message: '操作失败，请稍后重试', type: 'error' })
    }
    this.isRequest = false
  }
}
</script>