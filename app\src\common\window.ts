/**
 * @description: 提供窗口、视图相关功能
 * @author:      <PERSON><PERSON><PERSON><PERSON>
 * @version:     1.0
 */
// TODO 这个文件实际上在browser和renderer里面都使用了，需要特别注意哪些接口应该在哪种类型得进程里面调用，容易出问题

import { BrowserWindow, ipcRenderer, screen, Point, Display } from 'electron';
import { ThunderChannelList } from '@/common/channel';
import { client as clientModule } from '@xunlei/node-net-ipc/dist/ipc-client';
import { CommonIPCBase } from '@xunlei/native-call/lib/ipc/ipc-base';
import { WindowMessage } from '@/common/window-define';
export namespace ThunderWindowNS {
  /** 窗口适配对话框大小 */
  export async function resizeToFitContent(width: number = 0, height: number = 0, beforeShow?: () => any): Promise<void> {
    const { asyncRemoteCall } = await import('@/common/renderer-process-call'); // tslint:disable-line
    const currentWindow: any = await asyncRemoteCall.getCurrentWindow();

    if (width > 0 && height > 0) {
      // await currentWindow.setSize(width, height);
      window.resizeTo(
        Math.round(width),
        Math.round(height));
    } else {
      let $dialog: HTMLElement = document.querySelector('.td-dialog');
      if ($dialog === null) {
        $dialog = document.querySelector('.msg-pop-message');
      }
      if ($dialog !== null) {
        // await currentWindow.setSize($dialog.offsetWidth, $dialog.offsetHeight);
        window.resizeTo(
          Math.round($dialog.offsetWidth),
          Math.round($dialog.offsetHeight));
      }
    }

    if (beforeShow && typeof beforeShow === 'function') {
      if (beforeShow instanceof Object.getPrototypeOf(async function(): Promise<void> {
        //
      }).constructor) {
        await beforeShow().catch();
      } else {
        beforeShow();
      }
    }

    window.requestIdleCallback(() => {
      // 这里在beforeShow居中，会有一个窗口从屏幕中间到主窗口居中的闪烁，延时50ms
      currentWindow.show();
    });
  }

  /**
   * @description: 滚动条的自适应，适用于列表组建，某个列表项不在可视视图区域，则调整到可视区域
   * 类似于scrollIntoViewIfNeed / scrollIntoView，
   * 但是任务列表用这两个接口有bug，所以重新实现
   * @param: childEl 子元素
   * @param: parentEl 滚动条所在的父元素
   * @param: defaultHeight 可能父元素不可见的时候高度为0，给一个默认的高度
   */
  export function autoAdaptScrollIntoView(childEl: Element, parentEl: Element, defaultHeight?: number): void {
    const curScrollTop: number = parentEl.scrollTop;
    const contentRect: ClientRect = parentEl.getBoundingClientRect();
    const elRect: ClientRect = childEl.getBoundingClientRect();
    let bottom: number = contentRect.bottom;
    // console.log('scroll', elRect, elRect.height, contentRect, curScrollTop, defaultHeight);
    // 有时候正在展开，也会出问题，所以需判断高度<defaultHeight，则需要用到defaultHeight
    if (defaultHeight !== undefined && typeof defaultHeight === 'number' && defaultHeight > 0) {
      // 私人空间切换过来可能 列表高度也还没刷新。。所以只能用defaultHeight
      bottom = contentRect.top + defaultHeight;
    }

    if (elRect.top < contentRect.top) {
      // 元素已经向上滚动,并超出了可视区域
      parentEl.scrollTop = curScrollTop - (contentRect.top - elRect.top);
    } else if (elRect.bottom > bottom) {
      // 元素已经下滚，并超出了可视区域
      parentEl.scrollTop = curScrollTop + (elRect.bottom - bottom);
    }
  }

  /**
   * @description 标准的调整窗口位置函数
   * @summary 仅限在已经load了页面的渲染进程使用
   */
  export async function fitWindowPosInParent(childWnd?: any, fatherWnd?: any): Promise<void> {
    if (!childWnd) {
      const { asyncRemoteCall } = await import('@/common/renderer-process-call'); // tslint:disable-line
      childWnd = await asyncRemoteCall.getCurrentWindow();
    }

    if (!fatherWnd) {
      fatherWnd = await childWnd.getParentWindow();
    }
    // 支持不传parentid, 不调整位置
    if (!fatherWnd) {
      return;
    }

    const childHandleBuf: Buffer = await childWnd.getNativeWindowHandle();
    const childHandle: number = childHandleBuf.readUIntLE(0, 4);
    const fatherHandleBuf: Buffer = await fatherWnd.getNativeWindowHandle();
    const fatherHandle: number = fatherHandleBuf.readUIntLE(0, 4);
    await clientModule.callRemoteClientFunction(CommonIPCBase.mainProcessContext, 'SetPosition', childHandle, fatherHandle);
  }

  export async function getWindowsInParentCenterPos(width: number, height: number, parentWindow?: any): Promise<number[]> {
    let pos: number[] = [];
    let size: number[] = [];
    let point: Point = undefined;
    let disp: Display = undefined;
    if (process.type === 'browser') {
      if (!parentWindow) {
        return [0, 0];
      }
      pos = parentWindow.getPosition();
      size = parentWindow.getSize();
      point = screen.getCursorScreenPoint();
      disp = screen.getDisplayNearestPoint(point);
    } else {
      const { asyncRemoteCall } = await import('@/common/renderer-process-call'); // tslint:disable-line
      if (!parentWindow) {
        parentWindow = await asyncRemoteCall.getCurrentWindow();
      }
      pos = await parentWindow.getPosition();
      size = await parentWindow.getSize();
      let screenObj: any = await asyncRemoteCall.getScreen();
      point = await screenObj.getCursorScreenPoint();
      disp = await screenObj.getDisplayNearestPoint(point);
    }

    const screenWidth: number = disp.size.width;
    const screenHeight: number = disp.size.height;
    let visibleWidth: number = size[0];
    let visibleHeight: number = size[1];

    if (pos[0] + visibleWidth > screenWidth) {
      visibleWidth = screenWidth - pos[0];
    }
    if (pos[1] + visibleHeight > screenHeight) {
      visibleHeight = screenHeight - pos[1];
    }

    let x: number = pos[0] + (visibleWidth - width) / 2;
    let y: number = pos[1] + (visibleHeight - height) / 2;
    if (x < 0) {
      x = 0;
    } else if (x > screenWidth - width) {
      x = screenWidth - width;
    }

    if (y < 0) {
      y = 0;
    } else if (y > screenHeight - height) {
      y = screenHeight - height;
    }

    const ret: number[] = [Math.round(x), Math.round(y)];
    return ret;
  }

  // 该接口只能在主后台进程调用
  export function centerWnd(selfWindow: BrowserWindow, parentWindow: BrowserWindow, thunderHelper: any): void {
    do {
      if (process.type !== 'browser') {
        break;
      }

      if (!selfWindow || !parentWindow) {
        break;
      }

      const selfHwnd: number = selfWindow.getNativeWindowHandle().readUIntLE(0, 4);
      if (!selfHwnd) {
        break;
      }
      const pos: number[] = parentWindow.getPosition();
      const size: number[] = parentWindow.getSize();
      const selfSize: number[] = selfWindow.getSize();
      const point: Point = screen.getCursorScreenPoint();
      const disp: Display = screen.getDisplayNearestPoint(point);
      const dpi: number = disp.scaleFactor;
      const screenWidth: number = disp.size.width;
      const screenHeight: number = disp.size.height;
      let visibleWidth: number = size[0];
      let visibleHeight: number = size[1];
      if (pos[0] + visibleWidth > screenWidth) {
        visibleWidth = screenWidth - pos[0];
      }
      if (pos[1] + visibleHeight > screenHeight) {
        visibleHeight = screenHeight - pos[1];
      }

      let x: number = pos[0] + (visibleWidth - selfSize[0]) / 2;
      let y: number = pos[1] + (visibleHeight - selfSize[1]) / 2;
      if (x < 0) {
        x = 0;
      } else if (x > screenWidth - selfSize[0]) {
        x = screenWidth - selfSize[0];
      }
      if (y < 0) {
        y = 0;
      } else if (y > screenHeight - selfSize[1]) {
        y = screenHeight - selfSize[1];
      }

      thunderHelper.setWindowPos(selfHwnd, 0, x * dpi, y * dpi, 0, 0, 5);
    } while (0);
  }

  // TODO centerWnd是传入了thunderhelper，这里也先传入吧，centerWnd只是把窗口放在主显里面，
  // centerWndEx会基于thunder所在得屏幕进行显示,这个接口只能在browser里面使用
  export function centerWndEx(selfWindow: BrowserWindow, parentWindow: BrowserWindow, thunderHelper: any): void {
    do {
      if (!selfWindow || !parentWindow) {
        break;
      }

      let selfHwnd: number = selfWindow.getNativeWindowHandle().readUIntLE(0, 4);
      let parentHwnd: number = parentWindow.getNativeWindowHandle().readUIntLE(0, 4);

      let parentRect: any = thunderHelper.getWindowRect(parentHwnd);
      let pos: number[] = parentWindow.getPosition();
      let selfSize: number[] = selfWindow.getSize();
      // 二级面板显示在父窗口左上角所在的屏幕
      let p1: Point = { x: pos[0], y: pos[1] };
      const disp: Display = screen.getDisplayNearestPoint(p1);

      // 转换成屏幕上真实要显示的大小
      selfSize[0] = selfSize[0] * disp.scaleFactor;
      selfSize[1] = selfSize[1] * disp.scaleFactor;

      const dpi: number = disp.scaleFactor;
      const screenWidth: number = disp.workArea.width * dpi;
      const screenHeight: number = disp.workArea.height * dpi;
      let p3 = screen.dipToScreenPoint({ x: disp.workArea.x, y: disp.workArea.y });

      // 父窗口左边超出了屏幕，那么只用右边部分进行居中
      if (parentRect.x < p3.x) {
        parentRect.width = parentRect.width - (p3.x - parentRect.x);
        parentRect.x = p3.x;
      }
      if (parentRect.y < p3.y) {
        parentRect.height = parentRect.height - (p3.y - parentRect.y);
        parentRect.y = p3.y;
      }

      // 计算父窗口在屏幕内的部分可显示的区域
      let visibleWidth: number = parentRect.width;
      let visibleHeight: number = parentRect.height;
      if (parentRect.x + visibleWidth > p3.x + screenWidth) {
        visibleWidth = p3.x + screenWidth - parentRect.x;
      }
      if (parentRect.y + visibleHeight > p3.y + screenHeight) {
        visibleHeight = p3.y + screenHeight - parentRect.y;
      }

      // 二级面板的预想位置
      let x: number = parentRect.x + (visibleWidth - selfSize[0]) / 2;
      let y: number = parentRect.y + (visibleHeight - selfSize[1]) / 2;

      // 矫正位置，二级面板不能超出屏幕,优先保证面板的左上角在屏幕内
      if (x < p3.x) {
        x = p3.x;
      } else if (x > p3.x + screenWidth - selfSize[0]) {
        x = p3.x + screenWidth - selfSize[0];
      }
      if (y < p3.y) {
        y = p3.y;
      } else if (y > p3.y + screenHeight - selfSize[1]) {
        y = p3.y + screenHeight - selfSize[1];
      }

      thunderHelper.setWindowPos(selfHwnd, 0, x , y , 0, 0, 5);
    } while (0);
  }

  /**
   * @description 把窗口拉到最前
   * @summary 使用发IPC事件通知主后台进程调用的方式来实现
   * @param hwnd 窗口句柄，如果不指定则默认为当前调用进程的窗口句柄
   */
  export async function bringWindowToTop(hwnd?: number): Promise<any> {
    if (process.type !== 'browser') {
      if (!hwnd) {
        const { asyncRemoteCall } = await import('@/common/renderer-process-call'); // tslint:disable-line
        let mainWindow: any = await asyncRemoteCall.getCurrentWindow();
        let hwndBuffer: any = await mainWindow.getNativeWindowHandle();
        hwnd = hwndBuffer.readUIntLE(0, 4);
      }
      ipcRenderer.send(ThunderChannelList.channelMRBringWindowToTop, hwnd);
    }
  }

  // WM_PARENTNOTIFY消息
  export function hookParentNotifyClick(win: any, callback: () => void): void {
    do {
      if (!win) {
        break;
      }

      if (!callback) {
        break;
      }

      win.hookWindowMessage(528, (wparamBuf: Buffer, lparam: any) => {
        do {
          if (wparamBuf) {
            // https://docs.microsoft.com/en-us/previous-versions/ms632638(v=vs.85)
            const wparam: number = wparamBuf.readUIntLE(0, 4);
            const loword: number = wparam & 0x0000ffff;
            if (loword === WindowMessage.WM_CREATE || loword === WindowMessage.WM_DESTROY) {
              break;
            }
          }

          callback();
        } while (0);
      });
    } while (0);
  }
}
