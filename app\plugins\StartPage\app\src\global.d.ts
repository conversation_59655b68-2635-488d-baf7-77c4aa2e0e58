declare var __rootDir: string;
declare var __profilesDir: string;
declare var __xmpRootDir: string;
declare var __xmpRootBinDir: string;
declare var __xmpProfilesDir: string;
declare var __non_webpack_require__: NodeRequire;

// 用于JS与本地业务接口互掉
// 为兼容tbc组件的调用方式，此接口提供的方法名需与tbc组件里提供的接口名保持一致
// 因此忽略函数首字母要小写的规则
interface INativeCall {
  // 调用本地业务接口
  CallNativeFunction(funcName: string, ...restOfArgs: any[]): void;

  // 监听本地事件
  AttachNativeEvent(eventName: string, callback: (...args: any[]) => void): number;

  // 取消监听本地事件
  DetachNativeEvent(eventName: string, id: number): void;

  // 检查本地业务接口是否存在
  CheckNativeFunction(funcName: string, callback: (errorCode: number) => void): void;

  // 注册JS函数以供本地调用
  RegisterJSFunction(funcName: string, jsFunc: (...args: any[]) => any): number;
}

declare namespace XDASNS {
  class PerformanceMonitorReport {
    public initPerformanceMonitor(bussinessName: string, config: object): void;

    public uninitPerformanceMonitor(): void;
  }

  export class XDAS {
    public GetPerformanceMonitorReport(): PerformanceMonitorReport;
  }
}

declare class PluginUpdater {
  public install(version: string, src: string): Promise<void>;

  public active(): Promise<void>;

  public getCurFilePath(): Promise<string>;
}

interface Window {
  __rootDir: string;
  __profilesDir: string;
  __xmpRootDir: string;
  __xmpRootBinDir: string; // 用于进入bin目录
  __xmpProfilesDir: string;
  native: INativeCall;

  getPluginUpdater: (dir: string) => PluginUpdater;
  xlDesktopApplicationSolution: XDASNS.XDAS;
  requestIdleCallback(callback: () => void): void;
}


declare namespace NodeJS {
  export interface Global {
    xlPluginEvent: any;
    dklib: any;
    nodeFileCache: object;
    __rootDir: string;
    __profilesDir: string;
    AsyncGetNativeCallModuleObj: any;
    xlNativeCallModule: any;
    xlDesktopApplicationSolution: XDASNS.XDAS;
    __xdasObjectLiftMonitor: any;
    __xdasPluginConfig: any;
  }
}

declare var xlRequire: NodeRequire;
// 给 util.promisify 库的 promisify 绑定定义一个别名类型
type Promisify = (...args: any[]) => Promise<any>;
