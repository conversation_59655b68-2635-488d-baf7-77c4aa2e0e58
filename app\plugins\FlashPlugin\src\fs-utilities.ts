import fs = require('fs');
import util = require('util');
import { Logger } from './logger';

const logger: Logger = Logger.getLogger('fs-utilities');
const promisify: any = util.promisify;

export async function readFileAW(filePath: string): Promise<Buffer> {
  let ret: Buffer = null;
  if (filePath !== undefined) {
    const readFile: Promisify = promisify(fs.readFile);
    try {
      ret = await readFile(filePath);
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function writeFileAW(filePath: string, data: any): Promise<boolean> {
  let ret: boolean = false;
  if (filePath !== undefined && data !== null) {
    const writeFile: Promisify = promisify(fs.writeFile);
    try {
      await writeFile(filePath, data);
      ret = true;
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function existsAW(filePath: string): Promise<boolean> {
  let ret: boolean = false;
  if (filePath !== undefined) {
    const exists: Promisify = promisify(fs.access);
    try {
      await exists(filePath);
      ret = true;
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function unlinkAW(filePath: string): Promise<boolean> {
  let ret: boolean = false;
  if (filePath !== undefined) {
    const unlink: Promisify = promisify(fs.unlink);
    try {
      await unlink(filePath);
      ret = true;
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function renameAW(oldPath: string, newPath: string): Promise<void> {
  if (oldPath !== undefined && newPath !== undefined) {
    const rename: Promisify = promisify(fs.rename);
    try {
      await rename(oldPath, newPath);
    } catch (err) {
      logger.warning(err);
    }
  }
}

export async function copyFileAW(src: string, dest: string): Promise<boolean> {
  let result: Promise<boolean>;
  if (src.toLowerCase() !== dest.toLowerCase() && (await existsAW(src))) {
    let readStream: fs.ReadStream = fs.createReadStream(src);
    let writeStream: fs.WriteStream = fs.createWriteStream(dest);
    result = new Promise<boolean>(
      (resolve: (value?: boolean | PromiseLike<boolean>) => void): void => {
        readStream.pipe(writeStream).on('finish', () => {
          resolve(true);
        });
      }
    );
  } else {
    result = new Promise<boolean>(
      (resolve: (value?: boolean | PromiseLike<boolean>) => void): void => {
        resolve(false);
      }
    );
  }

  return result;
}

export async function lstatAW(filePath: string): Promise<fs.Stats> {
  let ret: fs.Stats = null;
  if (filePath !== undefined) {
    const lstat: Promisify = promisify(fs.lstat);
    try {
      ret = await lstat(filePath);
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}

export async function mkdirAW(dirName: string): Promise<boolean> {
  let ret: boolean = false;
  if (dirName !== undefined) {
    const mkdir: Promisify = promisify(fs.mkdir);
    try {
      await mkdir(dirName);
      ret = true;
    } catch (err) {
      logger.warning(err);
    }
  }
  return ret;
}
